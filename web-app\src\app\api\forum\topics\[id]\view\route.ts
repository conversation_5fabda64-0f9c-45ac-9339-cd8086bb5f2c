import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    const topicId = params.id

    if (!topicId) {
      return NextResponse.json({ error: 'Topic ID is required' }, { status: 400 })
    }

    // First get current view count, then increment
    const { data: topicData, error: fetchError } = await supabase
      .from('forum_topics')
      .select('view_count')
      .eq('id', topicId)
      .single()

    if (fetchError) {
      console.error('Error fetching topic:', fetchError)
      return NextResponse.json({ error: 'Topic not found' }, { status: 404 })
    }

    // Increment view count
    const { error } = await supabase
      .from('forum_topics')
      .update({
        view_count: (topicData.view_count || 0) + 1,
      })
      .eq('id', topicId)

    if (error) {
      console.error('Error incrementing view count:', error)
      return NextResponse.json({ error: 'Failed to increment view count' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in view count endpoint:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
