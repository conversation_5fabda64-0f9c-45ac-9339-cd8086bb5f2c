import React from "react";
import { View, ActivityIndicator, StyleSheet } from "react-native";
import { Tabs } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useProtectedRoute } from "@/hooks/useProtectedRoute";
import { Colors } from "@/constants/Colors";

export default function TabLayout() {
  const { user, loading } = useProtectedRoute();

  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color={Colors.light.tint} />
      </View>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <Tabs
      screenOptions={({ route }) => ({
        tabBarActiveTintColor: Colors.light.tint,
        tabBarInactiveTintColor: "gray",
        tabBarStyle: styles.tabBar,
        tabBarLabelStyle: styles.tabBarLabel,
        headerStyle: {
          backgroundColor: Colors.light.tint,
        },
        headerTintColor: "#fff",
        headerTitleStyle: {
          fontWeight: "bold",
        },
        tabBarIcon: ({ color, size }) => {
          let iconName: React.ComponentProps<typeof Ionicons>["name"] =
            "alert-circle-outline";

          if (route.name === "index") {
            iconName = "speedometer-outline";
          } else if (route.name === "browse") {
            iconName = "search-outline";
          } else if (route.name === "vehicles") {
            iconName = "car-outline";
          } else if (route.name === "charging") {
            iconName = "battery-charging-outline";
          } else if (route.name === "trips") {
            iconName = "map-outline";
          } else if (route.name === "profile") {
            iconName = "person-outline";
          } else if (route.name === "explore") {
            iconName = "compass-outline";
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
      })}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "Dashboard",
        }}
      />
      <Tabs.Screen
        name="browse"
        options={{
          title: "Browse EVs",
          headerShown: false,
        }}
      />
      <Tabs.Screen
        name="vehicles"
        options={{
          title: "My Vehicles",
        }}
      />
      <Tabs.Screen
        name="charging"
        options={{
          title: "Charging",
        }}
      />
      <Tabs.Screen
        name="trips"
        options={{
          title: "Trips",
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: "Profile",
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          title: "Explore",
        }}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  loaderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f0fdf4",
  },
  tabBar: {
    backgroundColor: "#ffffff",
    borderTopColor: "#e5e7eb",
    borderTopWidth: 1,
    height: 60,
    paddingBottom: 5,
    paddingTop: 5,
  },
  tabBarLabel: {
    fontSize: 12,
    fontWeight: "500",
  },
});
