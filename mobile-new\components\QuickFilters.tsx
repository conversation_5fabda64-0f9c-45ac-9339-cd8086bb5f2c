import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";

interface QuickFilter {
  id: string;
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
  filters: Record<string, any>;
  description: string;
}

interface QuickFiltersProps {
  onFilterSelect: (filters: Record<string, any>) => void;
  activeFilters: Record<string, any>;
}

const quickFilters: QuickFilter[] = [
  {
    id: "affordable",
    label: "Under $40k",
    icon: "cash",
    filters: { priceMax: "4000000" },
    description: "Budget-friendly EVs",
  },
  {
    id: "long-range",
    label: "300+ Miles",
    icon: "speedometer",
    filters: { rangeMin: "300" },
    description: "Long-range vehicles",
  },
  {
    id: "fast-charging",
    label: "Fast Charging",
    icon: "flash",
    filters: { chargingSpeedMin: "150" },
    description: "Quick charging capability",
  },
  {
    id: "family",
    label: "Family SUVs",
    icon: "car",
    filters: { bodyType: "suv", seatingMin: "5" },
    description: "Spacious family vehicles",
  },
  {
    id: "luxury",
    label: "Luxury",
    icon: "diamond",
    filters: { priceMin: "7000000" },
    description: "Premium vehicles",
  },
];

export function QuickFilters({
  onFilterSelect,
  activeFilters,
}: QuickFiltersProps) {
  const isFilterActive = (filter: QuickFilter) => {
    return Object.entries(filter.filters).some(([key, value]) => {
      return activeFilters[key] === value;
    });
  };

  const handleFilterPress = (filter: QuickFilter) => {
    if (isFilterActive(filter)) {
      const clearedFilters = Object.keys(filter.filters).reduce((acc, key) => {
        acc[key] = undefined;
        return acc;
      }, {} as Record<string, any>);
      onFilterSelect(clearedFilters);
    } else {
      onFilterSelect(filter.filters);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
      >
        {quickFilters.map((filter) => {
          const isActive = isFilterActive(filter);
          return (
            <TouchableOpacity
              key={filter.id}
              onPress={() => handleFilterPress(filter)}
              style={[
                styles.filterButton,
                isActive
                  ? styles.filterButtonActive
                  : styles.filterButtonInactive,
              ]}
            >
              <Ionicons
                name={filter.icon}
                size={16}
                color={isActive ? "white" : "#6b7280"}
              />
              <Text
                style={[
                  styles.filterText,
                  isActive
                    ? styles.filterTextActive
                    : styles.filterTextInactive,
                ]}
              >
                {filter.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  scrollContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  filterButtonActive: {
    backgroundColor: Colors.light.tint,
    borderColor: Colors.light.tint,
  },
  filterButtonInactive: {
    backgroundColor: "white",
    borderColor: "#d1d5db",
  },
  filterText: {
    marginLeft: 8,
    fontWeight: "500",
  },
  filterTextActive: {
    color: "white",
  },
  filterTextInactive: {
    color: "#374151",
  },
});
