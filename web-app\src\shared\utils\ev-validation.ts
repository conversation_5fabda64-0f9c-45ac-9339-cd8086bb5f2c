import type {
  EVPreferencesFormData,
  ComparisonFormData,
  FavoriteFormData,
  EVBodyType,
  UseCase,
  PriorityWeights
} from '../types'
import { EV_BODY_TYPES, USE_CASES, PRIORITY_FACTORS } from '../types'

// ===== VALIDATION UTILITIES =====

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

/**
 * Validate EV preferences form data
 */
export function validateEVPreferences(data: EVPreferencesFormData): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Budget validation
  if (data.budget_min !== undefined && data.budget_max !== undefined) {
    if (data.budget_min < 0) {
      errors.push('Minimum budget cannot be negative')
    }
    if (data.budget_max < 0) {
      errors.push('Maximum budget cannot be negative')
    }
    if (data.budget_min && data.budget_max && data.budget_min > data.budget_max) {
      errors.push('Minimum budget cannot be greater than maximum budget')
    }
    if (data.budget_max && data.budget_max < 1000000) { // $10,000
      warnings.push('Budget may be too low for most new EVs')
    }
  }

  // Range requirement validation
  if (data.range_requirement_miles !== undefined) {
    if (data.range_requirement_miles !== null && data.range_requirement_miles < 0) {
      errors.push('Range requirement cannot be negative')
    }
    if (data.range_requirement_miles && data.range_requirement_miles > 500) {
      warnings.push('Range requirement is very high - few EVs exceed 500 miles')
    }
    if (data.range_requirement_miles && data.range_requirement_miles < 100) {
      warnings.push('Range requirement is quite low - most EVs exceed 100 miles')
    }
  }

  // Body type preferences validation
  if (data.body_type_preferences) {
    const invalidBodyTypes = data.body_type_preferences.filter(
      type => !EV_BODY_TYPES.includes(type as EVBodyType)
    )
    if (invalidBodyTypes.length > 0) {
      errors.push(`Invalid body types: ${invalidBodyTypes.join(', ')}`)
    }
  }

  // Use case validation
  if (data.use_case && !USE_CASES.includes(data.use_case as UseCase)) {
    errors.push('Invalid use case')
  }

  // Daily driving miles validation
  if (data.daily_driving_miles !== undefined) {
    if (data.daily_driving_miles !== null && data.daily_driving_miles < 0) {
      errors.push('Daily driving miles cannot be negative')
    }
    if (data.daily_driving_miles && data.daily_driving_miles > 300) {
      warnings.push('Daily driving miles is very high')
    }
  }

  // Priority weights validation
  if (data.priority_weights) {
    const weightErrors = validatePriorityWeights(data.priority_weights)
    errors.push(...weightErrors)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate comparison form data
 */
export function validateComparison(data: ComparisonFormData): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // EV model IDs validation
  if (!data.ev_model_ids || data.ev_model_ids.length === 0) {
    errors.push('At least one EV model is required for comparison')
  } else {
    if (data.ev_model_ids.length > 4) {
      errors.push('Maximum 4 EV models can be compared')
    }
    if (data.ev_model_ids.length === 1) {
      warnings.push('Comparison works best with 2 or more models')
    }

    // Check for duplicate IDs
    const uniqueIds = new Set(data.ev_model_ids)
    if (uniqueIds.size !== data.ev_model_ids.length) {
      errors.push('Duplicate EV models in comparison')
    }

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    const invalidIds = data.ev_model_ids.filter(id => !uuidRegex.test(id))
    if (invalidIds.length > 0) {
      errors.push('Invalid EV model ID format')
    }
  }

  // Name validation
  if (data.name && data.name.length > 100) {
    errors.push('Comparison name cannot exceed 100 characters')
  }

  // Notes validation
  if (data.comparison_notes && data.comparison_notes.length > 1000) {
    errors.push('Comparison notes cannot exceed 1000 characters')
  }

  // User priorities validation
  if (data.user_priorities) {
    const priorityErrors = validateUserPriorities(data.user_priorities)
    errors.push(...priorityErrors)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate favorite form data
 */
export function validateFavorite(data: FavoriteFormData): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // EV model ID validation
  if (!data.ev_model_id) {
    errors.push('EV model ID is required')
  } else {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(data.ev_model_id)) {
      errors.push('Invalid EV model ID format')
    }
  }

  // Notes validation
  if (data.notes && data.notes.length > 500) {
    errors.push('Notes cannot exceed 500 characters')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate priority weights
 */
function validatePriorityWeights(weights: PriorityWeights): string[] {
  const errors: string[] = []

  // Check if all weights are valid numbers
  Object.entries(weights).forEach(([factor, weight]) => {
    if (typeof weight !== 'number' || isNaN(weight)) {
      errors.push(`Invalid weight for ${factor}`)
      return
    }
    
    if (weight < 0 || weight > 100) {
      errors.push(`Weight for ${factor} must be between 0 and 100`)
    }
  })

  // Check if weights sum to approximately 100
  const totalWeight = Object.values(weights).reduce((sum, weight) => sum + (weight || 0), 0)
  if (Math.abs(totalWeight - 100) > 5) {
    errors.push('Priority weights should sum to approximately 100')
  }

  // Check for valid priority factors
  const validFactors = PRIORITY_FACTORS
  const invalidFactors = Object.keys(weights).filter(
    factor => !validFactors.includes(factor as any)
  )
  if (invalidFactors.length > 0) {
    errors.push(`Invalid priority factors: ${invalidFactors.join(', ')}`)
  }

  return errors
}

/**
 * Validate user priorities (similar to priority weights but more flexible)
 */
function validateUserPriorities(priorities: Record<string, any>): string[] {
  const errors: string[] = []

  // Check for valid structure
  if (typeof priorities !== 'object' || priorities === null) {
    errors.push('User priorities must be an object')
    return errors
  }

  // Validate individual priority values
  Object.entries(priorities).forEach(([key, value]) => {
    if (key.endsWith('_weight')) {
      if (typeof value !== 'number' || isNaN(value)) {
        errors.push(`Invalid weight value for ${key}`)
      } else if (value < 0 || value > 100) {
        errors.push(`Weight for ${key} must be between 0 and 100`)
      }
    }
  })

  return errors
}

/**
 * Validate search query
 */
export function validateSearchQuery(query: string): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (!query || query.trim().length === 0) {
    errors.push('Search query cannot be empty')
  } else {
    if (query.trim().length < 2) {
      errors.push('Search query must be at least 2 characters long')
    }
    if (query.length > 100) {
      errors.push('Search query cannot exceed 100 characters')
    }
    
    // Check for potentially problematic characters
    const problematicChars = /[<>{}[\]\\]/
    if (problematicChars.test(query)) {
      warnings.push('Search query contains special characters that may affect results')
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate price range
 */
export function validatePriceRange(min?: number, max?: number): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (min !== undefined && min < 0) {
    errors.push('Minimum price cannot be negative')
  }

  if (max !== undefined && max < 0) {
    errors.push('Maximum price cannot be negative')
  }

  if (min !== undefined && max !== undefined && min > max) {
    errors.push('Minimum price cannot be greater than maximum price')
  }

  if (max !== undefined && max < 1000000) { // $10,000
    warnings.push('Maximum price may be too low for most new EVs')
  }

  if (min !== undefined && min > 20000000) { // $200,000
    warnings.push('Minimum price is very high - few EVs exceed $200,000')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate range requirement
 */
export function validateRangeRequirement(range?: number): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (range !== undefined) {
    if (range < 0) {
      errors.push('Range requirement cannot be negative')
    }

    if (range > 600) {
      warnings.push('Range requirement is very high - few EVs exceed 600 miles')
    }

    if (range < 50) {
      warnings.push('Range requirement is very low - most EVs exceed 50 miles')
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Sanitize user input to prevent XSS and other issues
 */
export function sanitizeInput(input: string): string {
  if (!input) return ''
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 1000) // Limit length
}

/**
 * Validate and sanitize form data
 */
export function sanitizeFormData<T extends Record<string, any>>(data: T): T {
  const sanitized = { ...data }

  Object.keys(sanitized).forEach(key => {
    const value = sanitized[key]
    
    if (typeof value === 'string') {
      sanitized[key] = sanitizeInput(value) as T[Extract<keyof T, string>]
    } else if (Array.isArray(value)) {
      sanitized[key] = value.map(item => 
        typeof item === 'string' ? sanitizeInput(item) : item
      ) as T[Extract<keyof T, string>]
    }
  })

  return sanitized
}
