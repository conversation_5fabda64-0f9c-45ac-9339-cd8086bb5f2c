'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ComparisonButton } from '@/components/comparison/ComparisonButton'
import { Heart, Plus, Zap, Battery, Clock, Star } from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  formatPrice,
  formatRange,
  formatChargingTime,
  formatAcceleration,
} from '@/utils/ev-buyer-guide'
import { PLACEHOLDER_IMAGES } from '@/constants/ev-buyer-guide'
import type { EVModel } from '../../../types'

interface EVModelCardProps {
  model: EVModel
  viewMode: 'grid' | 'list'
  onAddToFavorites?: (modelId: string) => void
  isFavorite?: boolean
}

export function EVModelCard({
  model,
  viewMode,
  onAddToFavorites,
  isFavorite = false,
}: EVModelCardProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  const primaryImage = model.images?.[0] || PLACEHOLDER_IMAGES.ev_model
  const manufacturerLogo = model.ev_manufacturers?.logo_url || PLACEHOLDER_IMAGES.manufacturer_logo

  const handleAddToFavorites = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onAddToFavorites?.(model.id)
  }

  const modelUrl = `/ev-models/${model.id}`

  if (viewMode === 'list') {
    return (
      <Card className="overflow-hidden transition-shadow hover:shadow-lg">
        <Link href={modelUrl}>
          <div className="flex">
            {/* Image */}
            <div className="relative h-32 w-48 shrink-0">
              <Image
                src={imageError ? PLACEHOLDER_IMAGES.ev_model : primaryImage}
                alt={`${model.make} ${model.model}`}
                fill
                className="object-cover"
                onError={() => setImageError(true)}
                onLoad={() => setImageLoading(false)}
              />
              {imageLoading && (
                <div className="absolute inset-0 animate-pulse bg-gray-200 dark:bg-gray-700" />
              )}

              {/* Badges */}
              <div className="absolute left-2 top-2 flex flex-col gap-1">
                {model.is_featured && (
                  <Badge className="bg-electric-600 text-white">
                    <Star className="mr-1 h-3 w-3" />
                    Featured
                  </Badge>
                )}
                {model.best_value && <Badge variant="secondary">Best Value</Badge>}
                {model.editor_choice && (
                  <Badge className="bg-amber-600 text-white">Editor's Choice</Badge>
                )}
              </div>
            </div>

            {/* Content */}
            <div className="flex flex-1 flex-col justify-between p-6">
              <div>
                {/* Header */}
                <div className="mb-3 flex items-start justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      {manufacturerLogo && (
                        <Image
                          src={manufacturerLogo}
                          alt={model.make}
                          width={24}
                          height={24}
                          className="rounded"
                        />
                      )}
                      <span className="text-sm text-gray-600 dark:text-gray-400">{model.make}</span>
                    </div>
                    <h3 className="text-xl font-bold">
                      {model.model} {model.trim && `(${model.trim})`}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {model.year} • {model.body_type}
                    </p>
                  </div>

                  <div className="text-right">
                    <div className="text-2xl font-bold text-electric-600">
                      {formatPrice(model.price_msrp)}
                    </div>
                    {model.price_base && model.price_base !== model.price_msrp && (
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        From {formatPrice(model.price_base)}
                      </div>
                    )}
                  </div>
                </div>

                {/* Key Specs */}
                <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                  <div className="flex items-center gap-2">
                    <Battery className="h-4 w-4 text-electric-600" />
                    <div>
                      <div className="text-sm font-medium">
                        {formatRange(model.range_epa_miles)}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">EPA Range</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-electric-600" />
                    <div>
                      <div className="text-sm font-medium">
                        {model.charging_speed_dc_kw ? `${model.charging_speed_dc_kw}kW` : 'N/A'}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">DC Charging</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-electric-600" />
                    <div>
                      <div className="text-sm font-medium">
                        {formatChargingTime(model.charging_time_10_80_minutes)}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">10-80%</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 text-electric-600">⚡</div>
                    <div>
                      <div className="text-sm font-medium">
                        {formatAcceleration(model.acceleration_0_60_mph)}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">0-60 mph</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="mt-4 flex items-center justify-between">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAddToFavorites}
                    className={cn(
                      'transition-colors',
                      isFavorite &&
                        'bg-red-50 text-red-600 hover:bg-red-100 dark:bg-red-950 dark:text-red-400'
                    )}
                  >
                    <Heart className={cn('h-4 w-4', isFavorite && 'fill-current')} />
                  </Button>

                  <ComparisonButton model={model} variant="outline" size="sm" />
                </div>

                <Button asChild>
                  <Link href={modelUrl}>View Details</Link>
                </Button>
              </div>
            </div>
          </div>
        </Link>
      </Card>
    )
  }

  // Grid view
  return (
    <Card className="group overflow-hidden transition-all hover:shadow-lg hover:shadow-electric-600/10">
      <Link href={modelUrl}>
        {/* Image */}
        <div className="relative aspect-video overflow-hidden">
          <Image
            src={imageError ? PLACEHOLDER_IMAGES.ev_model : primaryImage}
            alt={`${model.make} ${model.model}`}
            fill
            className="object-cover transition-transform group-hover:scale-105"
            onError={() => setImageError(true)}
            onLoad={() => setImageLoading(false)}
          />
          {imageLoading && (
            <div className="absolute inset-0 animate-pulse bg-gray-200 dark:bg-gray-700" />
          )}

          {/* Badges */}
          <div className="absolute left-3 top-3 flex flex-col gap-1">
            {model.is_featured && (
              <Badge className="bg-electric-600 text-white">
                <Star className="mr-1 h-3 w-3" />
                Featured
              </Badge>
            )}
            {model.best_value && <Badge variant="secondary">Best Value</Badge>}
            {model.editor_choice && (
              <Badge className="bg-amber-600 text-white">Editor's Choice</Badge>
            )}
          </div>

          {/* Action Buttons */}
          <div className="absolute right-3 top-3 flex flex-col gap-2 opacity-0 transition-opacity group-hover:opacity-100">
            <Button
              variant="outline"
              size="icon"
              onClick={handleAddToFavorites}
              className={cn(
                'h-8 w-8 bg-white/90 backdrop-blur-sm hover:bg-white',
                isFavorite && 'bg-red-50 text-red-600 hover:bg-red-100'
              )}
            >
              <Heart className={cn('h-4 w-4', isFavorite && 'fill-current')} />
            </Button>

            <ComparisonButton
              model={model}
              variant="outline"
              size="sm"
              showText={false}
              className="h-8 w-8 bg-white/90 backdrop-blur-sm hover:bg-white"
            />
          </div>
        </div>

        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2">
                {manufacturerLogo && (
                  <Image
                    src={manufacturerLogo}
                    alt={model.make}
                    width={20}
                    height={20}
                    className="rounded"
                  />
                )}
                <span className="text-sm text-gray-600 dark:text-gray-400">{model.make}</span>
              </div>
              <h3 className="font-bold leading-tight">{model.model}</h3>
              {model.trim && (
                <p className="text-sm text-gray-600 dark:text-gray-400">{model.trim}</p>
              )}
            </div>

            <div className="text-right">
              <div className="text-lg font-bold text-electric-600">
                {formatPrice(model.price_msrp)}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">{model.year}</div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {/* Key Specs */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Range</span>
              <span className="font-medium">{formatRange(model.range_epa_miles)}</span>
            </div>

            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Charging</span>
              <span className="font-medium">
                {formatChargingTime(model.charging_time_10_80_minutes)}
              </span>
            </div>

            {model.acceleration_0_60_mph && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">0-60 mph</span>
                <span className="font-medium">
                  {formatAcceleration(model.acceleration_0_60_mph)}
                </span>
              </div>
            )}
          </div>

          {/* Action Button */}
          <Button className="mt-4 w-full" asChild>
            <Link href={modelUrl}>View Details</Link>
          </Button>
        </CardContent>
      </Link>
    </Card>
  )
}
