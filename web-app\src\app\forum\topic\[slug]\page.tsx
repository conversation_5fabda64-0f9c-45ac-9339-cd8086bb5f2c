'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRout<PERSON> } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  MessageSquare,
  Heart,
  Bookmark,
  Share2,
  Flag,
  MoreHorizontal,
  Pin,
  Lock,
  Edit,
  Reply,
  ChevronUp,
  ChevronDown,
  Clock,
  User,
} from 'lucide-react'
import Link from 'next/link'

interface TopicPost {
  id: string
  content: string
  content_type: 'text' | 'markdown'
  author: {
    id: string
    full_name: string
    avatar_url?: string
  }
  created_at: string
  updated_at: string
  like_count: number
  is_edited: boolean
  parent_post_id?: string
  parent_post?: {
    id: string
    content: string
    author: {
      id: string
      full_name: string
    }
  }
}

interface TopicDetail {
  id: string
  title: string
  slug: string
  content: string
  content_type: 'text' | 'markdown'
  author: {
    id: string
    full_name: string
    avatar_url?: string
  }
  category: {
    id: string
    name: string
    slug: string
    color?: string
    icon?: string
  }
  created_at: string
  updated_at: string
  view_count: number
  post_count: number
  like_count: number
  is_pinned: boolean
  is_locked: boolean
  is_approved: boolean
  tags?: string[]
  linked_ev_model?: {
    id: string
    name: string
    make: string
  }
  linked_manufacturer?: {
    id: string
    name: string
  }
}

interface TopicDetailData {
  topic: TopicDetail
  posts: TopicPost[]
}

export default function TopicDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const [data, setData] = useState<TopicDetailData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState('')
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [submittingReply, setSubmittingReply] = useState(false)

  const slug = params.slug as string

  useEffect(() => {
    fetchTopicDetail()
  }, [slug])

  const fetchTopicDetail = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch topic details
      const topicResponse = await fetch(`/api/forum/topics?slug=${slug}`)
      const topicData = await topicResponse.json()

      if (!topicResponse.ok) {
        throw new Error(topicData.error || 'Failed to fetch topic')
      }

      if (!topicData.data || topicData.data.length === 0) {
        throw new Error('Topic not found')
      }

      const topic = topicData.data[0]

      // Fetch posts for this topic
      const postsResponse = await fetch(
        `/api/forum/posts?topic_id=${topic.id}&limit=50&sort=oldest`
      )
      const postsData = await postsResponse.json()

      if (!postsResponse.ok) {
        throw new Error(postsData.error || 'Failed to fetch posts')
      }

      setData({
        topic,
        posts: postsData.data || [],
      })

      // Increment view count (fire and forget)
      if (topic.id) {
        fetch(`/api/forum/topics/${topic.id}/view`, { method: 'POST' }).catch(console.error)
      }
    } catch (err) {
      console.error('Error fetching topic:', err)
      setError(err instanceof Error ? err.message : 'Failed to load topic')
    } finally {
      setLoading(false)
    }
  }

  const handleReplySubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      router.push('/auth/signin')
      return
    }

    if (!replyContent.trim()) return

    try {
      setSubmittingReply(true)

      const response = await fetch('/api/forum/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic_id: data?.topic.id,
          content: replyContent.trim(),
          content_type: 'text',
          parent_post_id: replyingTo || undefined,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to post reply')
      }

      // Reset form and refresh posts
      setReplyContent('')
      setReplyingTo(null)
      await fetchTopicDetail()
    } catch (err) {
      console.error('Error posting reply:', err)
      alert(err instanceof Error ? err.message : 'Failed to post reply')
    } finally {
      setSubmittingReply(false)
    }
  }

  const handleReaction = async (postId: string, type: 'like' | 'dislike') => {
    if (!user) {
      router.push('/auth/signin')
      return
    }

    try {
      const response = await fetch('/api/forum/reactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          post_id: postId,
          reaction_type: type,
        }),
      })

      if (response.ok) {
        // Refresh topic to update reaction counts
        await fetchTopicDetail()
      }
    } catch (err) {
      console.error('Error posting reaction:', err)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    return date.toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            <div className="animate-pulse space-y-6">
              <div className="h-8 w-3/4 rounded bg-gray-200"></div>
              <div className="h-4 w-1/2 rounded bg-gray-200"></div>
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-32 rounded bg-gray-200"></div>
                ))}
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            <Alert className="border-red-200 bg-red-50">
              <AlertDescription className="text-red-600">
                {error || 'Topic not found'}
              </AlertDescription>
            </Alert>
            <div className="mt-4">
              <Button onClick={() => router.back()} variant="outline">
                Go Back
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  const { topic, posts } = data

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        <div className="container mx-auto px-4 py-8">
          {/* Breadcrumb */}
          <nav className="mb-6 text-sm text-gray-600">
            <Link href="/forum" className="hover:text-blue-600">
              Forum
            </Link>
            <span className="mx-2">›</span>
            <Link href={`/forum/category/${topic.category.slug}`} className="hover:text-blue-600">
              {topic.category.name}
            </Link>
            <span className="mx-2">›</span>
            <span className="text-gray-900">{topic.title}</span>
          </nav>

          {/* Topic Header */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="mb-3 flex items-center space-x-2">
                    {topic.is_pinned && <Pin className="h-4 w-4 text-orange-500" />}
                    {topic.is_locked && <Lock className="h-4 w-4 text-red-500" />}
                    <Badge
                      style={{
                        backgroundColor: topic.category.color
                          ? `${topic.category.color}20`
                          : '#f3f4f6',
                        color: topic.category.color || '#6b7280',
                      }}
                    >
                      {topic.category.icon} {topic.category.name}
                    </Badge>
                  </div>

                  <h1 className="mb-3 text-2xl font-bold md:text-3xl">{topic.title}</h1>

                  <div className="mb-4 flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      {topic.author.avatar_url ? (
                        <img
                          src={topic.author.avatar_url}
                          alt={topic.author.full_name}
                          className="h-6 w-6 rounded-full"
                        />
                      ) : (
                        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-300 text-xs">
                          {topic.author.full_name[0]?.toUpperCase()}
                        </div>
                      )}
                      <span>by {topic.author.full_name}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{formatDate(topic.created_at)}</span>
                    </div>
                    <span>•</span>
                    <span>{topic.view_count} views</span>
                    <span>•</span>
                    <span>{topic.post_count} replies</span>
                  </div>

                  {/* Topic Content */}
                  <div className="prose max-w-none">
                    {topic.content_type === 'markdown' ? (
                      <div dangerouslySetInnerHTML={{ __html: topic.content }} />
                    ) : (
                      <p className="whitespace-pre-wrap">{topic.content}</p>
                    )}
                  </div>

                  {/* Tags */}
                  {topic.tags && topic.tags.length > 0 && (
                    <div className="mt-4 flex flex-wrap gap-2">
                      {topic.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}

                  {/* Linked EV/Manufacturer */}
                  {(topic.linked_ev_model || topic.linked_manufacturer) && (
                    <div className="mt-4 flex items-center space-x-4">
                      {topic.linked_ev_model && (
                        <Link
                          href={`/ev-models/${topic.linked_ev_model.id}`}
                          className="flex items-center space-x-2 text-sm text-blue-600 hover:text-blue-800"
                        >
                          <MessageSquare className="h-4 w-4" />
                          <span>
                            {topic.linked_ev_model.make} {topic.linked_ev_model.name}
                          </span>
                        </Link>
                      )}
                      {topic.linked_manufacturer && (
                        <Link
                          href={`/manufacturers/${topic.linked_manufacturer.id}`}
                          className="flex items-center space-x-2 text-sm text-blue-600 hover:text-blue-800"
                        >
                          <MessageSquare className="h-4 w-4" />
                          <span>{topic.linked_manufacturer.name}</span>
                        </Link>
                      )}
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="ml-4 flex flex-col space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleReaction(topic.id, 'like')}
                    className="flex items-center space-x-1"
                  >
                    <Heart className="h-4 w-4" />
                    <span>{topic.like_count}</span>
                  </Button>
                  <Button variant="outline" size="sm">
                    <Bookmark className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Share2 className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Posts */}
          <div className="space-y-4">
            {posts.map((post, index) => (
              <Card key={post.id} className="relative">
                <CardContent className="p-6">
                  {/* Reply indicator */}
                  {post.parent_post && (
                    <div className="mb-4 rounded-lg bg-gray-50 p-3">
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <Reply className="h-4 w-4" />
                        <span>Replying to {post.parent_post.author.full_name}:</span>
                      </div>
                      <p className="mt-1 line-clamp-2 text-sm text-gray-700">
                        {post.parent_post.content}
                      </p>
                    </div>
                  )}

                  <div className="flex items-start space-x-4">
                    {/* Author Avatar */}
                    {post.author.avatar_url ? (
                      <img
                        src={post.author.avatar_url}
                        alt={post.author.full_name}
                        className="h-10 w-10 rounded-full"
                      />
                    ) : (
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-300">
                        {post.author.full_name[0]?.toUpperCase()}
                      </div>
                    )}

                    <div className="flex-1">
                      {/* Post Header */}
                      <div className="mb-3 flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{post.author.full_name}</span>
                          <span className="text-sm text-gray-600">#{index + 1}</span>
                          <span className="text-sm text-gray-600">•</span>
                          <span className="text-sm text-gray-600">
                            {formatDate(post.created_at)}
                          </span>
                          {post.is_edited && (
                            <>
                              <span className="text-sm text-gray-600">•</span>
                              <span className="text-sm italic text-gray-500">edited</span>
                            </>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          {user && user.id === post.author.id && (
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          )}
                          <Button variant="ghost" size="sm">
                            <Flag className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Post Content */}
                      <div className="prose max-w-none">
                        {post.content_type === 'markdown' ? (
                          <div dangerouslySetInnerHTML={{ __html: post.content }} />
                        ) : (
                          <p className="whitespace-pre-wrap">{post.content}</p>
                        )}
                      </div>

                      {/* Post Actions */}
                      <div className="mt-4 flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleReaction(post.id, 'like')}
                            className="flex items-center space-x-1"
                          >
                            <ChevronUp className="h-4 w-4" />
                            <span>{post.like_count}</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setReplyingTo(replyingTo === post.id ? null : post.id)}
                            className="flex items-center space-x-1"
                          >
                            <Reply className="h-4 w-4" />
                            <span>Reply</span>
                          </Button>
                        </div>
                      </div>

                      {/* Reply Form */}
                      {replyingTo === post.id && (
                        <form onSubmit={handleReplySubmit} className="mt-4 space-y-3">
                          <textarea
                            value={replyContent}
                            onChange={(e) => setReplyContent(e.target.value)}
                            placeholder="Write your reply..."
                            className="min-h-[100px] w-full rounded-lg border border-gray-300 p-3 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                            rows={3}
                          />
                          <div className="flex items-center space-x-2">
                            <Button
                              type="submit"
                              disabled={!replyContent.trim() || submittingReply}
                              size="sm"
                            >
                              {submittingReply ? (
                                <>
                                  <LoadingSpinner className="mr-2 h-4 w-4" />
                                  Posting...
                                </>
                              ) : (
                                'Post Reply'
                              )}
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setReplyingTo(null)
                                setReplyContent('')
                              }}
                            >
                              Cancel
                            </Button>
                          </div>
                        </form>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Main Reply Form */}
          {!topic.is_locked && (
            <Card className="mt-6">
              <CardContent className="p-6">
                <h3 className="mb-4 text-lg font-semibold">Post a Reply</h3>
                {user ? (
                  <form onSubmit={handleReplySubmit} className="space-y-4">
                    <textarea
                      value={replyContent}
                      onChange={(e) => setReplyContent(e.target.value)}
                      placeholder="Write your reply to this topic..."
                      className="min-h-[120px] w-full rounded-lg border border-gray-300 p-4 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      rows={4}
                    />
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-gray-600">
                        Be respectful and follow our community guidelines.
                      </p>
                      <Button type="submit" disabled={!replyContent.trim() || submittingReply}>
                        {submittingReply ? (
                          <>
                            <LoadingSpinner className="mr-2 h-4 w-4" />
                            Posting Reply...
                          </>
                        ) : (
                          'Post Reply'
                        )}
                      </Button>
                    </div>
                  </form>
                ) : (
                  <div className="text-center">
                    <p className="mb-4 text-gray-600">You need to be signed in to post a reply.</p>
                    <div className="space-x-2">
                      <Button asChild>
                        <Link href="/auth/signin">Sign In</Link>
                      </Button>
                      <Button variant="outline" asChild>
                        <Link href="/auth/signup">Sign Up</Link>
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {topic.is_locked && (
            <Card className="mt-6">
              <CardContent className="p-6 text-center">
                <Lock className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                <p className="text-gray-600">
                  This topic has been locked and no new replies can be posted.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
      <Footer />
    </div>
  )
}
