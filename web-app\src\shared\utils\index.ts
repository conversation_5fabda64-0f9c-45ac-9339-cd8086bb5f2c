// Utility functions shared between web and mobile apps

/**
 * Calculate the distance between two points using the Haversine formula
 */
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 3959 // Earth's radius in miles
  const dLat = toRadians(lat2 - lat1)
  const dLon = toRadians(lon2 - lon1)
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180)
}

/**
 * Calculate estimated range based on battery level and vehicle specs
 */
export function calculateEstimatedRange(
  batteryLevel: number,
  maxRange: number
): number {
  return Math.round((batteryLevel / 100) * maxRange)
}

/**
 * Calculate charging time estimate
 */
export function calculateChargingTime(
  currentLevel: number,
  targetLevel: number,
  batteryCapacity: number,
  chargingPower: number
): number {
  const energyNeeded = ((targetLevel - currentLevel) / 100) * batteryCapacity
  return Math.round((energyNeeded / chargingPower) * 60) // Return minutes
}

/**
 * Calculate charging cost
 */
export function calculateChargingCost(
  energyAdded: number,
  pricePerKwh: number
): number {
  return Math.round(energyAdded * pricePerKwh * 100) / 100
}

/**
 * Calculate trip efficiency
 */
export function calculateEfficiency(
  distanceMiles: number,
  energyUsedKwh: number
): number {
  if (energyUsedKwh === 0) return 0
  return Math.round((distanceMiles / energyUsedKwh) * 100) / 100
}

/**
 * Calculate carbon savings compared to gas vehicle
 */
export function calculateCarbonSavings(
  distanceMiles: number,
  gasVehicleMpg: number = 25
): number {
  // Average CO2 emissions: 19.6 lbs per gallon of gasoline
  const gallonsUsed = distanceMiles / gasVehicleMpg
  return Math.round(gallonsUsed * 19.6 * 100) / 100
}

/**
 * Calculate money savings compared to gas vehicle
 */
export function calculateMoneySavings(
  distanceMiles: number,
  electricityCost: number,
  energyUsedKwh: number,
  gasPrice: number = 3.50,
  gasVehicleMpg: number = 25
): number {
  const electricCost = energyUsedKwh * electricityCost
  const gasCost = (distanceMiles / gasVehicleMpg) * gasPrice
  return Math.round((gasCost - electricCost) * 100) / 100
}

/**
 * Format duration in minutes to human readable format
 */
export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}m`
  }
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  if (remainingMinutes === 0) {
    return `${hours}h`
  }
  return `${hours}h ${remainingMinutes}m`
}

/**
 * Format distance with appropriate units
 */
export function formatDistance(miles: number): string {
  if (miles < 1) {
    return `${Math.round(miles * 5280)} ft`
  }
  return `${Math.round(miles * 10) / 10} mi`
}

/**
 * Format currency
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

/**
 * Format date relative to now
 */
export function formatRelativeTime(date: string | Date): string {
  const now = new Date()
  const targetDate = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'Just now'
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`
  }

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours}h ago`
  }

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) {
    return `${diffInDays}d ago`
  }

  const diffInWeeks = Math.floor(diffInDays / 7)
  if (diffInWeeks < 4) {
    return `${diffInWeeks}w ago`
  }

  return targetDate.toLocaleDateString()
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validate VIN format
 */
export function isValidVIN(vin: string): boolean {
  const vinRegex = /^[A-HJ-NPR-Z0-9]{17}$/
  return vinRegex.test(vin.toUpperCase())
}

/**
 * Generate a random color for vehicles
 */
export function getRandomVehicleColor(): string {
  const colors = [
    'White',
    'Black',
    'Silver',
    'Gray',
    'Red',
    'Blue',
    'Green',
    'Yellow',
    'Orange',
    'Purple',
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

/**
 * Get battery level color based on percentage
 */
export function getBatteryLevelColor(level: number): string {
  if (level >= 80) return '#22c55e' // Green
  if (level >= 50) return '#eab308' // Yellow
  if (level >= 20) return '#f97316' // Orange
  return '#ef4444' // Red
}

/**
 * Get charging station availability status
 */
export function getAvailabilityStatus(
  availableStalls: number,
  totalStalls: number
): 'available' | 'busy' | 'full' {
  const ratio = availableStalls / totalStalls
  if (ratio >= 0.5) return 'available'
  if (ratio > 0) return 'busy'
  return 'full'
}

/**
 * Sort charging stations by distance
 */
export function sortStationsByDistance(
  stations: any[],
  userLat: number,
  userLon: number
): any[] {
  return stations
    .map(station => ({
      ...station,
      distance: calculateDistance(userLat, userLon, station.latitude, station.longitude),
    }))
    .sort((a, b) => a.distance - b.distance)
}

/**
 * Debounce function for search inputs
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
