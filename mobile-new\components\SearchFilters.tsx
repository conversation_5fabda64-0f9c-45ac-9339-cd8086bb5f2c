import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import Slider from "@react-native-community/slider";
import { VEHICLE_MAKES, EV_BODY_TYPES } from "@/constants/filters";
import { Colors } from "@/constants/Colors";

interface SearchFiltersProps {
  filters: Record<string, any>;
  onFiltersChange: (filters: Record<string, any>) => void;
  onClose: () => void;
}

export function SearchFilters({
  filters,
  onFiltersChange,
  onClose,
}: SearchFiltersProps) {
  const [localFilters, setLocalFilters] = useState(filters);

  const formatPrice = (priceInCents: number) => {
    return `$${(priceInCents / 100).toLocaleString()}`;
  };

  const updateLocalFilter = (key: string, value: any) => {
    setLocalFilters((prev) => ({ ...prev, [key]: value }));
  };

  const applyFilters = () => {
    onFiltersChange(localFilters);
    onClose();
  };

  const clearAllFilters = () => {
    const clearedFilters = Object.keys(localFilters).reduce((acc, key) => {
      acc[key] = undefined;
      return acc;
    }, {} as Record<string, any>);
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const FilterSection: React.FC<{
    title: string;
    children: React.ReactNode;
  }> = ({ title, children }) => (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const FilterOption = ({
    label,
    selected,
    onPress,
  }: {
    label: string;
    selected: boolean;
    onPress: () => void;
  }) => (
    <TouchableOpacity
      onPress={onPress}
      style={[
        styles.optionButton,
        selected ? styles.optionButtonSelected : styles.optionButtonDefault,
      ]}
    >
      <Text
        style={[
          styles.optionText,
          selected ? styles.optionTextSelected : styles.optionTextDefault,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <Modal visible={true} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.modalContainer}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#374151" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Filters</Text>
          <TouchableOpacity onPress={clearAllFilters}>
            <Text style={styles.clearButton}>Clear All</Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
        >
          <FilterSection title="Price Range">
            <Text style={styles.sliderLabel}>
              Maximum Price: {formatPrice(localFilters.priceMax || 15000000)}
            </Text>
            <Slider
              style={styles.slider}
              minimumValue={2000000}
              maximumValue={15000000}
              step={500000}
              value={localFilters.priceMax || 15000000}
              onValueChange={(value) => updateLocalFilter("priceMax", value)}
              minimumTrackTintColor={Colors.light.tint}
              maximumTrackTintColor="#d1d5db"
              thumbTintColor={Colors.light.tint}
            />
          </FilterSection>

          <FilterSection title="Make">
            <View style={styles.optionsContainer}>
              {VEHICLE_MAKES.slice(0, -1).map((make: string) => (
                <FilterOption
                  key={make}
                  label={make}
                  selected={localFilters.make === make}
                  onPress={() =>
                    updateLocalFilter(
                      "make",
                      localFilters.make === make ? undefined : make
                    )
                  }
                />
              ))}
            </View>
          </FilterSection>

          <FilterSection title="Body Type">
            <View style={styles.optionsContainer}>
              {EV_BODY_TYPES.map((type: string) => (
                <FilterOption
                  key={type}
                  label={type.charAt(0).toUpperCase() + type.slice(1)}
                  selected={localFilters.bodyType === type}
                  onPress={() =>
                    updateLocalFilter(
                      "bodyType",
                      localFilters.bodyType === type ? undefined : type
                    )
                  }
                />
              ))}
            </View>
          </FilterSection>

          <FilterSection title="Range">
            <Text style={styles.sliderLabel}>
              Minimum Range: {localFilters.rangeMin || 100} miles
            </Text>
            <Slider
              style={styles.slider}
              minimumValue={100}
              maximumValue={500}
              step={25}
              value={localFilters.rangeMin || 100}
              onValueChange={(value) => updateLocalFilter("rangeMin", value)}
              minimumTrackTintColor={Colors.light.tint}
              maximumTrackTintColor="#d1d5db"
              thumbTintColor={Colors.light.tint}
            />
          </FilterSection>

          <FilterSection title="Efficiency">
            <Text style={styles.sliderLabel}>
              Minimum Efficiency: {localFilters.efficiencyMin || 70} MPGe
            </Text>
            <Slider
              style={styles.slider}
              minimumValue={70}
              maximumValue={150}
              step={5}
              value={localFilters.efficiencyMin || 70}
              onValueChange={(value) =>
                updateLocalFilter("efficiencyMin", value)
              }
              minimumTrackTintColor={Colors.light.tint}
              maximumTrackTintColor="#d1d5db"
              thumbTintColor={Colors.light.tint}
            />
          </FilterSection>

          <FilterSection title="Performance">
            <Text style={styles.sliderLabel}>
              Maximum 0-60 mph: {localFilters.accelerationMax || 10.0}s
            </Text>
            <Slider
              style={styles.slider}
              minimumValue={2.0}
              maximumValue={10.0}
              step={0.5}
              value={localFilters.accelerationMax || 10.0}
              onValueChange={(value) =>
                updateLocalFilter("accelerationMax", value)
              }
              minimumTrackTintColor={Colors.light.tint}
              maximumTrackTintColor="#d1d5db"
              thumbTintColor={Colors.light.tint}
            />
          </FilterSection>

          <FilterSection title="Charging Speed">
            <Text style={styles.sliderLabel}>
              Minimum DC Fast Charging: {localFilters.chargingSpeedMin || 50} kW
            </Text>
            <Slider
              style={styles.slider}
              minimumValue={50}
              maximumValue={350}
              step={25}
              value={localFilters.chargingSpeedMin || 50}
              onValueChange={(value) =>
                updateLocalFilter("chargingSpeedMin", value)
              }
              minimumTrackTintColor={Colors.light.tint}
              maximumTrackTintColor="#d1d5db"
              thumbTintColor={Colors.light.tint}
            />
          </FilterSection>

          <FilterSection title="Special Categories">
            <View style={styles.optionsContainer}>
              <FilterOption
                label="Featured"
                selected={localFilters.featured === "true"}
                onPress={() =>
                  updateLocalFilter(
                    "featured",
                    localFilters.featured === "true" ? undefined : "true"
                  )
                }
              />
              <FilterOption
                label="Editor's Choice"
                selected={localFilters.editorChoice === "true"}
                onPress={() =>
                  updateLocalFilter(
                    "editorChoice",
                    localFilters.editorChoice === "true" ? undefined : "true"
                  )
                }
              />
              <FilterOption
                label="Best Value"
                selected={localFilters.bestValue === "true"}
                onPress={() =>
                  updateLocalFilter(
                    "bestValue",
                    localFilters.bestValue === "true" ? undefined : "true"
                  )
                }
              />
            </View>
          </FilterSection>
        </ScrollView>
        <View style={styles.footer}>
          <TouchableOpacity style={styles.applyButton} onPress={applyFilters}>
            <Text style={styles.applyButtonText}>Apply Filters</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "white",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1f2937",
  },
  clearButton: {
    color: Colors.light.tint,
    fontWeight: "500",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 12,
  },
  sliderLabel: {
    fontSize: 14,
    color: "#4b5563",
    marginBottom: 8,
  },
  slider: {
    width: "100%",
    height: 40,
  },
  optionsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8,
  },
  optionButtonSelected: {
    backgroundColor: Colors.light.tint,
    borderColor: Colors.light.tint,
  },
  optionButtonDefault: {
    backgroundColor: "white",
    borderColor: "#d1d5db",
  },
  optionText: {
    fontWeight: "500",
  },
  optionTextSelected: {
    color: "white",
  },
  optionTextDefault: {
    color: "#374151",
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#e5e7eb",
  },
  applyButton: {
    backgroundColor: Colors.light.tint,
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
  },
  applyButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 16,
  },
});
