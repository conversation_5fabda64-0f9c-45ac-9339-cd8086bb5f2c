import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/shared/types'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50)

    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        data: [],
        suggestions: []
      })
    }

    const searchTerm = query.trim()

    // Full-text search using the search index
    const { data: searchResults, error } = await supabase
      .from('ev_models')
      .select(`
        id,
        make,
        model,
        year,
        trim,
        body_type,
        price_msrp,
        range_epa_miles,
        images,
        is_featured,
        best_value,
        popularity_score,
        ev_manufacturers!inner(name, logo_url)
      `)
      .textSearch('make,model,trim', searchTerm, {
        type: 'websearch',
        config: 'english'
      })
      .eq('production_status', 'current')
      .order('popularity_score', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Search error:', error)
      // Fallback to simple ILIKE search
      const { data: fallbackResults } = await supabase
        .from('ev_models')
        .select(`
          id,
          make,
          model,
          year,
          trim,
          body_type,
          price_msrp,
          range_epa_miles,
          images,
          is_featured,
          best_value,
          popularity_score,
          ev_manufacturers!inner(name, logo_url)
        `)
        .or(`make.ilike.%${searchTerm}%,model.ilike.%${searchTerm}%,trim.ilike.%${searchTerm}%`)
        .eq('production_status', 'current')
        .order('popularity_score', { ascending: false })
        .limit(limit)

      return NextResponse.json({
        data: fallbackResults || [],
        suggestions: []
      })
    }

    // Generate search suggestions based on partial matches
    const { data: suggestions } = await supabase
      .from('ev_models')
      .select('make, model')
      .or(`make.ilike.%${searchTerm}%,model.ilike.%${searchTerm}%`)
      .eq('production_status', 'current')
      .limit(5)

    const uniqueSuggestions = Array.from(
      new Set(
        suggestions?.flatMap(item => [
          item.make,
          item.model,
          `${item.make} ${item.model}`
        ]).filter(suggestion => 
          suggestion.toLowerCase().includes(searchTerm.toLowerCase()) &&
          suggestion.toLowerCase() !== searchTerm.toLowerCase()
        ) || []
      )
    ).slice(0, 5)

    return NextResponse.json({
      data: searchResults || [],
      suggestions: uniqueSuggestions,
      query: searchTerm
    })

  } catch (error) {
    console.error('Unexpected search error:', error)
    return NextResponse.json(
      { error: 'Search failed' },
      { status: 500 }
    )
  }
}
