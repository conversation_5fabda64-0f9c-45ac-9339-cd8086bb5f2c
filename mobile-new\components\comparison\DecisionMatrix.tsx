import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import Slider from "@react-native-community/slider";
import { EVModel } from "@/types";

interface DecisionMatrixProps {
  models: EVModel[];
}

interface DecisionWeights {
  price: number;
  range: number;
  efficiency: number;
  performance: number;
  charging: number;
}

interface ModelScore {
  model: EVModel;
  scores: {
    price: number;
    range: number;
    efficiency: number;
    performance: number;
    charging: number;
  };
  weightedTotal: number;
  rank: number;
}

const PRESET_PROFILES = {
  commuter: {
    price: 30,
    range: 25,
    efficiency: 30,
    performance: 5,
    charging: 10,
  },
  family: {
    price: 25,
    range: 30,
    efficiency: 20,
    performance: 10,
    charging: 15,
  },
  performance: {
    price: 15,
    range: 20,
    efficiency: 10,
    performance: 40,
    charging: 15,
  },
  budget: { price: 50, range: 20, efficiency: 20, performance: 5, charging: 5 },
  balanced: {
    price: 20,
    range: 20,
    efficiency: 20,
    performance: 20,
    charging: 20,
  },
};

const ScoreBar = ({
  score,
  color = "#3B82F6",
}: {
  score: number;
  color?: string;
}) => (
  <View style={styles.scoreBarBackground}>
    <View
      style={[
        styles.scoreBarFill,
        { width: `${score}%`, backgroundColor: color },
      ]}
    />
  </View>
);

export function DecisionMatrix({ models }: DecisionMatrixProps) {
  const [weights, setWeights] = useState<DecisionWeights>(
    PRESET_PROFILES.balanced
  );
  const [selectedProfile, setSelectedProfile] = useState<string>("balanced");

  const normalizeScore = (
    value: number | null,
    min: number,
    max: number,
    reverse = false
  ): number => {
    if (value === null || value === undefined) return 0;
    if (max === min) return 50;
    const normalized = ((value - min) / (max - min)) * 100;
    return reverse ? 100 - normalized : normalized;
  };

  const calculateScores = (): ModelScore[] => {
    if (models.length === 0) return [];

    const prices = models
      .map((m) => m.price_msrp)
      .filter((p) => p !== null) as number[];
    const ranges = models
      .map((m) => m.range_epa_miles)
      .filter((r) => r !== null) as number[];
    const efficiencies = models
      .map((m) => m.efficiency_mpge)
      .filter((e) => e !== null) as number[];
    const accelerations = models
      .map((m) => m.acceleration_0_60_mph)
      .filter((a) => a !== null) as number[];
    const chargingSpeeds = models
      .map((m) => m.charging_speed_dc_kw)
      .filter((c) => c !== null) as number[];

    const priceRange = { min: Math.min(...prices), max: Math.max(...prices) };
    const rangeRange = { min: Math.min(...ranges), max: Math.max(...ranges) };
    const efficiencyRange = {
      min: Math.min(...efficiencies),
      max: Math.max(...efficiencies),
    };
    const accelerationRange = {
      min: Math.min(...accelerations),
      max: Math.max(...accelerations),
    };
    const chargingRange = {
      min: Math.min(...chargingSpeeds),
      max: Math.max(...chargingSpeeds),
    };

    const modelScores = models.map((model): ModelScore => {
      const scores = {
        price: normalizeScore(
          model.price_msrp,
          priceRange.min,
          priceRange.max,
          true
        ),
        range: normalizeScore(
          model.range_epa_miles,
          rangeRange.min,
          rangeRange.max
        ),
        efficiency: normalizeScore(
          model.efficiency_mpge,
          efficiencyRange.min,
          efficiencyRange.max
        ),
        performance: normalizeScore(
          model.acceleration_0_60_mph,
          accelerationRange.min,
          accelerationRange.max,
          true
        ),
        charging: normalizeScore(
          model.charging_speed_dc_kw,
          chargingRange.min,
          chargingRange.max
        ),
      };

      const weightedTotal =
        (scores.price * weights.price +
          scores.range * weights.range +
          scores.efficiency * weights.efficiency +
          scores.performance * weights.performance +
          scores.charging * weights.charging) /
        100;

      return { model, scores, weightedTotal, rank: 0 };
    });

    modelScores.sort((a, b) => b.weightedTotal - a.weightedTotal);
    modelScores.forEach((score, index) => {
      score.rank = index + 1;
    });

    return modelScores;
  };

  const updateWeight = (category: keyof DecisionWeights, value: number) => {
    setWeights((prev) => ({ ...prev, [category]: value }));
    setSelectedProfile("custom");
  };

  const applyProfile = (profileName: string) => {
    if (profileName in PRESET_PROFILES) {
      setWeights(PRESET_PROFILES[profileName as keyof typeof PRESET_PROFILES]);
      setSelectedProfile(profileName);
    }
  };

  const modelScores = calculateScores();

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Your Priorities</Text>
        <WeightSlider
          label="Price"
          category="price"
          icon="cash-outline"
          weights={weights}
          updateWeight={updateWeight}
        />
        <WeightSlider
          label="Range"
          category="range"
          icon="speedometer-outline"
          weights={weights}
          updateWeight={updateWeight}
        />
        <WeightSlider
          label="Efficiency"
          category="efficiency"
          icon="leaf-outline"
          weights={weights}
          updateWeight={updateWeight}
        />
        <WeightSlider
          label="Performance"
          category="performance"
          icon="flash-outline"
          weights={weights}
          updateWeight={updateWeight}
        />
        <WeightSlider
          label="Charging"
          category="charging"
          icon="battery-charging-outline"
          weights={weights}
          updateWeight={updateWeight}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Results</Text>
        {modelScores.map((score) => (
          <ModelScoreCard key={score.model.id} modelScore={score} />
        ))}
      </View>
    </ScrollView>
  );
}

const WeightSlider = ({
  label,
  category,
  icon,
  weights,
  updateWeight,
}: {
  label: string;
  category: keyof DecisionWeights;
  icon: keyof typeof Ionicons.glyphMap;
  weights: DecisionWeights;
  updateWeight: (category: keyof DecisionWeights, value: number) => void;
}) => (
  <View style={styles.sliderContainer}>
    <View style={styles.sliderLabelContainer}>
      <View style={styles.sliderLabel}>
        <Ionicons name={icon} size={16} color="#6b7280" />
        <Text style={styles.sliderText}>{label}</Text>
      </View>
      <Text style={styles.sliderValue}>{weights[category]}%</Text>
    </View>
    <Slider
      style={{ width: "100%", height: 40 }}
      minimumValue={0}
      maximumValue={50}
      step={5}
      value={weights[category]}
      onValueChange={(value) => updateWeight(category, value)}
      minimumTrackTintColor="#22c55e"
      maximumTrackTintColor="#d1d5db"
      thumbTintColor="#22c55e"
    />
  </View>
);

const ModelScoreCard = ({ modelScore }: { modelScore: ModelScore }) => {
  const { model, scores, weightedTotal, rank } = modelScore;
  const isTopChoice = rank === 1;

  return (
    <View style={[styles.card, isTopChoice && styles.topChoiceCard]}>
      <View style={styles.cardHeader}>
        <View style={styles.modelInfo}>
          <Text style={styles.rank}>#{rank}</Text>
          <View>
            <Text style={styles.modelName}>
              {model.make} {model.model}
            </Text>
            {model.trim && <Text style={styles.modelTrim}>{model.trim}</Text>}
          </View>
        </View>
        {isTopChoice && (
          <View style={styles.topChoiceBadge}>
            <Text style={styles.topChoiceText}>Top Choice</Text>
          </View>
        )}
      </View>

      <View style={styles.overallScoreContainer}>
        <View style={styles.overallScoreHeader}>
          <Text style={styles.overallScoreLabel}>Overall Score</Text>
          <Text
            style={[
              styles.overallScoreValue,
              isTopChoice && styles.topChoiceScoreValue,
            ]}
          >
            {Math.round(weightedTotal)}/100
          </Text>
        </View>
        <ScoreBar
          score={weightedTotal}
          color={isTopChoice ? "#22C55E" : "#3B82F6"}
        />
      </View>

      <View style={styles.categoryScoresContainer}>
        <CategoryScoreRow
          icon="cash-outline"
          label="Price"
          score={scores.price}
          color="#4ADE80"
        />
        <CategoryScoreRow
          icon="speedometer-outline"
          label="Range"
          score={scores.range}
          color="#60A5FA"
        />
        <CategoryScoreRow
          icon="leaf-outline"
          label="Efficiency"
          score={scores.efficiency}
          color="#4ADE80"
        />
        <CategoryScoreRow
          icon="flash-outline"
          label="Performance"
          score={scores.performance}
          color="#A78BFA"
        />
        <CategoryScoreRow
          icon="battery-charging-outline"
          label="Charging"
          score={scores.charging}
          color="#FBBF24"
        />
      </View>
    </View>
  );
};

const CategoryScoreRow = ({
  icon,
  label,
  score,
  color,
}: {
  icon: keyof typeof Ionicons.glyphMap;
  label: string;
  score: number;
  color: string;
}) => (
  <View style={styles.categoryScoreRow}>
    <View style={styles.categoryLabelContainer}>
      <Ionicons name={icon} size={14} color="#6B7280" />
      <Text style={styles.categoryLabel}>{label}</Text>
    </View>
    <View style={styles.categoryScore}>
      <ScoreBar score={score} color={color} />
      <Text style={styles.categoryScoreValue}>{Math.round(score)}</Text>
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#F9FAFB" },
  section: { backgroundColor: "white", padding: 16, marginBottom: 12 },
  sectionTitle: { fontSize: 20, fontWeight: "bold", marginBottom: 16 },
  sliderContainer: { marginBottom: 4 },
  sliderLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 2,
  },
  sliderLabel: { flexDirection: "row", alignItems: "center" },
  sliderText: { color: "#374151", marginLeft: 8 },
  sliderValue: { fontWeight: "500", color: "#111827" },
  card: {
    backgroundColor: "white",
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  topChoiceCard: { borderColor: "#10B981", borderWidth: 2 },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  modelInfo: { flexDirection: "row", alignItems: "center" },
  rank: { fontSize: 18, fontWeight: "bold", color: "#9CA3AF", marginRight: 12 },
  modelName: { fontSize: 18, fontWeight: "600", color: "#111827" },
  modelTrim: { fontSize: 14, color: "#4B5563" },
  topChoiceBadge: {
    backgroundColor: "#D1FAE5",
    borderRadius: 999,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  topChoiceText: { color: "#065F46", fontSize: 12, fontWeight: "500" },
  overallScoreContainer: { marginBottom: 16 },
  overallScoreHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  overallScoreLabel: { fontWeight: "500", color: "#111827" },
  overallScoreValue: { fontSize: 18, fontWeight: "bold", color: "#111827" },
  topChoiceScoreValue: { color: "#059669" },
  categoryScoresContainer: { gap: 12 },
  categoryScoreRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  categoryLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  categoryLabel: { fontSize: 14, color: "#4B5563", marginLeft: 8 },
  categoryScore: { flexDirection: "row", alignItems: "center", flex: 1 },
  categoryScoreValue: {
    fontSize: 14,
    fontWeight: "500",
    color: "#111827",
    marginLeft: 8,
    width: 28,
  },
  scoreBarBackground: {
    backgroundColor: "#E5E7EB",
    borderRadius: 999,
    height: 8,
    flex: 1,
  },
  scoreBarFill: { height: 8, borderRadius: 999 },
});
