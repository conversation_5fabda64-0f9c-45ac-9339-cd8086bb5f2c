import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { IntelligentSearch } from '@/components/ev-models/IntelligentSearch'

// Mock the fetch API
global.fetch = jest.fn()

const mockFetch = fetch as jest.MockedFunction<typeof fetch>

const mockSearchResponse = {
  modelSuggestions: [
    {
      make: 'Tesla',
      model: 'Model 3',
      price_msrp: 4799900,
      range_epa_miles: 358
    },
    {
      make: 'BMW',
      model: 'iX',
      price_msrp: 8399900,
      range_epa_miles: 324
    }
  ],
  semanticSuggestions: [
    {
      text: 'EVs under $40,000',
      description: 'Affordable electric vehicles'
    },
    {
      text: 'Long-range EVs (300+ miles)',
      description: 'Electric vehicles with extended range'
    }
  ],
  featureSuggestions: [
    'autopilot',
    'all-wheel drive',
    'fast charging'
  ],
  query: 'affordable long range'
}

describe('IntelligentSearch', () => {
  const mockOnChange = jest.fn()
  const mockOnQuickFilter = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    localStorage.clear()
    
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => mockSearchResponse,
    } as Response)
  })

  const renderIntelligentSearch = (props = {}) => {
    return render(
      <IntelligentSearch
        value=""
        onChange={mockOnChange}
        onQuickFilter={mockOnQuickFilter}
        {...props}
      />
    )
  }

  describe('Basic Search Functionality', () => {
    it('should render search input with placeholder', () => {
      renderIntelligentSearch()
      
      expect(screen.getByPlaceholderText(/describe what you're looking for/i)).toBeInTheDocument()
    })

    it('should call onChange when user types', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'tesla')
      
      expect(mockOnChange).toHaveBeenCalledWith('tesla')
    })

    it('should show clear button when there is text', () => {
      renderIntelligentSearch({ value: 'tesla' })
      
      expect(screen.getByRole('button', { name: /clear/i })).toBeInTheDocument()
    })

    it('should clear input when clear button is clicked', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch({ value: 'tesla' })
      
      const clearButton = screen.getByRole('button', { name: /clear/i })
      await user.click(clearButton)
      
      expect(mockOnChange).toHaveBeenCalledWith('')
    })
  })

  describe('Quick Filters', () => {
    it('should show quick filters when filter button is clicked', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const filterButton = screen.getByRole('button', { name: /filter/i })
      await user.click(filterButton)
      
      expect(screen.getByText('Quick Filters')).toBeInTheDocument()
      expect(screen.getByText('Under $40k')).toBeInTheDocument()
      expect(screen.getByText('300+ Miles')).toBeInTheDocument()
      expect(screen.getByText('Fast Charging')).toBeInTheDocument()
    })

    it('should apply quick filter when clicked', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const filterButton = screen.getByRole('button', { name: /filter/i })
      await user.click(filterButton)
      
      const affordableFilter = screen.getByText('Under $40k')
      await user.click(affordableFilter)
      
      expect(mockOnChange).toHaveBeenCalledWith('affordable electric cars under 40000')
      expect(mockOnQuickFilter).toHaveBeenCalledWith({
        type: 'affordable',
        query: 'affordable electric cars under 40000'
      })
    })

    it('should hide quick filters when clicked outside', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const filterButton = screen.getByRole('button', { name: /filter/i })
      await user.click(filterButton)
      
      expect(screen.getByText('Quick Filters')).toBeInTheDocument()
      
      // Click outside
      await user.click(document.body)
      
      await waitFor(() => {
        expect(screen.queryByText('Quick Filters')).not.toBeInTheDocument()
      })
    })
  })

  describe('Intelligent Suggestions', () => {
    it('should fetch suggestions when user types', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'affordable')
      
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/ev-models/intelligent-search?q=affordable')
        )
      })
    })

    it('should display semantic suggestions', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'affordable')
      await user.click(input) // Focus to show suggestions
      
      await waitFor(() => {
        expect(screen.getByText('EVs under $40,000')).toBeInTheDocument()
        expect(screen.getByText('Affordable electric vehicles')).toBeInTheDocument()
      })
    })

    it('should display model suggestions', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'tesla')
      await user.click(input)
      
      await waitFor(() => {
        expect(screen.getByText('Tesla Model 3')).toBeInTheDocument()
        expect(screen.getByText('BMW iX')).toBeInTheDocument()
      })
    })

    it('should apply suggestion when clicked', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'affordable')
      await user.click(input)
      
      await waitFor(() => {
        expect(screen.getByText('EVs under $40,000')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('EVs under $40,000'))
      
      expect(mockOnChange).toHaveBeenCalledWith('EVs under $40,000')
    })
  })

  describe('Recent Searches', () => {
    it('should show recent searches when input is empty and focused', async () => {
      // Pre-populate localStorage with recent searches
      localStorage.setItem('ev-recent-searches', JSON.stringify(['tesla model 3', 'bmw ix']))
      
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      await user.click(input)
      
      await waitFor(() => {
        expect(screen.getByText('Recent Searches')).toBeInTheDocument()
        expect(screen.getByText('tesla model 3')).toBeInTheDocument()
        expect(screen.getByText('bmw ix')).toBeInTheDocument()
      })
    })

    it('should save search to recent searches when suggestion is clicked', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'tesla')
      await user.click(input)
      
      await waitFor(() => {
        expect(screen.getByText('Tesla Model 3')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('Tesla Model 3'))
      
      const saved = localStorage.getItem('ev-recent-searches')
      expect(saved).toBeTruthy()
      
      const recentSearches = JSON.parse(saved!)
      expect(recentSearches).toContain('Tesla Model 3')
    })
  })

  describe('Trending Searches', () => {
    it('should show trending searches when input is empty', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      await user.click(input)
      
      await waitFor(() => {
        expect(screen.getByText('Trending')).toBeInTheDocument()
        expect(screen.getByText('Tesla Model 3')).toBeInTheDocument()
      })
    })

    it('should apply trending search when clicked', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      await user.click(input)
      
      await waitFor(() => {
        expect(screen.getByText('Tesla Model 3')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('Tesla Model 3'))
      
      expect(mockOnChange).toHaveBeenCalledWith('Tesla Model 3')
    })
  })

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      mockFetch.mockRejectedValueOnce(new Error('API Error'))
      
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'tesla')
      
      // Should not crash and should still show input
      await waitFor(() => {
        expect(input).toBeInTheDocument()
      })
    })

    it('should fallback to basic suggestions on API failure', async () => {
      // Mock intelligent search to fail, then basic search to succeed
      mockFetch
        .mockRejectedValueOnce(new Error('Intelligent search failed'))
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            suggestions: ['Tesla', 'BMW', 'Hyundai']
          }),
        } as Response)
      
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'tesla')
      await user.click(input)
      
      await waitFor(() => {
        expect(screen.getByText('Tesla')).toBeInTheDocument()
      })
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('aria-label')
    })

    it('should be keyboard navigable', async () => {
      const user = userEvent.setup()
      renderIntelligentSearch()
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'tesla')
      await user.click(input)
      
      await waitFor(() => {
        expect(screen.getByText('Tesla Model 3')).toBeInTheDocument()
      })
      
      // Should be able to navigate suggestions with keyboard
      await user.keyboard('{ArrowDown}')
      await user.keyboard('{Enter}')
      
      expect(mockOnChange).toHaveBeenCalled()
    })
  })
})
