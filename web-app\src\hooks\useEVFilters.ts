'use client'

import { useState, useCallback, useMemo } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import type { EVModelFilters, SortOptions, EVSortOption } from '@/shared/types'

interface UseEVFiltersReturn {
  filters: EVModelFilters
  sortOptions: SortOptions
  searchQuery: string
  updateFilters: (newFilters: Partial<EVModelFilters>) => void
  updateSort: (newSort: SortOptions) => void
  updateSearch: (query: string) => void
  clearFilters: () => void
  hasActiveFilters: boolean
}

export function useEVFilters(): UseEVFiltersReturn {
  const router = useRouter()
  const searchParams = useSearchParams()

  // Initialize state from URL parameters
  const [filters, setFilters] = useState<EVModelFilters>(() => ({
    make: searchParams.get('make') || undefined,
    bodyType: searchParams.get('body_type') || undefined,
    priceMin: searchParams.get('price_min') || undefined,
    priceMax: searchParams.get('price_max') || undefined,
    rangeMin: searchParams.get('range_min') || undefined,
    productionStatus: searchParams.get('production_status') || 'current',
    featured: searchParams.get('featured') || undefined,
    bestValue: searchParams.get('best_value') || undefined,
    search: searchParams.get('search') || undefined
  }))

  const [sortOptions, setSortOptions] = useState<SortOptions>(() => ({
    field: (searchParams.get('sort_by') as EVSortOption) || 'popularity_score',
    order: (searchParams.get('sort_order') as 'asc' | 'desc') || 'desc'
  }))

  const [searchQuery, setSearchQuery] = useState(() => searchParams.get('search') || '')

  // Update URL when state changes
  const updateURL = useCallback((newFilters: EVModelFilters, newSort: SortOptions, newSearch: string) => {
    const params = new URLSearchParams()

    // Add filters to URL
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.set(key, value)
      }
    })

    // Add sort to URL
    if (newSort.field !== 'popularity_score') {
      params.set('sort_by', newSort.field)
    }
    if (newSort.order !== 'desc') {
      params.set('sort_order', newSort.order)
    }

    // Add search to URL
    if (newSearch) {
      params.set('search', newSearch)
    }

    // Update URL without page reload
    const newURL = params.toString() ? `?${params.toString()}` : '/ev-models'
    router.push(newURL, { scroll: false })
  }, [router])

  const updateFilters = useCallback((newFilters: Partial<EVModelFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    updateURL(updatedFilters, sortOptions, searchQuery)
  }, [filters, sortOptions, searchQuery, updateURL])

  const updateSort = useCallback((newSort: SortOptions) => {
    setSortOptions(newSort)
    updateURL(filters, newSort, searchQuery)
  }, [filters, searchQuery, updateURL])

  const updateSearch = useCallback((query: string) => {
    setSearchQuery(query)
    const updatedFilters = { ...filters, search: query || undefined }
    setFilters(updatedFilters)
    updateURL(updatedFilters, sortOptions, query)
  }, [filters, sortOptions, updateURL])

  const clearFilters = useCallback(() => {
    const clearedFilters: EVModelFilters = {
      productionStatus: 'current' // Keep production status as current
    }
    const defaultSort: SortOptions = {
      field: 'popularity_score',
      order: 'desc'
    }
    
    setFilters(clearedFilters)
    setSortOptions(defaultSort)
    setSearchQuery('')
    updateURL(clearedFilters, defaultSort, '')
  }, [updateURL])

  const hasActiveFilters = useMemo(() => {
    return Object.entries(filters).some(([key, value]) => {
      if (key === 'productionStatus' && value === 'current') return false
      return value !== undefined && value !== ''
    }) || searchQuery !== ''
  }, [filters, searchQuery])

  return {
    filters,
    sortOptions,
    searchQuery,
    updateFilters,
    updateSort,
    updateSearch,
    clearFilters,
    hasActiveFilters
  }
}
