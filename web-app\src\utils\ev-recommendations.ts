import type {
  EVModel,
  UserEVPreferences,
  UseCase,
  EVBodyType,
  PriorityWeights
} from '../types'
import { DEFAULT_PRIORITY_WEIGHTS } from '../constants/ev-buyer-guide'
import { calculateMatchScore } from './ev-buyer-guide'

// ===== RECOMMENDATION ENGINE =====

/**
 * Get recommended EV models based on user preferences
 */
export function getRecommendedEVs(
  evModels: EVModel[],
  preferences: UserEVPreferences | null,
  limit: number = 10
): Array<EVModel & { matchScore: number; reasons: string[] }> {
  if (!preferences) {
    // Return popular models if no preferences
    return evModels
      .filter(model => model.production_status === 'current')
      .sort((a, b) => b.popularity_score - a.popularity_score)
      .slice(0, limit)
      .map(model => ({
        ...model,
        matchScore: model.popularity_score,
        reasons: ['Popular choice']
      }))
  }

  const recommendations = evModels
    .filter(model => model.production_status === 'current')
    .map(model => {
      const matchScore = calculateMatchScore(model, preferences)
      const reasons = getMatchReasons(model, preferences)
      
      return {
        ...model,
        matchScore,
        reasons
      }
    })
    .sort((a, b) => b.matchScore - a.matchScore)
    .slice(0, limit)

  return recommendations
}

/**
 * Get reasons why an EV model matches user preferences
 */
export function getMatchReasons(
  evModel: EVModel,
  preferences: UserEVPreferences
): string[] {
  const reasons: string[] = []

  // Budget match
  if (evModel.price_msrp && preferences.budget_min && preferences.budget_max) {
    if (evModel.price_msrp >= preferences.budget_min && evModel.price_msrp <= preferences.budget_max) {
      reasons.push('Fits your budget')
    }
  }

  // Range requirement
  if (evModel.range_epa_miles && preferences.range_requirement_miles) {
    if (evModel.range_epa_miles >= preferences.range_requirement_miles) {
      const excess = evModel.range_epa_miles - preferences.range_requirement_miles
      if (excess > 50) {
        reasons.push('Exceeds your range needs')
      } else {
        reasons.push('Meets your range requirement')
      }
    }
  }

  // Body type preference
  if (evModel.body_type && preferences.body_type_preferences?.includes(evModel.body_type)) {
    reasons.push(`Matches your ${evModel.body_type} preference`)
  }

  // Use case specific reasons
  if (preferences.use_case) {
    const useCaseReasons = getUseCaseReasons(evModel, preferences.use_case)
    reasons.push(...useCaseReasons)
  }

  // Charging at home
  if (preferences.charging_at_home && evModel.charging_speed_ac_kw) {
    if (evModel.charging_speed_ac_kw >= 11) {
      reasons.push('Fast home charging capability')
    } else {
      reasons.push('Good for home charging')
    }
  }

  // Daily driving
  if (preferences.daily_driving_miles && evModel.range_epa_miles) {
    const daysOfRange = Math.floor(evModel.range_epa_miles / preferences.daily_driving_miles)
    if (daysOfRange >= 7) {
      reasons.push('Week+ of driving on single charge')
    } else if (daysOfRange >= 3) {
      reasons.push('Several days of driving per charge')
    }
  }

  // Special badges
  if (evModel.is_featured) {
    reasons.push('Featured model')
  }
  
  if (evModel.best_value) {
    reasons.push('Best value pick')
  }
  
  if (evModel.editor_choice) {
    reasons.push("Editor's choice")
  }

  return reasons.slice(0, 3) // Limit to top 3 reasons
}

/**
 * Get use case specific reasons
 */
function getUseCaseReasons(evModel: EVModel, useCase: UseCase): string[] {
  const reasons: string[] = []

  switch (useCase) {
    case 'commuting':
      if (evModel.efficiency_mpge && evModel.efficiency_mpge > 120) {
        reasons.push('Excellent efficiency for commuting')
      }
      if (evModel.range_epa_miles && evModel.range_epa_miles > 300) {
        reasons.push('Long range for worry-free commuting')
      }
      break

    case 'family':
      if (evModel.seating_capacity && evModel.seating_capacity >= 7) {
        reasons.push('Seats 7+ passengers')
      } else if (evModel.seating_capacity && evModel.seating_capacity >= 5) {
        reasons.push('Family-friendly seating')
      }
      if (evModel.safety_ratings?.nhtsa_overall && evModel.safety_ratings.nhtsa_overall >= 5) {
        reasons.push('Top safety ratings')
      }
      if (evModel.cargo_volume_cubic_ft && evModel.cargo_volume_cubic_ft > 25) {
        reasons.push('Spacious cargo area')
      }
      break

    case 'performance':
      if (evModel.acceleration_0_60_mph && evModel.acceleration_0_60_mph < 4.0) {
        reasons.push('Sub-4 second acceleration')
      } else if (evModel.acceleration_0_60_mph && evModel.acceleration_0_60_mph < 6.0) {
        reasons.push('Quick acceleration')
      }
      if (evModel.motor_power_hp && evModel.motor_power_hp > 400) {
        reasons.push('High performance power')
      }
      break

    case 'luxury':
      if (evModel.price_msrp && evModel.price_msrp > 7500000) {
        reasons.push('Premium luxury vehicle')
      }
      if (evModel.features && Object.keys(evModel.features).length > 10) {
        reasons.push('Loaded with luxury features')
      }
      break

    case 'utility':
      if (evModel.body_type === 'truck') {
        reasons.push('Pickup truck utility')
      }
      if (evModel.cargo_volume_cubic_ft && evModel.cargo_volume_cubic_ft > 30) {
        reasons.push('Excellent cargo capacity')
      }
      break

    case 'eco_friendly':
      if (evModel.efficiency_mpge && evModel.efficiency_mpge > 130) {
        reasons.push('Outstanding energy efficiency')
      }
      break
  }

  return reasons
}

/**
 * Find similar EV models based on specifications
 */
export function findSimilarEVs(
  targetModel: EVModel,
  allModels: EVModel[],
  limit: number = 6
): EVModel[] {
  const priceRange = targetModel.price_msrp ? targetModel.price_msrp * 0.3 : 1000000 // 30% price range
  
  return allModels
    .filter(model => 
      model.id !== targetModel.id &&
      model.production_status === 'current' &&
      model.body_type === targetModel.body_type
    )
    .map(model => ({
      ...model,
      similarity: calculateSimilarityScore(targetModel, model)
    }))
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, limit)
}

/**
 * Calculate similarity score between two EV models
 */
function calculateSimilarityScore(model1: EVModel, model2: EVModel): number {
  let score = 0
  let factors = 0

  // Price similarity (30% weight)
  if (model1.price_msrp && model2.price_msrp) {
    const priceDiff = Math.abs(model1.price_msrp - model2.price_msrp)
    const avgPrice = (model1.price_msrp + model2.price_msrp) / 2
    const priceScore = Math.max(0, 100 - (priceDiff / avgPrice) * 100)
    score += priceScore * 0.3
    factors += 0.3
  }

  // Range similarity (25% weight)
  if (model1.range_epa_miles && model2.range_epa_miles) {
    const rangeDiff = Math.abs(model1.range_epa_miles - model2.range_epa_miles)
    const avgRange = (model1.range_epa_miles + model2.range_epa_miles) / 2
    const rangeScore = Math.max(0, 100 - (rangeDiff / avgRange) * 100)
    score += rangeScore * 0.25
    factors += 0.25
  }

  // Performance similarity (20% weight)
  if (model1.acceleration_0_60_mph && model2.acceleration_0_60_mph) {
    const perfDiff = Math.abs(model1.acceleration_0_60_mph - model2.acceleration_0_60_mph)
    const perfScore = Math.max(0, 100 - (perfDiff / 10) * 100)
    score += perfScore * 0.2
    factors += 0.2
  }

  // Seating capacity similarity (15% weight)
  if (model1.seating_capacity && model2.seating_capacity) {
    const seatScore = model1.seating_capacity === model2.seating_capacity ? 100 : 50
    score += seatScore * 0.15
    factors += 0.15
  }

  // Body type match (10% weight) - already filtered, so always matches
  score += 100 * 0.1
  factors += 0.1

  return factors > 0 ? score / factors : 0
}

/**
 * Get EV models by category
 */
export function getEVsByCategory(
  evModels: EVModel[],
  category: 'best_value' | 'featured' | 'performance' | 'family' | 'luxury' | 'efficient',
  limit: number = 8
): EVModel[] {
  let filtered: EVModel[]

  switch (category) {
    case 'best_value':
      filtered = evModels.filter(model => model.best_value && model.production_status === 'current')
      break
    
    case 'featured':
      filtered = evModels.filter(model => model.is_featured && model.production_status === 'current')
      break
    
    case 'performance':
      filtered = evModels
        .filter(model => 
          model.production_status === 'current' &&
          model.acceleration_0_60_mph && 
          model.acceleration_0_60_mph < 5.0
        )
        .sort((a, b) => (a.acceleration_0_60_mph || 999) - (b.acceleration_0_60_mph || 999))
      break
    
    case 'family':
      filtered = evModels
        .filter(model => 
          model.production_status === 'current' &&
          (model.body_type === 'suv' || model.body_type === 'crossover') &&
          model.seating_capacity && 
          model.seating_capacity >= 5
        )
        .sort((a, b) => (b.seating_capacity || 0) - (a.seating_capacity || 0))
      break
    
    case 'luxury':
      filtered = evModels
        .filter(model => 
          model.production_status === 'current' &&
          model.price_msrp && 
          model.price_msrp > 7500000
        )
        .sort((a, b) => (b.price_msrp || 0) - (a.price_msrp || 0))
      break
    
    case 'efficient':
      filtered = evModels
        .filter(model => 
          model.production_status === 'current' &&
          model.efficiency_mpge && 
          model.efficiency_mpge > 100
        )
        .sort((a, b) => (b.efficiency_mpge || 0) - (a.efficiency_mpge || 0))
      break
    
    default:
      filtered = evModels.filter(model => model.production_status === 'current')
  }

  return filtered.slice(0, limit)
}

/**
 * Generate personalized EV recommendations based on lifestyle
 */
export function getPersonalizedRecommendations(
  evModels: EVModel[],
  preferences: UserEVPreferences
): {
  primary: EVModel[]
  alternatives: EVModel[]
  budgetOptions: EVModel[]
} {
  const currentModels = evModels.filter(model => model.production_status === 'current')
  
  // Primary recommendations based on full preferences
  const primary = getRecommendedEVs(currentModels, preferences, 3)
  
  // Alternative recommendations with relaxed criteria
  const relaxedPrefs = {
    ...preferences,
    budget_max: preferences.budget_max ? preferences.budget_max * 1.2 : null,
    range_requirement_miles: preferences.range_requirement_miles ? 
      Math.max(200, preferences.range_requirement_miles * 0.8) : null
  }
  const alternatives = getRecommendedEVs(currentModels, relaxedPrefs, 3)
    .filter(model => !primary.some(p => p.id === model.id))
  
  // Budget-friendly options
  const budgetOptions = currentModels
    .filter(model => 
      model.price_msrp && 
      model.price_msrp < (preferences.budget_max || 5000000) &&
      model.best_value
    )
    .sort((a, b) => (a.price_msrp || 0) - (b.price_msrp || 0))
    .slice(0, 3)
    .filter(model => 
      !primary.some(p => p.id === model.id) && 
      !alternatives.some(a => a.id === model.id)
    )

  return {
    primary,
    alternatives,
    budgetOptions
  }
}
