// Database types for GreenMilesEV
export interface Database {
  public: {
    Tables: {
      ev_manufacturers: {
        Row: {
          id: string;
          name: string;
          logo_url: string | null;
          website_url: string | null;
          headquarters_country: string | null;
          founded_year: number | null;
          description: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          logo_url?: string | null;
          website_url?: string | null;
          headquarters_country?: string | null;
          founded_year?: number | null;
          description?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          logo_url?: string | null;
          website_url?: string | null;
          headquarters_country?: string | null;
          founded_year?: number | null;
          description?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      ev_models: {
        Row: {
          id: string;
          manufacturer_id: string | null;
          make: string;
          model: string;
          year: number;
          trim: string | null;
          body_type: string | null;
          price_msrp: number | null;
          price_base: number | null;
          price_as_tested: number | null;
          production_status: string;
          market_regions: string[] | null;
          battery_capacity_kwh: number;
          range_epa_miles: number | null;
          range_wltp_miles: number | null;
          range_real_world_miles: number | null;
          efficiency_mpge: number | null;
          charging_speed_dc_kw: number | null;
          charging_speed_ac_kw: number | null;
          charging_ports: string[] | null;
          charging_time_10_80_minutes: number | null;
          acceleration_0_60_mph: number | null;
          top_speed_mph: number | null;
          motor_power_hp: number | null;
          motor_torque_lb_ft: number | null;
          drivetrain: string | null;
          length_inches: number | null;
          width_inches: number | null;
          height_inches: number | null;
          wheelbase_inches: number | null;
          ground_clearance_inches: number | null;
          cargo_volume_cubic_ft: number | null;
          seating_capacity: number | null;
          curb_weight_lbs: number | null;
          features: Record<string, any> | null;
          safety_ratings: Record<string, any> | null;
          warranty_info: Record<string, any> | null;
          total_cost_ownership: Record<string, any> | null;
          user_reviews_summary: Record<string, any> | null;
          pros_cons: Record<string, any> | null;
          images: string[] | null;
          videos: string[] | null;
          brochure_url: string | null;
          is_featured: boolean;
          popularity_score: number;
          editor_choice: boolean;
          best_value: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          manufacturer_id?: string | null;
          make: string;
          model: string;
          year: number;
          trim?: string | null;
          body_type?: string | null;
          price_msrp?: number | null;
          price_base?: number | null;
          price_as_tested?: number | null;
          production_status?: string;
          market_regions?: string[] | null;
          battery_capacity_kwh: number;
          range_epa_miles?: number | null;
          range_wltp_miles?: number | null;
          range_real_world_miles?: number | null;
          efficiency_mpge?: number | null;
          charging_speed_dc_kw?: number | null;
          charging_speed_ac_kw?: number | null;
          charging_ports?: string[] | null;
          charging_time_10_80_minutes?: number | null;
          acceleration_0_60_mph?: number | null;
          top_speed_mph?: number | null;
          motor_power_hp?: number | null;
          motor_torque_lb_ft?: number | null;
          drivetrain?: string | null;
          length_inches?: number | null;
          width_inches?: number | null;
          height_inches?: number | null;
          wheelbase_inches?: number | null;
          ground_clearance_inches?: number | null;
          cargo_volume_cubic_ft?: number | null;
          seating_capacity?: number | null;
          curb_weight_lbs?: number | null;
          features?: Record<string, any> | null;
          safety_ratings?: Record<string, any> | null;
          warranty_info?: Record<string, any> | null;
          total_cost_ownership?: Record<string, any> | null;
          user_reviews_summary?: Record<string, any> | null;
          pros_cons?: Record<string, any> | null;
          images?: string[] | null;
          videos?: string[] | null;
          brochure_url?: string | null;
          is_featured?: boolean;
          popularity_score?: number;
          editor_choice?: boolean;
          best_value?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          manufacturer_id?: string | null;
          make?: string;
          model?: string;
          year?: number;
          trim?: string | null;
          body_type?: string | null;
          price_msrp?: number | null;
          price_base?: number | null;
          price_as_tested?: number | null;
          production_status?: string;
          market_regions?: string[] | null;
          battery_capacity_kwh?: number;
          range_epa_miles?: number | null;
          range_wltp_miles?: number | null;
          range_real_world_miles?: number | null;
          efficiency_mpge?: number | null;
          charging_speed_dc_kw?: number | null;
          charging_speed_ac_kw?: number | null;
          charging_ports?: string[] | null;
          charging_time_10_80_minutes?: number | null;
          acceleration_0_60_mph?: number | null;
          top_speed_mph?: number | null;
          motor_power_hp?: number | null;
          motor_torque_lb_ft?: number | null;
          drivetrain?: string | null;
          length_inches?: number | null;
          width_inches?: number | null;
          height_inches?: number | null;
          wheelbase_inches?: number | null;
          ground_clearance_inches?: number | null;
          cargo_volume_cubic_ft?: number | null;
          seating_capacity?: number | null;
          curb_weight_lbs?: number | null;
          features?: Record<string, any> | null;
          safety_ratings?: Record<string, any> | null;
          warranty_info?: Record<string, any> | null;
          total_cost_ownership?: Record<string, any> | null;
          user_reviews_summary?: Record<string, any> | null;
          pros_cons?: Record<string, any> | null;
          images?: string[] | null;
          videos?: string[] | null;
          brochure_url?: string | null;
          is_featured?: boolean;
          popularity_score?: number;
          editor_choice?: boolean;
          best_value?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          phone: string | null;
          address: string | null;
          city: string | null;
          state: string | null;
          zip_code: string | null;
          country: string;
          preferences: Record<string, any>;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          phone?: string | null;
          address?: string | null;
          city?: string | null;
          state?: string | null;
          zip_code?: string | null;
          country?: string;
          preferences?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          phone?: string | null;
          address?: string | null;
          city?: string | null;
          state?: string | null;
          zip_code?: string | null;
          country?: string;
          preferences?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
      };
      vehicles: {
        Row: {
          id: string;
          user_id: string;
          make: string;
          model: string;
          year: number;
          trim: string | null;
          color: string | null;
          vin: string | null;
          license_plate: string | null;
          battery_capacity_kwh: number;
          range_miles: number;
          efficiency_miles_per_kwh: number;
          current_battery_level: number;
          current_range_miles: number;
          odometer_miles: number;
          is_primary: boolean;
          status: "active" | "inactive" | "maintenance";
          metadata: Record<string, any>;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          make: string;
          model: string;
          year: number;
          trim?: string | null;
          color?: string | null;
          vin?: string | null;
          license_plate?: string | null;
          battery_capacity_kwh: number;
          range_miles: number;
          efficiency_miles_per_kwh?: number;
          current_battery_level?: number;
          current_range_miles?: number;
          odometer_miles?: number;
          is_primary?: boolean;
          status?: "active" | "inactive" | "maintenance";
          metadata?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          make?: string;
          model?: string;
          year?: number;
          trim?: string | null;
          color?: string | null;
          vin?: string | null;
          license_plate?: string | null;
          battery_capacity_kwh?: number;
          range_miles?: number;
          efficiency_miles_per_kwh?: number;
          current_battery_level?: number;
          current_range_miles?: number;
          odometer_miles?: number;
          is_primary?: boolean;
          status?: "active" | "inactive" | "maintenance";
          metadata?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
      };
      charging_stations: {
        Row: {
          id: string;
          name: string;
          network: string;
          address: string;
          city: string;
          state: string;
          zip_code: string | null;
          country: string;
          latitude: number;
          longitude: number;
          total_stalls: number;
          available_stalls: number;
          max_power_kw: number;
          connector_types: string[];
          pricing_per_kwh: number | null;
          pricing_per_minute: number | null;
          amenities: string[];
          hours_of_operation: string | null;
          status: "operational" | "maintenance" | "offline";
          last_updated: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          network: string;
          address: string;
          city: string;
          state: string;
          zip_code?: string | null;
          country?: string;
          latitude: number;
          longitude: number;
          total_stalls?: number;
          available_stalls?: number;
          max_power_kw: number;
          connector_types: string[];
          pricing_per_kwh?: number | null;
          pricing_per_minute?: number | null;
          amenities?: string[];
          hours_of_operation?: string | null;
          status?: "operational" | "maintenance" | "offline";
          last_updated?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          network?: string;
          address?: string;
          city?: string;
          state?: string;
          zip_code?: string | null;
          country?: string;
          latitude?: number;
          longitude?: number;
          total_stalls?: number;
          available_stalls?: number;
          max_power_kw?: number;
          connector_types?: string[];
          pricing_per_kwh?: number | null;
          pricing_per_minute?: number | null;
          amenities?: string[];
          hours_of_operation?: string | null;
          status?: "operational" | "maintenance" | "offline";
          last_updated?: string;
          created_at?: string;
        };
      };
      charging_sessions: {
        Row: {
          id: string;
          user_id: string;
          vehicle_id: string;
          charging_station_id: string | null;
          session_type: "home" | "public" | "workplace";
          start_time: string;
          end_time: string | null;
          start_battery_level: number;
          end_battery_level: number | null;
          energy_added_kwh: number | null;
          cost_total: number | null;
          cost_per_kwh: number | null;
          power_kw: number | null;
          status: "in_progress" | "completed" | "interrupted" | "failed";
          notes: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          vehicle_id: string;
          charging_station_id?: string | null;
          session_type: "home" | "public" | "workplace";
          start_time: string;
          end_time?: string | null;
          start_battery_level: number;
          end_battery_level?: number | null;
          energy_added_kwh?: number | null;
          cost_total?: number | null;
          cost_per_kwh?: number | null;
          power_kw?: number | null;
          status?: "in_progress" | "completed" | "interrupted" | "failed";
          notes?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          vehicle_id?: string;
          charging_station_id?: string | null;
          session_type?: "home" | "public" | "workplace";
          start_time?: string;
          end_time?: string | null;
          start_battery_level?: number;
          end_battery_level?: number | null;
          energy_added_kwh?: number | null;
          cost_total?: number | null;
          cost_per_kwh?: number | null;
          power_kw?: number | null;
          status?: "in_progress" | "completed" | "interrupted" | "failed";
          notes?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      trips: {
        Row: {
          id: string;
          user_id: string;
          vehicle_id: string;
          start_location: string;
          end_location: string;
          start_latitude: number | null;
          start_longitude: number | null;
          end_latitude: number | null;
          end_longitude: number | null;
          start_time: string;
          end_time: string | null;
          distance_miles: number | null;
          duration_minutes: number | null;
          start_battery_level: number | null;
          end_battery_level: number | null;
          energy_used_kwh: number | null;
          efficiency_miles_per_kwh: number | null;
          average_speed_mph: number | null;
          weather_conditions: string | null;
          trip_type: "commute" | "leisure" | "business" | "other" | null;
          notes: string | null;
          status: "in_progress" | "completed" | "cancelled";
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          vehicle_id: string;
          start_location: string;
          end_location: string;
          start_latitude?: number | null;
          start_longitude?: number | null;
          end_latitude?: number | null;
          end_longitude?: number | null;
          start_time: string;
          end_time?: string | null;
          distance_miles?: number | null;
          duration_minutes?: number | null;
          start_battery_level?: number | null;
          end_battery_level?: number | null;
          energy_used_kwh?: number | null;
          efficiency_miles_per_kwh?: number | null;
          average_speed_mph?: number | null;
          weather_conditions?: string | null;
          trip_type?: "commute" | "leisure" | "business" | "other" | null;
          notes?: string | null;
          status?: "in_progress" | "completed" | "cancelled";
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          vehicle_id?: string;
          start_location?: string;
          end_location?: string;
          start_latitude?: number | null;
          start_longitude?: number | null;
          end_latitude?: number | null;
          end_longitude?: number | null;
          start_time?: string;
          end_time?: string | null;
          distance_miles?: number | null;
          duration_minutes?: number | null;
          start_battery_level?: number | null;
          end_battery_level?: number | null;
          energy_used_kwh?: number | null;
          efficiency_miles_per_kwh?: number | null;
          average_speed_mph?: number | null;
          weather_conditions?: string | null;
          trip_type?: "commute" | "leisure" | "business" | "other" | null;
          notes?: string | null;
          status?: "in_progress" | "completed" | "cancelled";
          created_at?: string;
          updated_at?: string;
        };
      };
      maintenance_records: {
        Row: {
          id: string;
          user_id: string;
          vehicle_id: string;
          service_type: string;
          description: string;
          service_date: string;
          odometer_miles: number | null;
          cost: number | null;
          service_provider: string | null;
          next_service_miles: number | null;
          next_service_date: string | null;
          status: "scheduled" | "completed" | "overdue";
          notes: string | null;
          attachments: string[];
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          vehicle_id: string;
          service_type: string;
          description: string;
          service_date: string;
          odometer_miles?: number | null;
          cost?: number | null;
          service_provider?: string | null;
          next_service_miles?: number | null;
          next_service_date?: string | null;
          status?: "scheduled" | "completed" | "overdue";
          notes?: string | null;
          attachments?: string[];
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          vehicle_id?: string;
          service_type?: string;
          description?: string;
          service_date?: string;
          odometer_miles?: number | null;
          cost?: number | null;
          service_provider?: string | null;
          next_service_miles?: number | null;
          next_service_date?: string | null;
          status?: "scheduled" | "completed" | "overdue";
          notes?: string | null;
          attachments?: string[];
          created_at?: string;
          updated_at?: string;
        };
      };
      user_favorites: {
        Row: {
          id: string;
          user_id: string;
          ev_model_id: string;
          notes: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          ev_model_id: string;
          notes?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          ev_model_id?: string;
          notes?: string | null;
          created_at?: string;
        };
      };
      comparison_sessions: {
        Row: {
          id: string;
          user_id: string;
          name: string | null;
          ev_model_ids: string[] | null;
          user_priorities: Record<string, any> | null;
          comparison_notes: string | null;
          is_public: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name?: string | null;
          ev_model_ids?: string[] | null;
          user_priorities?: Record<string, any> | null;
          comparison_notes?: string | null;
          is_public?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string | null;
          ev_model_ids?: string[] | null;
          user_priorities?: Record<string, any> | null;
          comparison_notes?: string | null;
          is_public?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      user_ev_preferences: {
        Row: {
          id: string;
          user_id: string;
          budget_min: number | null;
          budget_max: number | null;
          range_requirement_miles: number | null;
          body_type_preferences: string[] | null;
          use_case: string | null;
          charging_at_home: boolean | null;
          daily_driving_miles: number | null;
          priority_weights: Record<string, any> | null;
          lifestyle_data: Record<string, any> | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          budget_min?: number | null;
          budget_max?: number | null;
          range_requirement_miles?: number | null;
          body_type_preferences?: string[] | null;
          use_case?: string | null;
          charging_at_home?: boolean | null;
          daily_driving_miles?: number | null;
          priority_weights?: Record<string, any> | null;
          lifestyle_data?: Record<string, any> | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          budget_min?: number | null;
          budget_max?: number | null;
          range_requirement_miles?: number | null;
          body_type_preferences?: string[] | null;
          use_case?: string | null;
          charging_at_home?: boolean | null;
          daily_driving_miles?: number | null;
          priority_weights?: Record<string, any> | null;
          lifestyle_data?: Record<string, any> | null;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}
