'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  DollarSign,
  Calculator,
  TrendingDown,
  TrendingUp,
  Fuel,
  Zap,
  Wrench,
  Shield,
  PiggyBank,
  CreditCard,
  Calendar,
  Info,
  CheckCircle,
} from 'lucide-react'
import { formatPrice } from '@/utils/ev-buyer-guide'
import type { EVModelWithDetails } from '../../../shared/types'

interface EVModelCostAnalysisProps {
  evModel: EVModelWithDetails
}

interface CostCalculatorInputs {
  annualMiles: number
  electricityRate: number // cents per kWh
  gasPrice: number // per gallon
  loanTerm: number // years
  downPayment: number // percentage
  interestRate: number // percentage
  tradeInValue: number // dollars
}

interface CostBreakdown {
  purchase: number
  financing: number
  insurance: number
  maintenance: number
  fuel: number
  depreciation: number
  total: number
}

export function EVModelCostAnalysis({ evModel }: EVModelCostAnalysisProps) {
  const [inputs, setInputs] = useState<CostCalculatorInputs>({
    annualMiles: 12000,
    electricityRate: 13, // US average
    gasPrice: 3.5, // US average
    loanTerm: 5,
    downPayment: 20,
    interestRate: 6.5,
    tradeInValue: 0,
  })

  const [showCalculator, setShowCalculator] = useState(false)

  // Calculate financing details
  const calculateFinancing = () => {
    if (!evModel.price_msrp) return null

    const vehiclePrice = evModel.price_msrp / 100 // Convert from cents
    const downPaymentAmount = vehiclePrice * (inputs.downPayment / 100)
    const loanAmount = vehiclePrice - downPaymentAmount - inputs.tradeInValue
    const monthlyRate = inputs.interestRate / 100 / 12
    const numPayments = inputs.loanTerm * 12

    const monthlyPayment =
      (loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments))) /
      (Math.pow(1 + monthlyRate, numPayments) - 1)

    const totalInterest = monthlyPayment * numPayments - loanAmount

    return {
      vehiclePrice,
      downPaymentAmount,
      loanAmount,
      monthlyPayment,
      totalInterest,
      totalFinanced: loanAmount + totalInterest,
    }
  }

  // Calculate 5-year total cost of ownership
  const calculateTCO = (): { ev: CostBreakdown; gas: CostBreakdown } => {
    const financing = calculateFinancing()
    const years = 5

    // EV Costs
    const evPurchase = financing?.vehiclePrice || 0
    const evFinancing = financing?.totalInterest || 0
    const evInsurance = 1200 * years // Slightly higher for EVs
    const evMaintenance = 400 * years // Much lower maintenance
    const evFuelCost =
      ((inputs.annualMiles * years * (evModel.energy_consumption_kwh_100mi || 30)) / 100) *
      (inputs.electricityRate / 100)
    const evDepreciation = evPurchase * 0.6 // 60% depreciation over 5 years

    // Comparable Gas Car Costs (estimated)
    const gasPurchase = evPurchase * 0.75 // Gas cars typically cost less
    const gasFinancing = evFinancing * 0.75
    const gasInsurance = 1100 * years
    const gasMaintenance = 1200 * years // Higher maintenance
    const gasFuelCost = ((inputs.annualMiles * years) / 25) * inputs.gasPrice // Assume 25 MPG
    const gasDepreciation = gasPurchase * 0.65 // Slightly higher depreciation

    return {
      ev: {
        purchase: evPurchase,
        financing: evFinancing,
        insurance: evInsurance,
        maintenance: evMaintenance,
        fuel: evFuelCost,
        depreciation: evDepreciation,
        total: evPurchase + evFinancing + evInsurance + evMaintenance + evFuelCost,
      },
      gas: {
        purchase: gasPurchase,
        financing: gasFinancing,
        insurance: gasInsurance,
        maintenance: gasMaintenance,
        fuel: gasFuelCost,
        depreciation: gasDepreciation,
        total: gasPurchase + gasFinancing + gasInsurance + gasMaintenance + gasFuelCost,
      },
    }
  }

  const financing = calculateFinancing()
  const tco = calculateTCO()
  const savings = tco.gas.total - tco.ev.total

  // Federal tax credit (simplified)
  const federalTaxCredit = evModel.price_msrp && evModel.price_msrp <= 5500000 ? 7500 : 0

  return (
    <div className="space-y-8">
      {/* Purchase Price Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Purchase Price Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">MSRP</span>
                <span className="text-lg font-semibold">{formatPrice(evModel.price_msrp)}</span>
              </div>

              {evModel.price_base && evModel.price_base !== evModel.price_msrp && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Base Price</span>
                  <span className="font-semibold">{formatPrice(evModel.price_base)}</span>
                </div>
              )}

              {federalTaxCredit > 0 && (
                <div className="flex justify-between">
                  <span className="text-green-600 dark:text-green-400">Federal Tax Credit</span>
                  <span className="font-semibold text-green-600">
                    -${federalTaxCredit.toLocaleString()}
                  </span>
                </div>
              )}

              <div className="border-t pt-2">
                <div className="flex justify-between">
                  <span className="font-semibold">Effective Price</span>
                  <span className="text-lg font-bold">
                    {formatPrice((evModel.price_msrp || 0) - federalTaxCredit * 100)}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold">Potential Incentives</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Federal tax credit up to $7,500</span>
                </div>
                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4 text-blue-600" />
                  <span>State and local rebates vary</span>
                </div>
                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4 text-blue-600" />
                  <span>Utility company incentives</span>
                </div>
                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4 text-blue-600" />
                  <span>HOV lane access in some areas</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Financing Calculator */}
      {financing && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Financing Calculator
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div>
                  <Label htmlFor="downPayment">Down Payment (%)</Label>
                  <Input
                    id="downPayment"
                    type="number"
                    value={inputs.downPayment}
                    onChange={(e) => setInputs({ ...inputs, downPayment: Number(e.target.value) })}
                    min="0"
                    max="100"
                  />
                </div>
                <div>
                  <Label htmlFor="loanTerm">Loan Term (years)</Label>
                  <Input
                    id="loanTerm"
                    type="number"
                    value={inputs.loanTerm}
                    onChange={(e) => setInputs({ ...inputs, loanTerm: Number(e.target.value) })}
                    min="1"
                    max="8"
                  />
                </div>
                <div>
                  <Label htmlFor="interestRate">Interest Rate (%)</Label>
                  <Input
                    id="interestRate"
                    type="number"
                    step="0.1"
                    value={inputs.interestRate}
                    onChange={(e) => setInputs({ ...inputs, interestRate: Number(e.target.value) })}
                    min="0"
                    max="20"
                  />
                </div>
                <div>
                  <Label htmlFor="tradeInValue">Trade-in Value ($)</Label>
                  <Input
                    id="tradeInValue"
                    type="number"
                    value={inputs.tradeInValue}
                    onChange={(e) => setInputs({ ...inputs, tradeInValue: Number(e.target.value) })}
                    min="0"
                  />
                </div>
              </div>

              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-3">
                  <h4 className="font-semibold">Loan Details</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Vehicle Price</span>
                      <span className="font-semibold">
                        ${financing.vehiclePrice.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Down Payment</span>
                      <span className="font-semibold">
                        ${financing.downPaymentAmount.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Trade-in Value</span>
                      <span className="font-semibold">${inputs.tradeInValue.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="font-semibold">Loan Amount</span>
                      <span className="font-semibold">
                        ${financing.loanAmount.toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-semibold">Monthly Payment</h4>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-electric-600">
                      ${Math.round(financing.monthlyPayment).toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">per month</div>
                  </div>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Total Interest</span>
                      <span className="font-semibold">
                        ${Math.round(financing.totalInterest).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Total Financed</span>
                      <span className="font-semibold">
                        ${Math.round(financing.totalFinanced).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Total Cost of Ownership */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            5-Year Total Cost of Ownership
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="annualMiles">Annual Miles</Label>
                <Input
                  id="annualMiles"
                  type="number"
                  value={inputs.annualMiles}
                  onChange={(e) => setInputs({ ...inputs, annualMiles: Number(e.target.value) })}
                  min="1000"
                  max="50000"
                />
              </div>
              <div>
                <Label htmlFor="electricityRate">Electricity Rate (¢/kWh)</Label>
                <Input
                  id="electricityRate"
                  type="number"
                  step="0.1"
                  value={inputs.electricityRate}
                  onChange={(e) =>
                    setInputs({ ...inputs, electricityRate: Number(e.target.value) })
                  }
                  min="5"
                  max="50"
                />
              </div>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              {/* EV Costs */}
              <div className="rounded-lg border p-4">
                <h4 className="mb-4 flex items-center gap-2 font-semibold text-electric-600">
                  <Zap className="h-4 w-4" />
                  This EV ({evModel.make} {evModel.model})
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Purchase Price</span>
                    <span className="font-semibold">
                      ${Math.round(tco.ev.purchase).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Financing Cost</span>
                    <span className="font-semibold">
                      ${Math.round(tco.ev.financing).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Insurance (5 years)</span>
                    <span className="font-semibold">
                      ${Math.round(tco.ev.insurance).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Maintenance (5 years)</span>
                    <span className="font-semibold">
                      ${Math.round(tco.ev.maintenance).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Electricity (5 years)</span>
                    <span className="font-semibold">
                      ${Math.round(tco.ev.fuel).toLocaleString()}
                    </span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between">
                      <span className="font-semibold">Total 5-Year Cost</span>
                      <span className="text-lg font-bold">
                        ${Math.round(tco.ev.total).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Gas Car Comparison */}
              <div className="rounded-lg border p-4">
                <h4 className="mb-4 flex items-center gap-2 font-semibold text-gray-700 dark:text-gray-300">
                  <Fuel className="h-4 w-4" />
                  Comparable Gas Car
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Purchase Price</span>
                    <span className="font-semibold">
                      ${Math.round(tco.gas.purchase).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Financing Cost</span>
                    <span className="font-semibold">
                      ${Math.round(tco.gas.financing).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Insurance (5 years)</span>
                    <span className="font-semibold">
                      ${Math.round(tco.gas.insurance).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Maintenance (5 years)</span>
                    <span className="font-semibold">
                      ${Math.round(tco.gas.maintenance).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Gasoline (5 years)</span>
                    <span className="font-semibold">
                      ${Math.round(tco.gas.fuel).toLocaleString()}
                    </span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between">
                      <span className="font-semibold">Total 5-Year Cost</span>
                      <span className="text-lg font-bold">
                        ${Math.round(tco.gas.total).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Savings Summary */}
            <div className="dark:bg-electric-950 rounded-lg bg-electric-50 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-electric-900 dark:text-electric-100">
                    5-Year Savings with EV
                  </h4>
                  <p className="text-sm text-electric-800 dark:text-electric-200">
                    Compared to a similar gas vehicle
                  </p>
                </div>
                <div className="text-right">
                  <div
                    className={`text-2xl font-bold ${savings > 0 ? 'text-green-600' : 'text-red-600'}`}
                  >
                    {savings > 0 ? '+' : ''}${Math.round(Math.abs(savings)).toLocaleString()}
                  </div>
                  <div className="text-sm text-electric-800 dark:text-electric-200">
                    {savings > 0 ? 'savings' : 'additional cost'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
