import React from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { EVModel } from "@/types";

interface ComparisonRecommendationsProps {
  models: EVModel[];
}

const formatPrice = (price: number | null) => {
  if (price === null || price === undefined) return "Price TBA";
  return `$${price.toLocaleString()}`;
};

const formatRange = (range: number | null) => {
  if (range === null || range === undefined) return "N/A";
  return `${range} mi`;
};

const InsightCard = ({ insight }: { insight: any }) => (
  <View style={[styles.card, { borderColor: insight.color.border }]}>
    <View style={styles.cardHeader}>
      <View
        style={[
          styles.iconContainer,
          { backgroundColor: insight.color.background },
        ]}
      >
        <Ionicons name={insight.icon} size={20} color={insight.color.text} />
      </View>
      <View style={styles.cardHeaderText}>
        <Text style={styles.cardTitle}>{insight.title}</Text>
      </View>
    </View>
    <Text style={styles.cardDescription}>{insight.description}</Text>
    <View
      style={[
        styles.recommendationBox,
        { backgroundColor: insight.color.background },
      ]}
    >
      <Text style={[styles.recommendationText, { color: insight.color.text }]}>
        {insight.recommendation}
      </Text>
    </View>
  </View>
);

const RecommendationCard = ({ recommendation }: { recommendation: any }) => (
  <View style={[styles.card, { borderColor: recommendation.color.border }]}>
    <View style={styles.cardHeader}>
      <View
        style={[
          styles.iconContainer,
          { backgroundColor: recommendation.color.background },
        ]}
      >
        <Ionicons
          name={recommendation.icon}
          size={20}
          color={recommendation.color.text}
        />
      </View>
      <View style={styles.cardHeaderText}>
        <Text style={styles.cardTitle}>{recommendation.title}</Text>
      </View>
    </View>
    <TouchableOpacity
      onPress={() => router.push(`/ev-models/${recommendation.model.id}`)}
    >
      <View style={styles.modelContainer}>
        <Text style={styles.modelName}>
          {recommendation.model.make} {recommendation.model.model}
        </Text>
        <Text style={styles.modelReason}>{recommendation.reason}</Text>
        <View style={styles.modelSpecs}>
          <Text style={styles.specText}>
            {formatPrice(recommendation.model.price_msrp)}
          </Text>
          <Text style={styles.specText}>
            {formatRange(recommendation.model.range_epa_miles)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  </View>
);

export function ComparisonRecommendations({
  models,
}: ComparisonRecommendationsProps) {
  const generateInsights = () => {
    if (models.length < 2) return [];

    const insights = [];

    // Price analysis
    const prices = models
      .map((m) => m.price_msrp)
      .filter((p) => p !== null) as number[];
    if (prices.length > 1) {
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      const priceDiff = maxPrice - minPrice;
      const cheapestModel = models.find((m) => m.price_msrp === minPrice);
      const mostExpensiveModel = models.find((m) => m.price_msrp === maxPrice);

      if (priceDiff > 0 && cheapestModel && mostExpensiveModel) {
        insights.push({
          type: "price",
          icon: "cash" as keyof typeof Ionicons.glyphMap,
          title: "Price Range Analysis",
          description: `There is a price difference of ${formatPrice(
            priceDiff
          )} between the ${cheapestModel.make} ${cheapestModel.model} and the ${
            mostExpensiveModel.make
          } ${mostExpensiveModel.model}.`,
          recommendation:
            priceDiff > 20000
              ? "Consider if the premium features on the more expensive model justify the significant cost increase."
              : "The prices are relatively close. Focus on other aspects like features, range, and performance to make your decision.",
          color: { background: "#F0FDF4", border: "#D1FAE5", text: "#15803D" },
        });
      }
    }

    // Range analysis
    const ranges = models
      .map((m) => m.range_epa_miles)
      .filter((r) => r !== null) as number[];
    if (ranges.length > 1) {
      const minRange = Math.min(...ranges);
      const maxRange = Math.max(...ranges);
      const rangeDiff = maxRange - minRange;
      const shortestRangeModel = models.find(
        (m) => m.range_epa_miles === minRange
      );
      const longestRangeModel = models.find(
        (m) => m.range_epa_miles === maxRange
      );

      if (rangeDiff > 50 && shortestRangeModel && longestRangeModel) {
        insights.push({
          type: "range",
          icon: "speedometer" as keyof typeof Ionicons.glyphMap,
          title: "Range Considerations",
          description: `The ${longestRangeModel.make} ${longestRangeModel.model} offers ${rangeDiff} miles more range than the ${shortestRangeModel.make} ${shortestRangeModel.model}.`,
          recommendation:
            minRange < 250
              ? "The shorter range model may require more frequent charging depending on your daily commute and travel habits."
              : "All selected vehicles offer substantial range, suitable for most drivers including occasional long trips.",
          color: { background: "#EFF6FF", border: "#DBEAFE", text: "#1E40AF" },
        });
      }
    }

    return insights;
  };

  const calculateValueScore = (model: EVModel): number => {
    let score = 0;
    let factors = 0;
    if (model.price_msrp) {
      score += Math.max(0, 100 - model.price_msrp / 1000);
      factors++;
    }
    if (model.range_epa_miles) {
      score += Math.min(100, model.range_epa_miles / 3);
      factors++;
    }
    if (model.efficiency_mpge) {
      score += Math.min(100, model.efficiency_mpge / 1.5);
      factors++;
    }
    return factors > 0 ? score / factors : 0;
  };

  const generateRecommendations = () => {
    if (models.length === 0) return [];

    const recommendations = [];

    const bestValueModel = models.reduce((best, current) => {
      const bestScore = calculateValueScore(best);
      const currentScore = calculateValueScore(current);
      return currentScore > bestScore ? current : best;
    });

    if (bestValueModel) {
      recommendations.push({
        type: "best-value",
        title: "Best Overall Value",
        model: bestValueModel,
        reason:
          "This model offers the best combination of price, range, and efficiency among your selected vehicles.",
        icon: "trophy" as keyof typeof Ionicons.glyphMap,
        color: { background: "#FEFCE8", border: "#FEF9C3", text: "#854D0E" },
      });
    }

    return recommendations;
  };

  const insights = generateInsights();
  const recommendations = generateRecommendations();

  return (
    <ScrollView style={styles.container}>
      {recommendations.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Top Picks For You</Text>
          {recommendations.map((rec) => (
            <RecommendationCard key={rec.type} recommendation={rec} />
          ))}
        </View>
      )}
      {insights.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Insights</Text>
          {insights.map((insight) => (
            <InsightCard key={insight.type} insight={insight} />
          ))}
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  section: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 16,
  },
  card: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  iconContainer: {
    padding: 8,
    borderRadius: 8,
    marginRight: 12,
  },
  cardHeaderText: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
  },
  cardDescription: {
    color: "#374151",
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  recommendationBox: {
    borderRadius: 8,
    padding: 12,
  },
  recommendationText: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: "500",
  },
  modelContainer: {
    marginTop: 8,
  },
  modelName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1E40AF",
  },
  modelReason: {
    fontSize: 14,
    color: "#4B5563",
    marginTop: 4,
    marginBottom: 8,
  },
  modelSpecs: {
    flexDirection: "row",
    justifyContent: "space-around",
    backgroundColor: "#F3F4F6",
    borderRadius: 8,
    paddingVertical: 8,
  },
  specText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#111827",
  },
});
