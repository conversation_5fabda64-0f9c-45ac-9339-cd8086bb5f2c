# GreenMilesEV Web Application

A modern Next.js web application for electric vehicle management built with TypeScript, Tailwind CSS, and shadcn/ui.

## 🚀 Features

- **Modern Stack**: Next.js 14 with App Router, TypeScript, Tailwind CSS
- **UI Components**: shadcn/ui component library for consistent design
- **Authentication**: Supabase Auth integration
- **Database**: Supabase PostgreSQL database
- **Styling**: Tailwind CSS with custom EV-themed color palette
- **Code Quality**: ESLint, Prettier, and TypeScript for code quality

## 📁 Project Structure

```
src/
├── app/                 # Next.js App Router pages
│   ├── globals.css     # Global styles and Tailwind imports
│   ├── layout.tsx      # Root layout component
│   └── page.tsx        # Home page
├── components/         # React components
│   └── ui/            # shadcn/ui components
├── lib/               # Utility functions
│   └── utils.ts       # Common utilities (cn function)
├── hooks/             # Custom React hooks
├── types/             # TypeScript type definitions
└── utils/             # Helper functions
```

## 🛠️ Development

### Prerequisites

- Node.js 18+ and npm 9+
- Supabase account and project

### Setup

1. Navigate to the web app directory:
```bash
cd apps/web
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Update `.env.local` with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

5. Start the development server:
```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## 🎨 Design System

### Colors

The app uses a custom electric vehicle-themed color palette:

- **Electric Green**: Primary brand color for EV-related elements
- **Neutral Grays**: For text and backgrounds
- **Success/Warning/Error**: Standard semantic colors

### Components

Built with shadcn/ui components for consistency:

- **Button**: Various variants (default, outline, ghost, etc.)
- **Card**: For content containers
- **Badge**: For status indicators
- **Form Elements**: Inputs, selects, etc. (to be added)

## 🔧 Configuration

### Tailwind CSS

Custom configuration includes:
- EV-themed color palette
- Custom animations
- Responsive breakpoints
- shadcn/ui integration

### TypeScript

Configured with:
- Strict mode enabled
- Path aliases for clean imports
- Shared types from packages/shared

### ESLint & Prettier

Code quality tools configured with:
- Next.js recommended rules
- TypeScript support
- Tailwind CSS class sorting
- Consistent formatting

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🔗 Integration

### Supabase

The app integrates with Supabase for:
- User authentication
- Database operations
- Real-time subscriptions
- File storage

### Shared Packages

Uses shared types and utilities from `packages/shared` for consistency with the mobile app.

## 📱 Mobile Compatibility

The web app is fully responsive and works well on mobile devices, complementing the dedicated React Native mobile app.
