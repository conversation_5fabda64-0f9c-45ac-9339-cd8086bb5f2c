'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Zap,
  Battery,
  Gauge,
  Car,
  Ruler,
  Weight,
  Users,
  Package,
  Shield,
  Wrench,
  DollarSign,
  Globe,
} from 'lucide-react'
import { formatPrice, formatRange, formatChargingTime } from '@/utils/ev-buyer-guide'
import type { EVModelWithDetails } from '../../../shared/types'

interface EVModelSpecsProps {
  evModel: EVModelWithDetails
}

interface SpecItemProps {
  label: string
  value: string | number | null
  unit?: string
  formatter?: (value: any) => string
}

function SpecItem({ label, value, unit, formatter }: SpecItemProps) {
  const displayValue = formatter ? formatter(value) : value ? `${value}${unit || ''}` : 'N/A'

  return (
    <div className="flex justify-between py-2">
      <span className="text-gray-600 dark:text-gray-400">{label}</span>
      <span className="font-semibold">{displayValue}</span>
    </div>
  )
}

function Separator() {
  return <div className="h-px bg-gray-200 dark:bg-gray-700" />
}

export function EVModelSpecs({ evModel }: EVModelSpecsProps) {
  return (
    <div className="space-y-8">
      {/* Performance & Powertrain */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            <SpecItem label="0-60 mph" value={evModel.acceleration_0_60_mph} unit="s" />
            <Separator />
            <SpecItem label="Top Speed" value={evModel.top_speed_mph} unit=" mph" />
            <Separator />
            <SpecItem label="Motor Power" value={evModel.motor_power_hp} unit=" hp" />
            <Separator />
            <SpecItem label="Motor Torque" value={evModel.motor_torque_lb_ft} unit=" lb-ft" />
            <Separator />
            <SpecItem
              label="Drivetrain"
              value={evModel.drivetrain}
              formatter={(val) => (val ? val.charAt(0).toUpperCase() + val.slice(1) : 'N/A')}
            />
            {evModel.buyerMetrics?.powerToWeightRatio && (
              <>
                <Separator />
                <SpecItem
                  label="Power-to-Weight"
                  value={evModel.buyerMetrics.powerToWeightRatio}
                  unit=" hp/lb"
                />
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Battery className="h-5 w-5" />
              Battery & Charging
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            <SpecItem label="Battery Capacity" value={evModel.battery_capacity_kwh} unit=" kWh" />
            <Separator />
            <SpecItem label="EPA Range" value={evModel.range_epa_miles} formatter={formatRange} />
            <Separator />
            <SpecItem label="WLTP Range" value={evModel.range_wltp_miles} formatter={formatRange} />
            <Separator />
            <SpecItem
              label="Real-World Range"
              value={evModel.range_real_world_miles}
              formatter={formatRange}
            />
            <Separator />
            <SpecItem label="DC Fast Charging" value={evModel.charging_speed_dc_kw} unit=" kW" />
            <Separator />
            <SpecItem label="AC Charging" value={evModel.charging_speed_ac_kw} unit=" kW" />
            <Separator />
            <SpecItem
              label="Charging Time (10-80%)"
              value={evModel.charging_time_10_80_minutes}
              formatter={formatChargingTime}
            />
            {evModel.charging_ports && evModel.charging_ports.length > 0 && (
              <>
                <Separator />
                <div className="flex justify-between py-2">
                  <span className="text-gray-600 dark:text-gray-400">Charging Ports</span>
                  <div className="flex flex-wrap gap-1">
                    {evModel.charging_ports.map((port, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {port}
                      </Badge>
                    ))}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Efficiency & Range */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gauge className="h-5 w-5" />
            Efficiency & Range
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="space-y-1">
              <SpecItem label="EPA Efficiency" value={evModel.efficiency_mpge} unit=" MPGe" />
              <Separator />
              <SpecItem
                label="Energy Consumption"
                value={evModel.energy_consumption_kwh_100mi}
                unit=" kWh/100mi"
              />
              {evModel.buyerMetrics?.chargingEfficiency && (
                <>
                  <Separator />
                  <SpecItem
                    label="Charging Efficiency"
                    value={evModel.buyerMetrics.chargingEfficiency}
                    unit="%"
                  />
                </>
              )}
            </div>
            <div className="space-y-1">
              <SpecItem label="EPA Range" value={evModel.range_epa_miles} formatter={formatRange} />
              <Separator />
              <SpecItem
                label="WLTP Range"
                value={evModel.range_wltp_miles}
                formatter={formatRange}
              />
              <Separator />
              <SpecItem
                label="Real-World Range"
                value={evModel.range_real_world_miles}
                formatter={formatRange}
              />
            </div>
            <div className="space-y-1">
              {evModel.buyerMetrics?.costPerMile && (
                <>
                  <SpecItem label="Cost per Mile" value={`$${evModel.buyerMetrics.costPerMile}`} />
                  <Separator />
                </>
              )}
              {evModel.buyerMetrics?.practicalRange && (
                <>
                  <SpecItem
                    label="Practical Range"
                    value={evModel.buyerMetrics.practicalRange}
                    formatter={formatRange}
                  />
                  <Separator />
                </>
              )}
              <SpecItem
                label="Range per kWh"
                value={
                  evModel.battery_capacity_kwh && evModel.range_epa_miles
                    ? Math.round((evModel.range_epa_miles / evModel.battery_capacity_kwh) * 10) / 10
                    : null
                }
                unit=" mi/kWh"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Physical Dimensions */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Ruler className="h-5 w-5" />
              Dimensions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            <SpecItem label="Length" value={evModel.length_inches} unit=" in" />
            <Separator />
            <SpecItem label="Width" value={evModel.width_inches} unit=" in" />
            <Separator />
            <SpecItem label="Height" value={evModel.height_inches} unit=" in" />
            <Separator />
            <SpecItem label="Wheelbase" value={evModel.wheelbase_inches} unit=" in" />
            <Separator />
            <SpecItem label="Ground Clearance" value={evModel.ground_clearance_inches} unit=" in" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Weight className="h-5 w-5" />
              Weight & Capacity
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            <SpecItem label="Curb Weight" value={evModel.curb_weight_lbs} unit=" lbs" />
            <Separator />
            <SpecItem label="Seating Capacity" value={evModel.seating_capacity} unit=" seats" />
            <Separator />
            <SpecItem label="Cargo Volume" value={evModel.cargo_volume_cubic_ft} unit=" cu ft" />
            <Separator />
            <SpecItem label="Towing Capacity" value={evModel.towing_capacity_lbs} unit=" lbs" />
            <Separator />
            <SpecItem label="Payload Capacity" value={evModel.payload_capacity_lbs} unit=" lbs" />
          </CardContent>
        </Card>
      </div>

      {/* Pricing & Market */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Pricing
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            <SpecItem label="MSRP" value={evModel.price_msrp} formatter={formatPrice} />
            <Separator />
            <SpecItem label="Base Price" value={evModel.price_base} formatter={formatPrice} />
            <Separator />
            <SpecItem
              label="As-Tested Price"
              value={evModel.price_as_tested}
              formatter={formatPrice}
            />
            <Separator />
            <div className="flex justify-between py-2">
              <span className="text-gray-600 dark:text-gray-400">Production Status</span>
              <Badge variant={evModel.production_status === 'current' ? 'default' : 'secondary'}>
                {evModel.production_status?.charAt(0).toUpperCase() +
                  evModel.production_status?.slice(1) || 'N/A'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Availability
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            <SpecItem label="Model Year" value={evModel.year} />
            <Separator />
            <SpecItem
              label="Body Type"
              value={evModel.body_type}
              formatter={(val) => (val ? val.charAt(0).toUpperCase() + val.slice(1) : 'N/A')}
            />
            {evModel.market_regions && evModel.market_regions.length > 0 && (
              <>
                <Separator />
                <div className="py-2">
                  <span className="mb-2 block text-gray-600 dark:text-gray-400">
                    Market Regions
                  </span>
                  <div className="flex flex-wrap gap-1">
                    {evModel.market_regions.map((region, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {region}
                      </Badge>
                    ))}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Features & Technology */}
      {evModel.features && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Car className="h-5 w-5" />
              Features & Technology
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {Object.entries(evModel.features as Record<string, any>).map(
                ([category, features]) => (
                  <div key={category} className="space-y-2">
                    <h4 className="text-sm font-semibold capitalize text-gray-900 dark:text-gray-100">
                      {category.replace(/_/g, ' ')}
                    </h4>
                    <div className="space-y-1">
                      {Array.isArray(features) ? (
                        features.map((feature: string, index: number) => (
                          <div key={index} className="text-sm text-gray-600 dark:text-gray-400">
                            • {feature}
                          </div>
                        ))
                      ) : typeof features === 'object' ? (
                        Object.entries(features).map(([key, value]) => (
                          <div key={key} className="text-sm">
                            <span className="text-gray-600 dark:text-gray-400">
                              {key.replace(/_/g, ' ')}:{' '}
                            </span>
                            <span className="font-medium">{String(value)}</span>
                          </div>
                        ))
                      ) : (
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {String(features)}
                        </div>
                      )}
                    </div>
                  </div>
                )
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Safety & Warranty */}
      <div className="grid gap-6 md:grid-cols-2">
        {evModel.safety_ratings && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Safety Ratings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(evModel.safety_ratings as Record<string, any>).map(
                  ([agency, rating]) => (
                    <div key={agency} className="flex items-center justify-between">
                      <span className="text-sm uppercase text-gray-600 dark:text-gray-400">
                        {agency}
                      </span>
                      <div className="flex items-center gap-2">
                        {typeof rating === 'object' ? (
                          <div className="text-right">
                            {Object.entries(rating).map(([test, score]) => (
                              <div key={test} className="text-xs">
                                <span className="text-gray-500">{test}: </span>
                                <span className="font-semibold">{String(score)}</span>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <Badge variant="outline" className="font-semibold">
                            {String(rating)}
                          </Badge>
                        )}
                      </div>
                    </div>
                  )
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {evModel.warranty_info && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wrench className="h-5 w-5" />
                Warranty Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(evModel.warranty_info as Record<string, any>).map(
                  ([type, details]) => (
                    <div key={type} className="space-y-1">
                      <h4 className="text-sm font-semibold capitalize">
                        {type.replace(/_/g, ' ')}
                      </h4>
                      {typeof details === 'object' ? (
                        Object.entries(details).map(([key, value]) => (
                          <div key={key} className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">
                              {key.replace(/_/g, ' ')}:
                            </span>
                            <span className="font-medium">{String(value)}</span>
                          </div>
                        ))
                      ) : (
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {String(details)}
                        </div>
                      )}
                    </div>
                  )
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
