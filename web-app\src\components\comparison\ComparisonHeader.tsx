'use client'

import Image from 'next/image'
import Link from 'next/link'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ExternalLink, Share2, Download, Star, TrendingUp } from 'lucide-react'
import { formatPrice, formatRange } from '@/utils/ev-buyer-guide'
import { PLACEHOLDER_IMAGES } from '@/constants/ev-buyer-guide'
import type { EVModel } from '../../../types'

interface ComparisonHeaderProps {
  models: EVModel[]
}

export function ComparisonHeader({ models }: ComparisonHeaderProps) {
  const handleShare = () => {
    const url = window.location.href
    if (navigator.share) {
      navigator.share({
        title: `Compare ${models.map((m) => `${m.make} ${m.model}`).join(' vs ')}`,
        url,
      })
    } else {
      navigator.clipboard.writeText(url)
      // Could add toast notification here
    }
  }

  const handleExport = () => {
    // Could implement PDF export or other export functionality
    console.log('Export comparison')
  }

  return (
    <section className="bg-white py-8 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 lg:text-4xl">
            EV Comparison
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Compare {models.length} electric vehicles side by side
          </p>

          {/* Actions */}
          <div className="mt-4 flex justify-center gap-3">
            <Button variant="outline" onClick={handleShare}>
              <Share2 className="mr-2 h-4 w-4" />
              Share
            </Button>
            <Button variant="outline" onClick={handleExport}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        {/* Model Overview Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {models.map((model) => (
            <Card key={model.id} className="overflow-hidden">
              {/* Model Image */}
              <div className="relative aspect-video">
                <Image
                  src={model.images?.[0] || PLACEHOLDER_IMAGES.ev_model}
                  alt={`${model.make} ${model.model}`}
                  fill
                  className="object-cover"
                />

                {/* Badges */}
                <div className="absolute left-3 top-3 flex flex-col gap-2">
                  {model.is_featured && (
                    <Badge className="bg-electric-600 text-white">
                      <Star className="mr-1 h-3 w-3" />
                      Featured
                    </Badge>
                  )}
                  {model.best_value && <Badge variant="secondary">Best Value</Badge>}
                  {model.editor_choice && (
                    <Badge className="bg-amber-600 text-white">Editor's Choice</Badge>
                  )}
                </div>

                {/* Link to Details */}
                <Button
                  asChild
                  variant="outline"
                  size="sm"
                  className="absolute right-3 top-3 bg-white/90 backdrop-blur-sm"
                >
                  <Link href={`/ev-models/${model.id}`}>
                    <ExternalLink className="h-3 w-3" />
                  </Link>
                </Button>
              </div>

              <CardContent className="p-4">
                {/* Model Info */}
                <div className="mb-3">
                  <h3 className="text-lg font-semibold">
                    {model.make} {model.model}
                  </h3>
                  {model.trim && (
                    <p className="text-sm text-gray-600 dark:text-gray-400">{model.trim}</p>
                  )}
                  <div className="mt-1 flex items-center gap-2 text-xs text-gray-500">
                    <span>{model.year}</span>
                    <span>•</span>
                    <span className="capitalize">{model.body_type}</span>
                  </div>
                </div>

                {/* Key Specs */}
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Price:</span>
                    <span className="font-semibold text-electric-600">
                      {formatPrice(model.price_msrp)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Range:</span>
                    <span className="font-semibold">{formatRange(model.range_epa_miles)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">0-60 mph:</span>
                    <span className="font-semibold">
                      {model.acceleration_0_60_mph ? `${model.acceleration_0_60_mph}s` : 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Efficiency:</span>
                    <span className="font-semibold">
                      {model.efficiency_mpge ? `${model.efficiency_mpge} MPGe` : 'N/A'}
                    </span>
                  </div>
                </div>

                {/* Popularity Score */}
                {model.popularity_score && (
                  <div className="mt-3 border-t pt-3">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-600 dark:text-gray-400">Popularity</span>
                      <div className="flex items-center gap-1">
                        <TrendingUp className="h-3 w-3 text-electric-600" />
                        <span className="font-semibold">{model.popularity_score}/100</span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
