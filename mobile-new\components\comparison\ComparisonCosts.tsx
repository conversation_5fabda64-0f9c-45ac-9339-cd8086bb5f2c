import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { EVModel } from "@/types";

interface ComparisonCostsProps {
  models: EVModel[];
}

const ComparisonCosts: React.FC<ComparisonCostsProps> = ({ models }) => {
  if (!models || models.length < 2) {
    return (
      <View style={styles.container}>
        <Text>Need at least 2 models to compare.</Text>
      </View>
    );
  }

  const [ev1_details, ev2_details] = models;

  const renderCostRow = (
    label: string,
    value1: string | number,
    value2: string | number
  ) => (
    <View style={styles.row}>
      <Text style={styles.labelCell}>{label}</Text>
      <Text style={styles.valueCell}>{value1}</Text>
      <Text style={styles.valueCell}>{value2}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.header}>Cost Analysis</Text>
      <View style={styles.table}>
        <View style={styles.tableHeader}>
          <Text style={styles.tableHeaderLabel}>Metric</Text>
          <Text style={styles.tableHeaderValue}>{ev1_details.model}</Text>
          <Text style={styles.tableHeaderValue}>{ev2_details.model}</Text>
        </View>
        {renderCostRow(
          "MSRP",
          `$${ev1_details.price_msrp?.toLocaleString() ?? "N/A"}`,
          `$${ev2_details.price_msrp?.toLocaleString() ?? "N/A"}`
        )}
        {renderCostRow(
          "Price w/ Tax Credit",
          `$${(
            (ev1_details.price_msrp ?? 0) -
            (ev1_details.total_cost_ownership?.federal_tax_credit ?? 0)
          ).toLocaleString()}`,
          `$${(
            (ev2_details.price_msrp ?? 0) -
            (ev2_details.total_cost_ownership?.federal_tax_credit ?? 0)
          ).toLocaleString()}`
        )}
        {renderCostRow(
          "5-Year Total Cost",
          `$${
            ev1_details.total_cost_ownership?.estimated_total_5_year?.toLocaleString() ??
            "N/A"
          }`,
          `$${
            ev2_details.total_cost_ownership?.estimated_total_5_year?.toLocaleString() ??
            "N/A"
          }`
        )}
        {renderCostRow(
          "Annual Fuel Cost",
          `$${
            ev1_details.total_cost_ownership?.estimated_annual_fuel_cost?.toLocaleString() ??
            "N/A"
          }`,
          `$${
            ev2_details.total_cost_ownership?.estimated_annual_fuel_cost?.toLocaleString() ??
            "N/A"
          }`
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: "#fff",
    borderRadius: 8,
    marginBottom: 16,
  },
  header: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 16,
  },
  table: {
    borderWidth: 1,
    borderColor: "#E5E7EB",
    borderRadius: 8,
    overflow: "hidden",
  },
  tableHeader: {
    flexDirection: "row",
    backgroundColor: "#F3F4F6",
    borderBottomWidth: 1,
    borderColor: "#E5E7EB",
  },
  tableHeaderLabel: {
    flex: 2,
    padding: 12,
    fontWeight: "bold",
  },
  tableHeaderValue: {
    flex: 1,
    padding: 12,
    fontWeight: "bold",
    textAlign: "center",
  },
  row: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderColor: "#E5E7EB",
    alignItems: "center",
  },
  labelCell: {
    flex: 2,
    padding: 12,
  },
  valueCell: {
    flex: 1,
    padding: 12,
    textAlign: "center",
  },
});

export default ComparisonCosts;
