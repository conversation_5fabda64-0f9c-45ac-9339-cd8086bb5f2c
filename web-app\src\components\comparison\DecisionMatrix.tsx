'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Crown, Star, TrendingUp, DollarSign, Zap, Battery, Gauge, Settings } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { EVModel } from '../../../types'

interface DecisionMatrixProps {
  models: EVModel[]
}

interface DecisionCriteria {
  id: string
  label: string
  icon: React.ReactNode
  weight: number
  getValue: (model: EVModel) => number
  format: (value: number) => string
  higherIsBetter: boolean
}

const defaultCriteria: DecisionCriteria[] = [
  {
    id: 'price',
    label: 'Price Value',
    icon: <DollarSign className="h-4 w-4" />,
    weight: 25,
    getValue: (model) => {
      // Convert price to value score (lower price = higher score)
      const price = model.price_msrp || 0
      if (price === 0) return 0
      // Normalize to 0-100 scale, with $30k = 100, $100k = 0
      return Math.max(0, Math.min(100, 100 - (price / 100 - 30000) / 700))
    },
    format: (value) => `${Math.round(value)}/100`,
    higherIsBetter: true,
  },
  {
    id: 'range',
    label: 'Driving Range',
    icon: <Battery className="h-4 w-4" />,
    weight: 25,
    getValue: (model) => {
      const range = model.range_epa_miles || 0
      // Normalize to 0-100 scale, with 400+ miles = 100
      return Math.min(100, (range / 400) * 100)
    },
    format: (value) => `${Math.round(value)}/100`,
    higherIsBetter: true,
  },
  {
    id: 'performance',
    label: 'Performance',
    icon: <Gauge className="h-4 w-4" />,
    weight: 20,
    getValue: (model) => {
      const acceleration = model.acceleration_0_60_mph
      if (!acceleration) return 50 // Default middle score
      // Convert acceleration to score (lower time = higher score)
      // 3s = 100, 8s = 0
      return Math.max(0, Math.min(100, 100 - ((acceleration - 3) / 5) * 100))
    },
    format: (value) => `${Math.round(value)}/100`,
    higherIsBetter: true,
  },
  {
    id: 'efficiency',
    label: 'Efficiency',
    icon: <Zap className="h-4 w-4" />,
    weight: 15,
    getValue: (model) => {
      const efficiency = model.efficiency_mpge || 0
      if (efficiency === 0) return 0
      // Normalize to 0-100 scale, with 150+ MPGe = 100
      return Math.min(100, (efficiency / 150) * 100)
    },
    format: (value) => `${Math.round(value)}/100`,
    higherIsBetter: true,
  },
  {
    id: 'charging',
    label: 'Charging Speed',
    icon: <TrendingUp className="h-4 w-4" />,
    weight: 15,
    getValue: (model) => {
      const chargingSpeed = model.charging_speed_dc_kw || 0
      if (chargingSpeed === 0) return 0
      // Normalize to 0-100 scale, with 250+ kW = 100
      return Math.min(100, (chargingSpeed / 250) * 100)
    },
    format: (value) => `${Math.round(value)}/100`,
    higherIsBetter: true,
  },
]

export function DecisionMatrix({ models }: DecisionMatrixProps) {
  const [criteria, setCriteria] = useState<DecisionCriteria[]>(defaultCriteria)
  const [showWeights, setShowWeights] = useState(false)

  const updateWeight = (criteriaId: string, newWeight: number) => {
    setCriteria((prev) => prev.map((c) => (c.id === criteriaId ? { ...c, weight: newWeight } : c)))
  }

  const resetWeights = () => {
    setCriteria(defaultCriteria)
  }

  const normalizeWeights = () => {
    const totalWeight = criteria.reduce((sum, c) => sum + c.weight, 0)
    if (totalWeight === 0) return criteria

    return criteria.map((c) => ({
      ...c,
      weight: (c.weight / totalWeight) * 100,
    }))
  }

  const scoredModels = useMemo(() => {
    const normalizedCriteria = normalizeWeights()

    return models
      .map((model) => {
        const scores = normalizedCriteria.map((criterion) => {
          const rawValue = criterion.getValue(model)
          const weightedScore = (rawValue * criterion.weight) / 100
          return {
            criteriaId: criterion.id,
            rawValue,
            weightedScore,
          }
        })

        const totalScore = scores.reduce((sum, score) => sum + score.weightedScore, 0)

        return {
          model,
          scores,
          totalScore,
        }
      })
      .sort((a, b) => b.totalScore - a.totalScore)
  }, [models, criteria])

  const topModel = scoredModels[0]

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-electric-600" />
              Decision Matrix
            </CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => setShowWeights(!showWeights)}>
                <Settings className="mr-2 h-4 w-4" />
                {showWeights ? 'Hide' : 'Adjust'} Weights
              </Button>
              <Button variant="outline" size="sm" onClick={resetWeights}>
                Reset
              </Button>
            </div>
          </div>
        </CardHeader>

        {showWeights && (
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Adjust the importance of each factor to match your priorities:
              </p>

              {criteria.map((criterion) => (
                <div key={criterion.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {criterion.icon}
                      <span className="font-medium">{criterion.label}</span>
                    </div>
                    <span className="text-sm font-medium">{Math.round(criterion.weight)}%</span>
                  </div>
                  <Slider
                    value={[criterion.weight]}
                    onValueChange={([value]) => updateWeight(criterion.id, value)}
                    max={50}
                    min={0}
                    step={5}
                    className="w-full"
                  />
                </div>
              ))}

              <div className="border-t pt-2">
                <div className="flex justify-between text-sm">
                  <span>Total Weight:</span>
                  <span className="font-medium">
                    {Math.round(criteria.reduce((sum, c) => sum + c.weight, 0))}%
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Results */}
      <div className="grid gap-4">
        {scoredModels.map((result, index) => (
          <Card
            key={result.model.id}
            className={cn(
              'transition-all',
              index === 0 && 'dark:bg-electric-950 bg-electric-50 ring-2 ring-electric-500'
            )}
          >
            <CardContent className="p-6">
              <div className="mb-4 flex items-start justify-between">
                <div className="flex items-center gap-3">
                  {index === 0 && <Crown className="h-5 w-5 text-amber-500" />}
                  <div>
                    <h3 className="text-lg font-semibold">
                      {result.model.make} {result.model.model}
                    </h3>
                    {result.model.trim && (
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {result.model.trim}
                      </p>
                    )}
                  </div>
                </div>

                <div className="text-right">
                  <div className="text-2xl font-bold text-electric-600">
                    {Math.round(result.totalScore)}/100
                  </div>
                  <Badge variant={index === 0 ? 'default' : 'secondary'}>#{index + 1} Choice</Badge>
                </div>
              </div>

              {/* Score Breakdown */}
              <div className="space-y-3">
                {result.scores.map((score) => {
                  const criterion = criteria.find((c) => c.id === score.criteriaId)!

                  return (
                    <div key={score.criteriaId} className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          {criterion.icon}
                          <span>{criterion.label}</span>
                        </div>
                        <span className="font-medium">
                          {criterion.format(score.rawValue)}
                          <span className="ml-1 text-gray-500">
                            (×{Math.round(criterion.weight)}%)
                          </span>
                        </span>
                      </div>
                      <Progress value={score.rawValue} className="h-2" />
                    </div>
                  )
                })}
              </div>

              {/* Overall Score Bar */}
              <div className="mt-4 border-t pt-4">
                <div className="mb-1 flex items-center justify-between text-sm">
                  <span className="font-medium">Overall Score</span>
                  <span className="font-bold">{Math.round(result.totalScore)}/100</span>
                </div>
                <Progress value={result.totalScore} className="h-3" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recommendation */}
      {topModel && (
        <Card className="dark:bg-electric-950 border-electric-200 bg-electric-50 dark:border-electric-800">
          <CardContent className="p-6">
            <div className="flex items-start gap-3">
              <Crown className="mt-1 h-6 w-6 text-amber-500" />
              <div>
                <h3 className="mb-2 text-lg font-semibold">Recommended Choice</h3>
                <p className="text-gray-700 dark:text-gray-300">
                  Based on your priorities, the{' '}
                  <strong>
                    {topModel.model.make} {topModel.model.model}
                  </strong>{' '}
                  scores highest with {Math.round(topModel.totalScore)}/100 points. This model
                  offers the best balance of the factors you value most.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
