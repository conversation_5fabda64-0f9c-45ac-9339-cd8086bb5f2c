'use client'

import { useState, useEffect, useRef } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Search, X, Clock } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useDebounce } from '@/hooks/useDebounce'
import { SEARCH_DEBOUNCE_MS, MIN_SEARCH_LENGTH } from '@/constants/ev-buyer-guide'

interface SearchSuggestion {
  text: string
  type: 'make' | 'model' | 'recent'
}

interface EVModelSearchProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
}

export function EVModelSearch({
  value,
  onChange,
  placeholder = 'Search EV models...',
  className,
}: EVModelSearchProps) {
  const [isFocused, setIsFocused] = useState(false)
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [loading, setLoading] = useState(false)

  const inputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const debouncedValue = useDebounce(value, SEARCH_DEBOUNCE_MS)

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('ev-search-recent')
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved))
      } catch (error) {
        console.error('Failed to parse recent searches:', error)
      }
    }
  }, [])

  // Fetch suggestions when debounced value changes
  useEffect(() => {
    if (debouncedValue && debouncedValue.length >= MIN_SEARCH_LENGTH) {
      fetchSuggestions(debouncedValue)
    } else {
      setSuggestions([])
    }
  }, [debouncedValue])

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsFocused(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const fetchSuggestions = async (query: string) => {
    setLoading(true)
    try {
      const response = await fetch(`/api/ev-models/search?q=${encodeURIComponent(query)}&limit=5`)
      if (response.ok) {
        const data = await response.json()
        const searchSuggestions: SearchSuggestion[] =
          data.suggestions?.map((text: string) => ({
            text,
            type: 'make' as const,
          })) || []
        setSuggestions(searchSuggestions)
      }
    } catch (error) {
      console.error('Failed to fetch suggestions:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value)
  }

  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion)
    setIsFocused(false)
    saveRecentSearch(suggestion)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (value.trim()) {
      setIsFocused(false)
      saveRecentSearch(value.trim())
    }
  }

  const handleClear = () => {
    onChange('')
    inputRef.current?.focus()
  }

  const saveRecentSearch = (search: string) => {
    const trimmed = search.trim()
    if (!trimmed || trimmed.length < MIN_SEARCH_LENGTH) return

    const updated = [trimmed, ...recentSearches.filter((s) => s !== trimmed)].slice(0, 5)
    setRecentSearches(updated)
    localStorage.setItem('ev-search-recent', JSON.stringify(updated))
  }

  const clearRecentSearches = () => {
    setRecentSearches([])
    localStorage.removeItem('ev-search-recent')
  }

  const showSuggestions = isFocused && (suggestions.length > 0 || recentSearches.length > 0)

  return (
    <div ref={containerRef} className={cn('relative', className)}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <Input
            ref={inputRef}
            type="text"
            value={value}
            onChange={handleInputChange}
            onFocus={() => setIsFocused(true)}
            placeholder={placeholder}
            className="pl-10 pr-10"
          />
          {value && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className="absolute right-1 top-1/2 h-8 w-8 -translate-y-1/2 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </form>

      {/* Suggestions Dropdown */}
      {showSuggestions && (
        <div className="absolute top-full z-50 mt-1 w-full rounded-md border bg-white shadow-lg dark:bg-gray-900">
          <div className="max-h-80 overflow-y-auto p-2">
            {/* Search Suggestions */}
            {suggestions.length > 0 && (
              <div className="mb-2">
                <div className="mb-2 px-2 text-xs font-medium text-gray-500 dark:text-gray-400">
                  Suggestions
                </div>
                {suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion.text)}
                    className="flex w-full items-center gap-2 rounded-md px-2 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-800"
                  >
                    <Search className="h-4 w-4 text-gray-400" />
                    <span>{suggestion.text}</span>
                    <Badge variant="outline" className="ml-auto text-xs">
                      {suggestion.type}
                    </Badge>
                  </button>
                ))}
              </div>
            )}

            {/* Recent Searches */}
            {recentSearches.length > 0 && (
              <div>
                <div className="mb-2 flex items-center justify-between px-2">
                  <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                    Recent searches
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearRecentSearches}
                    className="h-auto p-0 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                  >
                    Clear
                  </Button>
                </div>
                {recentSearches.map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(search)}
                    className="flex w-full items-center gap-2 rounded-md px-2 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-800"
                  >
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span>{search}</span>
                  </button>
                ))}
              </div>
            )}

            {/* No suggestions */}
            {suggestions.length === 0 &&
              recentSearches.length === 0 &&
              value.length >= MIN_SEARCH_LENGTH && (
                <div className="px-2 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                  {loading ? 'Searching...' : 'No suggestions found'}
                </div>
              )}
          </div>
        </div>
      )}
    </div>
  )
}
