import React from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { EVModel } from "../../types";
import { useComparison } from "../../contexts/ComparisonContext";

interface ComparisonOverviewProps {
  models: EVModel[];
}

export function ComparisonOverview({ models }: ComparisonOverviewProps) {
  const { removeFromComparison } = useComparison();

  const formatPrice = (priceInCents: number | null) => {
    if (!priceInCents) return "Price TBA";
    return `$${(priceInCents / 100).toLocaleString()}`;
  };

  const formatRange = (range: number | null) => {
    if (!range) return "N/A";
    return `${range} mi`;
  };

  const getImageSource = (model: EVModel) => {
    if (model.images && model.images.length > 0) {
      return { uri: model.images[0] };
    }
    return null;
  };

  const getBestInCategory = (
    category: "price" | "range" | "efficiency" | "acceleration"
  ) => {
    if (models.length === 0) return null;

    switch (category) {
      case "price":
        return models.reduce((best, current) => {
          if (!current.price_msrp) return best;
          if (!best.price_msrp) return current;
          return current.price_msrp < best.price_msrp ? current : best;
        });
      case "range":
        return models.reduce((best, current) => {
          if (!current.range_epa_miles) return best;
          if (!best.range_epa_miles) return current;
          return current.range_epa_miles > best.range_epa_miles
            ? current
            : best;
        });
      case "efficiency":
        return models.reduce((best, current) => {
          if (!current.efficiency_mpge) return best;
          if (!best.efficiency_mpge) return current;
          return current.efficiency_mpge > best.efficiency_mpge
            ? current
            : best;
        });
      case "acceleration":
        return models.reduce((best, current) => {
          if (!current.acceleration_0_60_mph) return best;
          if (!best.acceleration_0_60_mph) return current;
          return current.acceleration_0_60_mph < best.acceleration_0_60_mph
            ? current
            : best;
        });
      default:
        return models[0];
    }
  };

  const getModelRanking = (model: EVModel) => {
    let score = 0;
    let factors = 0;

    if (model.price_msrp) {
      const avgPrice =
        models.reduce((sum, m) => sum + (m.price_msrp || 0), 0) / models.length;
      score +=
        model.price_msrp < avgPrice ? 2 : model.price_msrp === avgPrice ? 1 : 0;
      factors++;
    }

    if (model.range_epa_miles) {
      const avgRange =
        models.reduce((sum, m) => sum + (m.range_epa_miles || 0), 0) /
        models.length;
      score +=
        model.range_epa_miles > avgRange
          ? 2
          : model.range_epa_miles === avgRange
          ? 1
          : 0;
      factors++;
    }

    if (model.efficiency_mpge) {
      const avgEfficiency =
        models.reduce((sum, m) => sum + (m.efficiency_mpge || 0), 0) /
        models.length;
      score +=
        model.efficiency_mpge > avgEfficiency
          ? 2
          : model.efficiency_mpge === avgEfficiency
          ? 1
          : 0;
      factors++;
    }

    return factors > 0 ? (score / (factors * 2)) * 100 : 50;
  };

  const VehicleCard = ({ model, index }: { model: EVModel; index: number }) => {
    const ranking = getModelRanking(model);
    const isTopRanked = ranking >= 75;
    const isGoodValue = ranking >= 50;
    const imageSource = getImageSource(model);

    return (
      <TouchableOpacity
        onPress={() => router.push(`/ev-models/${model.id}` as any)}
        style={styles.vehicleCard}
      >
        <View style={styles.cardHeader}>
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <Text style={styles.rankingNumber}>#{index + 1}</Text>
            <View>
              <Text style={styles.modelName} numberOfLines={1}>
                {model.make} {model.model}
              </Text>
              {model.trim && (
                <Text style={styles.modelTrim} numberOfLines={1}>
                  {model.trim}
                </Text>
              )}
            </View>
          </View>
          <View style={styles.headerActions}>
            {isTopRanked && (
              <View style={styles.topPickBadge}>
                <Text style={styles.topPickText}>Top Pick</Text>
              </View>
            )}
            <TouchableOpacity
              onPress={() => removeFromComparison(model.id)}
              style={{ padding: 4 }}
            >
              <Ionicons name="close" size={20} color="#6b7280" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.cardBody}>
          <View style={{ marginRight: 16 }}>
            {imageSource ? (
              <Image
                source={imageSource}
                style={styles.vehicleImage}
                resizeMode="cover"
              />
            ) : (
              <View style={styles.imagePlaceholder}>
                <Ionicons name="car" size={24} color="#9ca3af" />
              </View>
            )}
          </View>
          <View style={{ flex: 1 }}>
            <Text style={styles.modelPrice}>
              {formatPrice(model.price_msrp)}
            </Text>
            <View style={styles.keyStats}>
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>Range</Text>
                <Text style={styles.statValue}>
                  {formatRange(model.range_epa_miles)}
                </Text>
              </View>

              {model.efficiency_mpge && (
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Efficiency</Text>
                  <Text style={styles.statValue}>
                    {model.efficiency_mpge} MPGe
                  </Text>
                </View>
              )}

              {model.acceleration_0_60_mph && (
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>0-60 mph</Text>
                  <Text style={styles.statValue}>
                    {model.acceleration_0_60_mph}s
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>

        <View style={styles.rankingContainer}>
          <View style={styles.rankingLabelRow}>
            <Text style={styles.statLabel}>Overall Score</Text>
            <Text style={styles.rankingValue}>{Math.round(ranking)}%</Text>
          </View>
          <View style={styles.rankingBarBackground}>
            <View
              style={[
                styles.rankingBar,
                isTopRanked
                  ? styles.topRankedBar
                  : isGoodValue
                  ? styles.goodValueBar
                  : styles.defaultBar,
                { width: `${ranking}%` },
              ]}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const CategoryWinner = ({
    title,
    icon,
    model,
    value,
  }: {
    title: string;
    icon: keyof typeof Ionicons.glyphMap;
    model: EVModel | null;
    value: string;
  }) => {
    if (!model) return null;

    return (
      <View style={styles.winnerCard}>
        <View style={styles.winnerHeader}>
          <Ionicons name={icon} size={16} color="#6b7280" />
          <Text style={styles.winnerTitle}>{title}</Text>
        </View>
        <Text style={styles.winnerModel} numberOfLines={1}>
          {model.make} {model.model}
        </Text>
        <Text style={styles.winnerValue}>{value}</Text>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={{ padding: 16 }}>
        <Text style={styles.sectionTitle}>Category Leaders</Text>
        <View style={{ flexDirection: "row", gap: 12, marginBottom: 12 }}>
          <CategoryWinner
            title="Best Value"
            icon="cash"
            model={getBestInCategory("price")}
            value={formatPrice(getBestInCategory("price")?.price_msrp ?? null)}
          />
          <CategoryWinner
            title="Longest Range"
            icon="speedometer"
            model={getBestInCategory("range")}
            value={formatRange(
              getBestInCategory("range")?.range_epa_miles ?? null
            )}
          />
        </View>
        <View style={{ flexDirection: "row", gap: 12 }}>
          <CategoryWinner
            title="Most Efficient"
            icon="leaf"
            model={getBestInCategory("efficiency")}
            value={`${
              getBestInCategory("efficiency")?.efficiency_mpge || 0
            } MPGe`}
          />
          <CategoryWinner
            title="Quickest"
            icon="flash"
            model={getBestInCategory("acceleration")}
            value={`${
              getBestInCategory("acceleration")?.acceleration_0_60_mph || 0
            }s`}
          />
        </View>
      </View>
      {models.map((model, index) => (
        <VehicleCard key={model.id} model={model} index={index} />
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
    marginBottom: 16,
  },
  vehicleCard: {
    backgroundColor: "white",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    borderWidth: 1,
    borderColor: "#f3f4f6",
    marginBottom: 16,
    marginHorizontal: 16,
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    paddingBottom: 8,
  },
  rankingNumber: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#d1d5db",
    marginRight: 12,
  },
  modelName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
  },
  modelTrim: {
    fontSize: 14,
    color: "#4b5563",
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  topPickBadge: {
    backgroundColor: "#dcfce7",
    borderRadius: 9999,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  topPickText: {
    color: "#166534",
    fontSize: 12,
    fontWeight: "500",
  },
  cardBody: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  vehicleImage: {
    width: 96,
    height: 64,
    borderRadius: 8,
  },
  imagePlaceholder: {
    width: 96,
    height: 64,
    borderRadius: 8,
    backgroundColor: "#e5e7eb",
    alignItems: "center",
    justifyContent: "center",
  },
  modelPrice: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#4f46e5",
    marginBottom: 8,
  },
  keyStats: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  statItem: {
    alignItems: "center",
  },
  statLabel: {
    fontSize: 12,
    color: "#6b7280",
  },
  statValue: {
    fontWeight: "600",
    color: "#111827",
  },
  rankingContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  rankingLabelRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  rankingValue: {
    fontSize: 14,
    fontWeight: "500",
    color: "#111827",
  },
  rankingBarBackground: {
    backgroundColor: "#e5e7eb",
    borderRadius: 9999,
    height: 8,
  },
  rankingBar: {
    height: 8,
    borderRadius: 9999,
  },
  topRankedBar: {
    backgroundColor: "#22c55e",
  },
  goodValueBar: {
    backgroundColor: "#3b82f6",
  },
  defaultBar: {
    backgroundColor: "#a1a1aa",
  },
  winnerCard: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 12,
    flex: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  winnerHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  winnerTitle: {
    fontSize: 12,
    color: "#4b5563",
    marginLeft: 4,
  },
  winnerModel: {
    fontWeight: "600",
    color: "#111827",
    fontSize: 14,
  },
  winnerValue: {
    color: "#4f46e5",
    fontWeight: "bold",
    fontSize: 18,
  },
});
