import toast from 'react-hot-toast'

// Custom toast utilities with consistent styling
export const showToast = {
  success: (message: string, options?: any) => {
    return toast.success(message, {
      duration: 3000,
      style: {
        background: 'rgb(34 197 94)', // green-500
        color: 'white',
        border: '1px solid rgb(34 197 94)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        padding: '12px 16px',
        boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      },
      iconTheme: {
        primary: 'white',
        secondary: 'rgb(34 197 94)',
      },
      ...options,
    })
  },

  error: (message: string, options?: any) => {
    return toast.error(message, {
      duration: 5000,
      style: {
        background: 'rgb(239 68 68)', // red-500
        color: 'white',
        border: '1px solid rgb(239 68 68)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        padding: '12px 16px',
        boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      },
      iconTheme: {
        primary: 'white',
        secondary: 'rgb(239 68 68)',
      },
      ...options,
    })
  },

  loading: (message: string, options?: any) => {
    return toast.loading(message, {
      style: {
        background: 'rgb(59 130 246)', // blue-500
        color: 'white',
        border: '1px solid rgb(59 130 246)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        padding: '12px 16px',
        boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      },
      iconTheme: {
        primary: 'white',
        secondary: 'rgb(59 130 246)',
      },
      ...options,
    })
  },

  info: (message: string, options?: any) => {
    return toast(message, {
      duration: 4000,
      icon: 'ℹ️',
      style: {
        background: 'rgb(59 130 246)', // blue-500
        color: 'white',
        border: '1px solid rgb(59 130 246)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        padding: '12px 16px',
        boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      },
      ...options,
    })
  },

  warning: (message: string, options?: any) => {
    return toast(message, {
      duration: 4000,
      icon: '⚠️',
      style: {
        background: 'rgb(245 158 11)', // amber-500
        color: 'white',
        border: '1px solid rgb(245 158 11)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        padding: '12px 16px',
        boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      },
      ...options,
    })
  },

  // Electric theme variants for our app
  electric: {
    success: (message: string, options?: any) => {
      return toast.success(message, {
        duration: 3000,
        style: {
          background: 'rgb(34 197 94)', // green-500
          color: 'white',
          border: '2px solid rgb(34 197 94)',
          borderRadius: '12px',
          fontSize: '14px',
          fontWeight: '600',
          padding: '14px 18px',
          boxShadow: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
        },
        iconTheme: {
          primary: 'white',
          secondary: 'rgb(34 197 94)',
        },
        ...options,
      })
    },

    error: (message: string, options?: any) => {
      return toast.error(message, {
        duration: 5000,
        style: {
          background: 'rgb(239 68 68)', // red-500
          color: 'white',
          border: '2px solid rgb(239 68 68)',
          borderRadius: '12px',
          fontSize: '14px',
          fontWeight: '600',
          padding: '14px 18px',
          boxShadow: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
        },
        iconTheme: {
          primary: 'white',
          secondary: 'rgb(239 68 68)',
        },
        ...options,
      })
    },

    loading: (message: string, options?: any) => {
      return toast.loading(message, {
        style: {
          background: 'rgb(59 130 246)', // blue-500 (electric blue)
          color: 'white',
          border: '2px solid rgb(59 130 246)',
          borderRadius: '12px',
          fontSize: '14px',
          fontWeight: '600',
          padding: '14px 18px',
          boxShadow: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
        },
        iconTheme: {
          primary: 'white',
          secondary: 'rgb(59 130 246)',
        },
        ...options,
      })
    },
  },

  // Dismiss all toasts
  dismiss: toast.dismiss,

  // Remove specific toast
  remove: toast.remove,
}

// Export the original toast for advanced usage
export { toast }
export default showToast
