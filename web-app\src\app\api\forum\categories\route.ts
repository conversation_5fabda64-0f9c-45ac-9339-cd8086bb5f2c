import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET() {
  try {
    const { data: categories, error } = await supabase
      .from('forum_categories')
      .select(
        `
        *,
        subcategories:forum_categories!parent_category_id(*)
      `
      )
      .is('parent_category_id', null)
      .eq('is_active', true)
      .order('sort_order', { ascending: true })

    if (error) {
      console.error('Error fetching forum categories:', error)
      return NextResponse.json({ error: 'Failed to fetch categories' }, { status: 500 })
    }

    return NextResponse.json({ data: categories })
  } catch (error) {
    console.error('Error in forum categories API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and is an admin
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is forum admin using the helper function
    const { data: isAdmin, error: adminCheckError } = await supabase.rpc('is_forum_admin', {
      user_id: user.id,
    })

    if (adminCheckError || !isAdmin) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const {
      name,
      description,
      slug,
      icon,
      color,
      sort_order,
      requires_approval,
      parent_category_id,
    } = body

    // Validate required fields
    if (!name || !slug) {
      return NextResponse.json({ error: 'Name and slug are required' }, { status: 400 })
    }

    const { data: category, error } = await supabase
      .from('forum_categories')
      .insert({
        name,
        description,
        slug,
        icon,
        color,
        sort_order: sort_order || 0,
        requires_approval: requires_approval || false,
        parent_category_id,
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating forum category:', error)
      return NextResponse.json({ error: 'Failed to create category' }, { status: 500 })
    }

    return NextResponse.json({ data: category })
  } catch (error) {
    console.error('Error in forum categories POST API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
