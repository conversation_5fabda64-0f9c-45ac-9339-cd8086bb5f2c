'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Thermometer,
  Snowflake,
  Sun,
  Zap,
  Battery,
  Gauge,
  TrendingUp,
  TrendingDown,
  MapPin,
  Clock,
  AlertTriangle,
  CheckCircle,
  Info,
} from 'lucide-react'
import { formatChargingTime } from '@/utils/ev-buyer-guide'
import type { EVModelWithDetails } from '../../../shared/types'

interface EVModelRealWorldProps {
  evModel: EVModelWithDetails
}

interface RealWorldMetric {
  label: string
  epaValue: number | null
  realWorldValue: number | null
  unit: string
  icon: React.ReactNode
  formatter?: (value: number) => string
}

interface PerformanceCondition {
  condition: string
  icon: React.ReactNode
  rangeImpact: number // percentage change from EPA
  efficiencyImpact: number // percentage change from EPA
  description: string
}

export function EVModelRealWorld({ evModel }: EVModelRealWorldProps) {
  // Calculate real-world metrics
  const realWorldRange =
    evModel.range_real_world_miles ||
    (evModel.range_epa_miles ? Math.round(evModel.range_epa_miles * 0.85) : null)

  const realWorldEfficiency = evModel.efficiency_mpge
    ? Math.round(evModel.efficiency_mpge * 0.9)
    : null

  const practicalRange = evModel.buyerMetrics?.practicalRange || realWorldRange

  // Performance metrics comparison
  const metrics: RealWorldMetric[] = [
    {
      label: 'Range',
      epaValue: evModel.range_epa_miles,
      realWorldValue: realWorldRange,
      unit: 'miles',
      icon: <MapPin className="h-4 w-4" />,
      formatter: (value) => `${value} mi`,
    },
    {
      label: 'Efficiency',
      epaValue: evModel.efficiency_mpge,
      realWorldValue: realWorldEfficiency,
      unit: 'MPGe',
      icon: <Gauge className="h-4 w-4" />,
      formatter: (value) => `${value} MPGe`,
    },
    {
      label: 'Charging Time',
      epaValue: evModel.charging_time_10_80_minutes,
      realWorldValue: evModel.charging_time_10_80_minutes
        ? Math.round(evModel.charging_time_10_80_minutes * 1.15)
        : null,
      unit: 'minutes',
      icon: <Clock className="h-4 w-4" />,
      formatter: formatChargingTime,
    },
  ]

  // Environmental conditions impact
  const conditions: PerformanceCondition[] = [
    {
      condition: 'Cold Weather',
      icon: <Snowflake className="h-4 w-4 text-blue-500" />,
      rangeImpact: -25,
      efficiencyImpact: -20,
      description: 'Below 32°F (0°C) significantly reduces battery performance',
    },
    {
      condition: 'Hot Weather',
      icon: <Sun className="h-4 w-4 text-orange-500" />,
      rangeImpact: -15,
      efficiencyImpact: -10,
      description: 'Above 95°F (35°C) with A/C usage affects efficiency',
    },
    {
      condition: 'Highway Driving',
      icon: <TrendingUp className="h-4 w-4 text-red-500" />,
      rangeImpact: -20,
      efficiencyImpact: -25,
      description: 'Sustained high speeds (70+ mph) reduce efficiency',
    },
    {
      condition: 'City Driving',
      icon: <TrendingDown className="h-4 w-4 text-green-500" />,
      rangeImpact: 10,
      efficiencyImpact: 15,
      description: 'Stop-and-go traffic with regenerative braking improves efficiency',
    },
  ]

  // Calculate efficiency rating
  const getEfficiencyRating = () => {
    if (!evModel.efficiency_mpge) return null
    if (evModel.efficiency_mpge >= 130)
      return { rating: 'Excellent', color: 'text-green-600', progress: 90 }
    if (evModel.efficiency_mpge >= 110)
      return { rating: 'Very Good', color: 'text-blue-600', progress: 75 }
    if (evModel.efficiency_mpge >= 90)
      return { rating: 'Good', color: 'text-yellow-600', progress: 60 }
    if (evModel.efficiency_mpge >= 70)
      return { rating: 'Average', color: 'text-orange-600', progress: 45 }
    return { rating: 'Below Average', color: 'text-red-600', progress: 30 }
  }

  const efficiencyRating = getEfficiencyRating()

  return (
    <div className="space-y-8">
      {/* EPA vs Real-World Comparison */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gauge className="h-5 w-5" />
            EPA vs Real-World Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-3">
            {metrics.map((metric, index) => (
              <div key={index} className="space-y-3">
                <div className="flex items-center gap-2">
                  {metric.icon}
                  <span className="font-medium">{metric.label}</span>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">EPA Rating</span>
                    <span className="font-semibold">
                      {metric.epaValue
                        ? metric.formatter
                          ? metric.formatter(metric.epaValue)
                          : `${metric.epaValue} ${metric.unit}`
                        : 'N/A'}
                    </span>
                  </div>

                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Real-World</span>
                    <span className="font-semibold">
                      {metric.realWorldValue
                        ? metric.formatter
                          ? metric.formatter(metric.realWorldValue)
                          : `${metric.realWorldValue} ${metric.unit}`
                        : 'N/A'}
                    </span>
                  </div>

                  {metric.epaValue && metric.realWorldValue && (
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Difference</span>
                        <span
                          className={
                            metric.realWorldValue < metric.epaValue
                              ? 'text-red-600'
                              : 'text-green-600'
                          }
                        >
                          {metric.realWorldValue < metric.epaValue ? '-' : '+'}
                          {Math.abs(
                            Math.round(
                              ((metric.realWorldValue - metric.epaValue) / metric.epaValue) * 100
                            )
                          )}
                          %
                        </span>
                      </div>
                      <Progress
                        value={(metric.realWorldValue / metric.epaValue) * 100}
                        className="h-2"
                      />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Environmental Impact on Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Thermometer className="h-5 w-5" />
            Environmental Conditions Impact
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            {conditions.map((condition, index) => (
              <div key={index} className="rounded-lg border p-4">
                <div className="mb-3 flex items-center gap-2">
                  {condition.icon}
                  <span className="font-semibold">{condition.condition}</span>
                </div>

                <p className="mb-3 text-sm text-gray-600 dark:text-gray-400">
                  {condition.description}
                </p>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Range Impact</span>
                    <span className={condition.rangeImpact < 0 ? 'text-red-600' : 'text-green-600'}>
                      {condition.rangeImpact > 0 ? '+' : ''}
                      {condition.rangeImpact}%
                    </span>
                  </div>

                  <div className="flex justify-between text-sm">
                    <span>Efficiency Impact</span>
                    <span
                      className={condition.efficiencyImpact < 0 ? 'text-red-600' : 'text-green-600'}
                    >
                      {condition.efficiencyImpact > 0 ? '+' : ''}
                      {condition.efficiencyImpact}%
                    </span>
                  </div>

                  {evModel.range_epa_miles && (
                    <div className="mt-2 text-xs text-gray-500">
                      Estimated range:{' '}
                      {Math.round(evModel.range_epa_miles * (1 + condition.rangeImpact / 100))}{' '}
                      miles
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Efficiency Rating & Analysis */}
      {efficiencyRating && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Battery className="h-5 w-5" />
                Efficiency Rating
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-lg font-semibold">Overall Efficiency</span>
                  <Badge variant="outline" className={efficiencyRating.color}>
                    {efficiencyRating.rating}
                  </Badge>
                </div>

                <Progress value={efficiencyRating.progress} className="h-3" />

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">EPA Rating</span>
                    <span className="font-semibold">{evModel.efficiency_mpge} MPGe</span>
                  </div>

                  {realWorldEfficiency && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Real-World Est.</span>
                      <span className="font-semibold">{realWorldEfficiency} MPGe</span>
                    </div>
                  )}

                  {evModel.energy_consumption_kwh_100mi && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Energy Use</span>
                      <span className="font-semibold">
                        {evModel.energy_consumption_kwh_100mi} kWh/100mi
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Charging Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {evModel.charging_speed_dc_kw && (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">DC Fast Charging</span>
                      <span className="font-semibold">{evModel.charging_speed_dc_kw} kW</span>
                    </div>

                    <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-800">
                      <div className="text-sm">
                        <div className="mb-1 font-medium">Charging Speed Rating</div>
                        <div className="flex items-center gap-2">
                          {evModel.charging_speed_dc_kw >= 150 ? (
                            <>
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              <span className="text-green-600">Very Fast</span>
                            </>
                          ) : evModel.charging_speed_dc_kw >= 100 ? (
                            <>
                              <CheckCircle className="h-4 w-4 text-blue-600" />
                              <span className="text-blue-600">Fast</span>
                            </>
                          ) : evModel.charging_speed_dc_kw >= 50 ? (
                            <>
                              <Info className="h-4 w-4 text-yellow-600" />
                              <span className="text-yellow-600">Moderate</span>
                            </>
                          ) : (
                            <>
                              <AlertTriangle className="h-4 w-4 text-red-600" />
                              <span className="text-red-600">Slow</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {evModel.charging_time_10_80_minutes && (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">10-80% Charge Time</span>
                      <span className="font-semibold">
                        {formatChargingTime(evModel.charging_time_10_80_minutes)}
                      </span>
                    </div>

                    <div className="text-xs text-gray-500">
                      Real-world charging may take 15-20% longer due to temperature, battery
                      condition, and charger limitations.
                    </div>
                  </div>
                )}

                {evModel.buyerMetrics?.chargingEfficiency && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Charging Efficiency</span>
                    <span className="font-semibold">
                      {evModel.buyerMetrics.chargingEfficiency}%
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Real-World Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Real-World Driving Tips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <h4 className="font-semibold text-green-700 dark:text-green-400">Maximize Range</h4>
              <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                <li>• Use Eco mode for daily driving</li>
                <li>• Precondition cabin while plugged in</li>
                <li>• Maintain steady speeds (55-65 mph optimal)</li>
                <li>• Use regenerative braking effectively</li>
                <li>• Keep tires properly inflated</li>
              </ul>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-blue-700 dark:text-blue-400">Optimize Charging</h4>
              <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                <li>• Charge to 80% for daily use</li>
                <li>• Avoid frequent 100% charges</li>
                <li>• Charge when battery is warm for faster speeds</li>
                <li>• Use scheduled charging during off-peak hours</li>
                <li>• Keep battery between 20-80% when possible</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
