import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const postId = searchParams.get('post_id')
    const userId = searchParams.get('user_id')

    if (!postId) {
      return NextResponse.json({ error: 'Post ID is required' }, { status: 400 })
    }

    let query = supabase
      .from('forum_reactions')
      .select(
        `
        *,
        user:profiles!forum_reactions_user_id_fkey(
          id,
          full_name,
          avatar_url
        )
      `
      )
      .eq('post_id', postId)

    // Filter by user if specified
    if (userId) {
      query = query.eq('user_id', userId)
    }

    const { data: reactions, error } = await query

    if (error) {
      console.error('Error fetching forum reactions:', error)
      return NextResponse.json({ error: 'Failed to fetch reactions' }, { status: 500 })
    }

    // Group reactions by type for easier frontend consumption
    const reactionSummary = reactions.reduce(
      (acc, reaction) => {
        const type = reaction.reaction_type
        if (!acc[type]) {
          acc[type] = {
            count: 0,
            users: [],
          }
        }
        acc[type].count++
        acc[type].users.push({
          id: reaction.user.id,
          display_name: reaction.user.full_name,
          avatar_url: reaction.user.avatar_url,
        })
        return acc
      },
      {} as Record<
        string,
        {
          count: number
          users: Array<{ id: string; display_name: string; avatar_url: string | null }>
        }
      >
    )

    return NextResponse.json({
      data: reactions,
      summary: reactionSummary,
    })
  } catch (error) {
    console.error('Error in forum reactions API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { post_id, reaction_type = 'like' } = body

    // Validate required fields
    if (!post_id) {
      return NextResponse.json({ error: 'Post ID is required' }, { status: 400 })
    }

    // Validate reaction type
    const validReactionTypes = ['like', 'dislike', 'love', 'laugh', 'angry', 'sad']
    if (!validReactionTypes.includes(reaction_type)) {
      return NextResponse.json({ error: 'Invalid reaction type' }, { status: 400 })
    }

    // Check if post exists
    const { data: post, error: postError } = await supabase
      .from('forum_posts')
      .select('id')
      .eq('id', post_id)
      .single()

    if (postError || !post) {
      return NextResponse.json({ error: 'Post not found' }, { status: 404 })
    }

    // Check if user already reacted to this post
    const { data: existingReaction, error: existingError } = await supabase
      .from('forum_reactions')
      .select('id, reaction_type')
      .eq('post_id', post_id)
      .eq('user_id', user.id)
      .single()

    if (existingError && existingError.code !== 'PGRST116') {
      // PGRST116 = no rows found
      console.error('Error checking existing reaction:', existingError)
      return NextResponse.json({ error: 'Failed to check existing reaction' }, { status: 500 })
    }

    if (existingReaction) {
      // If same reaction type, remove it (toggle off)
      if (existingReaction.reaction_type === reaction_type) {
        const { error: deleteError } = await supabase
          .from('forum_reactions')
          .delete()
          .eq('id', existingReaction.id)

        if (deleteError) {
          console.error('Error removing reaction:', deleteError)
          return NextResponse.json({ error: 'Failed to remove reaction' }, { status: 500 })
        }

        return NextResponse.json({ data: { removed: true, reaction_type } })
      } else {
        // Update to new reaction type
        const { data: updatedReaction, error: updateError } = await supabase
          .from('forum_reactions')
          .update({ reaction_type })
          .eq('id', existingReaction.id)
          .select(
            `
            *,
            user:profiles!forum_reactions_user_id_fkey(
              id,
              full_name,
              avatar_url
            )
          `
          )
          .single()

        if (updateError) {
          console.error('Error updating reaction:', updateError)
          return NextResponse.json({ error: 'Failed to update reaction' }, { status: 500 })
        }

        return NextResponse.json({ data: updatedReaction })
      }
    } else {
      // Create new reaction
      const { data: newReaction, error: createError } = await supabase
        .from('forum_reactions')
        .insert({
          post_id,
          user_id: user.id,
          reaction_type,
        })
        .select(
          `
          *,
          user:profiles!forum_reactions_user_id_fkey(
            id,
            full_name,
            avatar_url
          )
        `
        )
        .single()

      if (createError) {
        console.error('Error creating reaction:', createError)
        return NextResponse.json({ error: 'Failed to create reaction' }, { status: 500 })
      }

      return NextResponse.json({ data: newReaction })
    }
  } catch (error) {
    console.error('Error in forum reactions POST API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check if user is authenticated
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const postId = searchParams.get('post_id')
    const reactionId = searchParams.get('reaction_id')

    if (!postId && !reactionId) {
      return NextResponse.json(
        { error: 'Either post_id or reaction_id is required' },
        { status: 400 }
      )
    }

    let deleteQuery = supabase.from('forum_reactions').delete()

    if (reactionId) {
      // Delete specific reaction by ID (must be user's own reaction)
      deleteQuery = deleteQuery.eq('id', reactionId).eq('user_id', user.id)
    } else if (postId) {
      // Delete user's reaction to specific post
      deleteQuery = deleteQuery.eq('post_id', postId).eq('user_id', user.id)
    }

    const { error } = await deleteQuery

    if (error) {
      console.error('Error deleting reaction:', error)
      return NextResponse.json({ error: 'Failed to delete reaction' }, { status: 500 })
    }

    return NextResponse.json({ data: { success: true } })
  } catch (error) {
    console.error('Error in forum reactions DELETE API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
