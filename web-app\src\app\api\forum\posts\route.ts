import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const topicId = searchParams.get('topic_id')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const sortBy = searchParams.get('sort') || 'oldest'

    if (!topicId) {
      return NextResponse.json({ error: 'Topic ID is required' }, { status: 400 })
    }

    let query = supabase
      .from('forum_posts')
      .select(
        `
        *,
        author:profiles!forum_posts_author_id_fkey(
          id,
          full_name,
          avatar_url
        ),
        parent_post:forum_posts!forum_posts_parent_post_id_fkey(
          id,
          content,
          author:profiles!forum_posts_author_id_fkey(
            id,
            full_name
          )
        ),
        replies:forum_posts!parent_post_id(
          id,
          content,
          created_at,
          author:profiles!forum_posts_author_id_fkey(
            id,
            full_name,
            avatar_url
          )
        )
      `
      )
      .eq('topic_id', topicId)
      .eq('is_approved', true)

    // Apply sorting
    switch (sortBy) {
      case 'newest':
        query = query.order('created_at', { ascending: false })
        break
      case 'most_liked':
        query = query.order('like_count', { ascending: false })
        break
      default: // oldest
        query = query.order('created_at', { ascending: true })
    }

    // Apply pagination
    const offset = (page - 1) * limit
    query = query.range(offset, offset + limit - 1)

    const { data: posts, error } = await query

    if (error) {
      console.error('Error fetching forum posts:', error)
      return NextResponse.json({ error: 'Failed to fetch posts' }, { status: 500 })
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('forum_posts')
      .select('*', { count: 'exact', head: true })
      .eq('topic_id', topicId)
      .eq('is_approved', true)

    if (countError) {
      console.error('Error counting posts:', countError)
    }

    // Increment view count for the topic
    await supabase.rpc('increment_topic_views', { topic_id: topicId })

    return NextResponse.json({
      data: posts,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    })
  } catch (error) {
    console.error('Error in forum posts API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { topic_id, content, content_type = 'markdown', parent_post_id } = body

    // Validate required fields
    if (!topic_id || !content) {
      return NextResponse.json({ error: 'Topic ID and content are required' }, { status: 400 })
    }

    // Check if topic exists and is not locked
    const { data: topic, error: topicError } = await supabase
      .from('forum_topics')
      .select(
        'id, is_locked, category:forum_categories!forum_topics_category_id_fkey(requires_approval)'
      )
      .eq('id', topic_id)
      .eq('is_approved', true)
      .single()

    if (topicError || !topic) {
      return NextResponse.json({ error: 'Topic not found' }, { status: 404 })
    }

    if (topic.is_locked) {
      return NextResponse.json({ error: 'Topic is locked' }, { status: 403 })
    }

    // Check if parent post exists (for replies)
    if (parent_post_id) {
      const { data: parentPost, error: parentError } = await supabase
        .from('forum_posts')
        .select('id, topic_id')
        .eq('id', parent_post_id)
        .eq('topic_id', topic_id)
        .single()

      if (parentError || !parentPost) {
        return NextResponse.json({ error: 'Parent post not found' }, { status: 400 })
      }
    }

    // Determine if post needs approval
    const requiresApproval = topic.category?.requires_approval || false

    const { data: post, error } = await supabase
      .from('forum_posts')
      .insert({
        topic_id,
        author_id: user.id,
        content,
        content_type,
        parent_post_id,
        is_approved: !requiresApproval, // Auto-approve if category doesn't require approval
      })
      .select(
        `
        *,
        author:profiles!forum_posts_author_id_fkey(
          id,
          full_name,
          avatar_url
        ),
        parent_post:forum_posts!forum_posts_parent_post_id_fkey(
          id,
          content,
          author:profiles!forum_posts_author_id_fkey(
            id,
            full_name
          )
        )
      `
      )
      .single()

    if (error) {
      console.error('Error creating forum post:', error)
      return NextResponse.json({ error: 'Failed to create post' }, { status: 500 })
    }

    return NextResponse.json({ data: post })
  } catch (error) {
    console.error('Error in forum posts POST API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
