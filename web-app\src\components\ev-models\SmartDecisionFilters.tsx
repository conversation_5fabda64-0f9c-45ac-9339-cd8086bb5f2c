'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Brain,
  Target,
  MapPin,
  Home,
  Car,
  DollarSign,
  Zap,
  Users,
  Calendar,
  Sparkles,
  RotateCcw,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { formatPrice } from '@/utils/ev-buyer-guide'
import type { EVModelFilters } from '../../shared/types'

interface SmartDecisionFiltersProps {
  onFiltersChange: (filters: Partial<EVModelFilters>) => void
  onClose?: () => void
  className?: string
}

interface DecisionProfile {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  filters: Partial<EVModelFilters>
  additionalCriteria?: {
    dailyMiles?: number
    hasHomeCharging?: boolean
    primaryUse?: string
  }
}

interface LifestyleInputs {
  budget: number[]
  dailyMiles: number
  hasHomeCharging: boolean
  primaryUse: string
  familySize: number
  chargingFrequency: string
  drivingStyle: string
}

export function SmartDecisionFilters({
  onFiltersChange,
  onClose,
  className,
}: SmartDecisionFiltersProps) {
  const [activeTab, setActiveTab] = useState<'profiles' | 'lifestyle' | 'smart'>('profiles')
  const [lifestyleInputs, setLifestyleInputs] = useState<LifestyleInputs>({
    budget: [30000, 80000],
    dailyMiles: 40,
    hasHomeCharging: true,
    primaryUse: 'commuting',
    familySize: 2,
    chargingFrequency: 'daily',
    drivingStyle: 'efficient',
  })

  // Pre-defined buyer profiles
  const decisionProfiles: DecisionProfile[] = [
    {
      id: 'first-time-buyer',
      name: 'First-Time EV Buyer',
      description: 'New to EVs, looking for reliable and affordable options',
      icon: <Sparkles className="h-5 w-5" />,
      filters: {
        priceMax: '6000000', // $60k
        rangeMin: '250',
        featured: 'true',
      },
    },
    {
      id: 'daily-commuter',
      name: 'Daily Commuter',
      description: 'Efficient, reliable EVs for daily work commutes',
      icon: <Car className="h-5 w-5" />,
      filters: {
        priceMax: '5000000', // $50k
        rangeMin: '200',
        bodyType: 'sedan',
      },
    },
    {
      id: 'family-hauler',
      name: 'Family Vehicle',
      description: 'Spacious SUVs and crossovers for families',
      icon: <Users className="h-5 w-5" />,
      filters: {
        bodyType: 'suv',
        rangeMin: '250',
      },
    },
    {
      id: 'luxury-seeker',
      name: 'Luxury & Performance',
      description: 'Premium EVs with advanced features and performance',
      icon: <Target className="h-5 w-5" />,
      filters: {
        priceMin: '7000000', // $70k
        rangeMin: '300',
      },
    },
    {
      id: 'budget-conscious',
      name: 'Budget-Friendly',
      description: 'Affordable EVs with good value proposition',
      icon: <DollarSign className="h-5 w-5" />,
      filters: {
        priceMax: '4000000', // $40k
        bestValue: 'true',
      },
    },
    {
      id: 'long-distance',
      name: 'Long-Distance Traveler',
      description: 'High-range EVs for road trips and long commutes',
      icon: <MapPin className="h-5 w-5" />,
      filters: {
        rangeMin: '350',
      },
    },
  ]

  const handleProfileSelect = (profile: DecisionProfile) => {
    onFiltersChange(profile.filters)
  }

  const generateSmartRecommendations = () => {
    const {
      budget,
      dailyMiles,
      hasHomeCharging,
      primaryUse,
      familySize,
      chargingFrequency,
      drivingStyle,
    } = lifestyleInputs

    let filters: Partial<EVModelFilters> = {
      priceMin: budget[0].toString(),
      priceMax: budget[1].toString(),
    }

    // Range requirements based on daily miles and charging habits
    if (dailyMiles > 100 || !hasHomeCharging) {
      filters.rangeMin = '300'
    } else if (dailyMiles > 50) {
      filters.rangeMin = '200'
    } else {
      filters.rangeMin = '150'
    }

    // Body type based on family size and use case
    if (familySize > 4 || primaryUse === 'family') {
      filters.bodyType = 'suv'
    } else if (primaryUse === 'commuting' && familySize <= 2) {
      filters.bodyType = 'sedan'
    }

    // Performance preferences
    if (drivingStyle === 'performance') {
      // No specific filter, but could add acceleration filter in future
    } else if (drivingStyle === 'efficient') {
      // Prioritize efficiency - could add efficiency filter
    }

    onFiltersChange(filters)
  }

  const handleLifestyleChange = (key: keyof LifestyleInputs, value: any) => {
    setLifestyleInputs((prev) => ({ ...prev, [key]: value }))
  }

  const clearAllFilters = () => {
    onFiltersChange({
      make: undefined,
      bodyType: undefined,
      priceMin: undefined,
      priceMax: undefined,
      rangeMin: undefined,
      productionStatus: undefined,
      featured: undefined,
      bestValue: undefined,
    })
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          Smart Decision Filters
        </CardTitle>
        <div className="flex gap-2">
          <Button
            variant={activeTab === 'profiles' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setActiveTab('profiles')}
          >
            Buyer Profiles
          </Button>
          <Button
            variant={activeTab === 'lifestyle' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setActiveTab('lifestyle')}
          >
            Lifestyle Quiz
          </Button>
          <Button
            variant={activeTab === 'smart' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setActiveTab('smart')}
          >
            Smart Match
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {activeTab === 'profiles' && (
          <div className="space-y-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Choose a profile that matches your EV buying needs
            </p>
            <div className="grid gap-3 md:grid-cols-2">
              {decisionProfiles.map((profile) => (
                <Button
                  key={profile.id}
                  variant="outline"
                  className="h-auto justify-start p-4 text-left"
                  onClick={() => handleProfileSelect(profile)}
                >
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5 text-electric-600">{profile.icon}</div>
                    <div>
                      <div className="font-semibold">{profile.name}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {profile.description}
                      </div>
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'lifestyle' && (
          <div className="space-y-6">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Tell us about your lifestyle to get personalized recommendations
            </p>

            {/* Budget Range */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Budget Range
              </Label>
              <div className="px-3">
                <Slider
                  value={lifestyleInputs.budget}
                  onValueChange={(value) => handleLifestyleChange('budget', value)}
                  max={150000}
                  min={20000}
                  step={5000}
                  className="w-full"
                />
                <div className="mt-2 flex justify-between text-sm text-gray-600 dark:text-gray-400">
                  <span>{formatPrice(lifestyleInputs.budget[0] * 100)}</span>
                  <span>{formatPrice(lifestyleInputs.budget[1] * 100)}</span>
                </div>
              </div>
            </div>

            {/* Daily Miles */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Daily Driving Miles
              </Label>
              <div className="px-3">
                <Slider
                  value={[lifestyleInputs.dailyMiles]}
                  onValueChange={(value) => handleLifestyleChange('dailyMiles', value[0])}
                  max={200}
                  min={10}
                  step={10}
                  className="w-full"
                />
                <div className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                  {lifestyleInputs.dailyMiles} miles per day
                </div>
              </div>
            </div>

            {/* Primary Use */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                <Car className="h-4 w-4" />
                Primary Use
              </Label>
              <Select
                value={lifestyleInputs.primaryUse}
                onValueChange={(value) => handleLifestyleChange('primaryUse', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="commuting">Daily Commuting</SelectItem>
                  <SelectItem value="family">Family Transportation</SelectItem>
                  <SelectItem value="weekend">Weekend Trips</SelectItem>
                  <SelectItem value="business">Business Travel</SelectItem>
                  <SelectItem value="recreation">Recreation & Leisure</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Family Size */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Typical Passengers
              </Label>
              <div className="px-3">
                <Slider
                  value={[lifestyleInputs.familySize]}
                  onValueChange={(value) => handleLifestyleChange('familySize', value[0])}
                  max={8}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                  {lifestyleInputs.familySize}{' '}
                  {lifestyleInputs.familySize === 1 ? 'person' : 'people'}
                </div>
              </div>
            </div>

            {/* Home Charging */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                <Home className="h-4 w-4" />
                Home Charging Available
              </Label>
              <div className="flex gap-2">
                <Button
                  variant={lifestyleInputs.hasHomeCharging ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleLifestyleChange('hasHomeCharging', true)}
                >
                  Yes
                </Button>
                <Button
                  variant={!lifestyleInputs.hasHomeCharging ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleLifestyleChange('hasHomeCharging', false)}
                >
                  No
                </Button>
              </div>
            </div>

            <Button onClick={generateSmartRecommendations} className="w-full">
              Get My Recommendations
            </Button>
          </div>
        )}

        {activeTab === 'smart' && (
          <div className="space-y-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              AI-powered recommendations based on your preferences and market trends
            </p>
            <div className="dark:bg-electric-950 rounded-lg bg-electric-50 p-4">
              <h4 className="mb-2 font-semibold text-electric-900 dark:text-electric-100">
                Smart Recommendations Coming Soon
              </h4>
              <p className="text-sm text-electric-800 dark:text-electric-200">
                Our AI will analyze your preferences, driving patterns, and market data to suggest
                the perfect EV for you.
              </p>
            </div>
          </div>
        )}

        <div className="flex gap-2 border-t pt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={clearAllFilters}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Clear All
          </Button>
          {onClose && (
            <Button variant="outline" size="sm" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
