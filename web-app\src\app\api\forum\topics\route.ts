import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('category_id')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const sortBy = searchParams.get('sort') || 'last_activity'
    const isPinned = searchParams.get('pinned') === 'true'

    let query = supabase
      .from('forum_topics')
      .select(
        `
        *,
        author:profiles!forum_topics_author_id_fkey(
          id,
          full_name,
          avatar_url
        ),
        category:forum_categories!forum_topics_category_id_fkey(
          id,
          name,
          slug,
          color
        ),
        linked_ev_model:ev_models(
          id,
          make,
          model,
          year,
          trim
        ),
        linked_manufacturer:ev_manufacturers(
          id,
          name
        )
      `
      )
      .eq('is_approved', true)

    // Filter by category if specified
    if (categoryId) {
      query = query.eq('category_id', categoryId)
    }

    // Filter by pinned status if specified
    if (isPinned) {
      query = query.eq('is_pinned', true)
    }

    // Apply sorting
    switch (sortBy) {
      case 'newest':
        query = query.order('created_at', { ascending: false })
        break
      case 'oldest':
        query = query.order('created_at', { ascending: true })
        break
      case 'most_replies':
        query = query.order('reply_count', { ascending: false })
        break
      case 'most_views':
        query = query.order('view_count', { ascending: false })
        break
      default: // last_activity
        query = query.order('last_activity_at', { ascending: false })
    }

    // Apply pagination
    const offset = (page - 1) * limit
    query = query.range(offset, offset + limit - 1)

    const { data: topics, error } = await query

    if (error) {
      console.error('Error fetching forum topics:', error)
      return NextResponse.json({ error: 'Failed to fetch topics' }, { status: 500 })
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('forum_topics')
      .select('*', { count: 'exact', head: true })
      .eq('is_approved', true)

    if (categoryId) {
      countQuery = countQuery.eq('category_id', categoryId)
    }

    const { count, error: countError } = await countQuery

    if (countError) {
      console.error('Error counting topics:', countError)
    }

    return NextResponse.json({
      data: topics,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    })
  } catch (error) {
    console.error('Error in forum topics API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      category_id,
      title,
      content,
      content_type = 'markdown',
      linked_ev_model_id,
      linked_manufacturer_id,
      linked_charging_station_id,
      tags,
    } = body

    // Validate required fields
    if (!category_id || !title || !content) {
      return NextResponse.json(
        { error: 'Category ID, title, and content are required' },
        { status: 400 }
      )
    }

    // Check if category exists and is active
    const { data: category, error: categoryError } = await supabase
      .from('forum_categories')
      .select('id, requires_approval')
      .eq('id', category_id)
      .eq('is_active', true)
      .single()

    if (categoryError || !category) {
      return NextResponse.json({ error: 'Invalid category' }, { status: 400 })
    }

    // Generate slug from title
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 100)

    // Determine if post needs approval
    const requiresApproval = category.requires_approval

    const { data: topic, error } = await supabase
      .from('forum_topics')
      .insert({
        category_id,
        author_id: user.id,
        title,
        slug,
        content,
        content_type,
        linked_ev_model_id,
        linked_manufacturer_id,
        linked_charging_station_id,
        tags,
        is_approved: !requiresApproval, // Auto-approve if category doesn't require approval
      })
      .select(
        `
        *,
        author:profiles!forum_topics_author_id_fkey(
          id,
          full_name,
          avatar_url
        ),
        category:forum_categories!forum_topics_category_id_fkey(
          id,
          name,
          slug,
          color
        )
      `
      )
      .single()

    if (error) {
      console.error('Error creating forum topic:', error)
      return NextResponse.json({ error: 'Failed to create topic' }, { status: 500 })
    }

    return NextResponse.json({ data: topic })
  } catch (error) {
    console.error('Error in forum topics POST API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
