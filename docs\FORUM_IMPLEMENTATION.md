# Forum Implementation Guide for GreenMilesEV

## 🎯 Overview

The forum feature adds a comprehensive community discussion platform to the GreenMilesEV marketplace, allowing users to discuss electric vehicles, share experiences, ask questions, and build a vibrant EV community.

## 📊 Database Schema

### Core Tables

#### 1. `forum_categories`

Organizes discussions into themed sections.

```sql
- id (UUID, Primary Key)
- name (VARCHAR(100), UNIQUE) - Category display name
- description (TEXT) - Category description
- slug (VARCHAR(100), UNIQUE) - URL-friendly identifier
- icon (VARCHAR(50)) - Icon class or emoji
- color (VARCHAR(7)) - Hex color for theming
- sort_order (INTEGER) - Display order
- is_active (BOOLEAN) - Active/inactive status
- requires_approval (BOOLEAN) - Requires post moderation
- parent_category_id (UUID) - For subcategories
- topic_count, post_count (INTEGER) - Statistics
- last_post_id (UUID) - Reference to latest post
- last_activity_at (TIMESTAMPTZ) - Last activity timestamp
```

#### 2. `forum_topics`

Individual discussion threads within categories.

```sql
- id (UUID, Primary Key)
- category_id (UUID, FK to forum_categories)
- author_id (UUID, FK to profiles)
- title (VARCHAR(255)) - Topic title
- slug (VARCHAR(255)) - URL-friendly identifier
- content (TEXT) - Topic content
- content_type (VARCHAR(20)) - markdown/html/text
- is_pinned, is_locked, is_approved, is_featured (BOOLEAN)
- related_ev_model_id (UUID, FK to ev_models) - Link to EV models
- related_manufacturer_id (UUID, FK to ev_manufacturers)
- related_charging_station_id (UUID, FK to charging_stations)
- view_count, post_count, like_count, bookmark_count (INTEGER)
- tags (TEXT[]) - Topic tags
- meta_description (TEXT) - SEO description
```

#### 3. `forum_posts`

Individual posts/replies within topics.

```sql
- id (UUID, Primary Key)
- topic_id (UUID, FK to forum_topics)
- author_id (UUID, FK to profiles)
- parent_post_id (UUID, FK to forum_posts) - For threaded replies
- content (TEXT) - Post content
- content_type (VARCHAR(20)) - Content format
- raw_content (TEXT) - Original content for editing
- is_approved, is_solution, is_edited (BOOLEAN)
- edit_reason (TEXT) - Reason for editing
- like_count, dislike_count (INTEGER)
- reaction_count (JSONB) - Various reaction types
- post_number (INTEGER) - Sequential number in topic
- reply_count, thread_level (INTEGER) - Threading info
- attachments (JSONB) - File attachments
```

### Supporting Tables

#### 4. `forum_reactions`

User reactions to posts and topics.

```sql
- user_id (UUID, FK to profiles)
- post_id or topic_id (UUID) - Target content
- reaction_type (VARCHAR) - like/dislike/helpful/solved/funny/insightful
```

#### 5. `forum_bookmarks`

User bookmarks for topics and posts.

```sql
- user_id (UUID, FK to profiles)
- topic_id or post_id (UUID) - Bookmarked content
- notes (TEXT) - Personal notes
```

#### 6. `forum_moderators`

Moderator permissions and roles.

```sql
- user_id (UUID, FK to profiles)
- category_id (UUID, FK to forum_categories) - NULL for global
- role (VARCHAR) - moderator/admin/super_admin
- permissions (JSONB) - Specific permissions
- is_active (BOOLEAN)
```

#### 7. `forum_user_stats`

User activity statistics and rankings.

```sql
- user_id (UUID, FK to profiles)
- topic_count, post_count (INTEGER)
- like_count_received, solution_count (INTEGER)
- reputation_score (INTEGER)
- forum_rank (VARCHAR) - User rank/title
```

#### 8. `forum_flags`

Content reporting and moderation.

```sql
- flagger_id (UUID, FK to profiles)
- post_id or topic_id (UUID) - Flagged content
- flag_type (VARCHAR) - spam/inappropriate/off_topic/etc.
- reason (TEXT) - Detailed reason
- status (VARCHAR) - pending/reviewed/resolved/dismissed
```

## 🔐 Security & Permissions

### Row Level Security (RLS)

All forum tables have RLS enabled with policies for:

- **Public reading** of approved content
- **Authenticated user creation** of content
- **Owner-based editing** of own content
- **Moderator privileges** for content management
- **Private bookmarks** and user stats

### Moderator System

Three-tier moderation system:

- **Moderators**: Can moderate specific categories
- **Admins**: Can moderate all categories and manage moderators
- **Super Admins**: Full system control

### Content Approval

Optional content approval workflow:

- Categories can require post approval
- Flagged content system for community reporting
- Automated and manual moderation tools

## 🎨 EV Marketplace Integration

### Smart Linking

Topics can be linked to:

- **EV Models**: Discussions about specific vehicles
- **Manufacturers**: Brand-specific discussions
- **Charging Stations**: Location and network discussions

### Contextual Forums

Default categories tailored for EV community:

- **General Discussion** 💬
- **EV Models & Reviews** 🚗
- **Charging & Infrastructure** ⚡
- **Buying Advice** 🛒
- **Technical Support** 🔧
- **Community Meetups** 🤝
- **News & Updates** 📰

### Cross-Platform Features

- Forum content appears in EV model detail pages
- Charging station discussions linked to station pages
- User vehicle ownership displayed in profiles
- Purchase history influences recommendation credibility

## 🚀 Implementation Steps

### 1. Database Setup

```bash
# Apply the migrations
psql -f database/migrations/003_create_forum_tables.sql
psql -f database/migrations/004_create_forum_rls_policies.sql
```

### 2. Web Application Integration

#### API Routes (Next.js App Router)

```typescript
// web-app/src/app/api/forum/
├── categories/
│   └── route.ts           # GET, POST categories
├── topics/
│   ├── route.ts           # GET, POST topics
│   ├── [id]/
│   │   └── route.ts       # GET, PUT, DELETE specific topic
│   └── search/
│       └── route.ts       # Search topics
├── posts/
│   ├── route.ts           # GET, POST posts
│   └── [id]/
│       └── route.ts       # GET, PUT, DELETE specific post
├── reactions/
│   └── route.ts           # POST, DELETE reactions
└── bookmarks/
    └── route.ts           # GET, POST, DELETE bookmarks
```

#### Components Structure

```typescript
// web-app/src/components/forum/
├── ForumLayout.tsx        # Main forum layout
├── CategoryList.tsx       # Category navigation
├── TopicList.tsx          # Topic listing with pagination
├── TopicDetail.tsx        # Topic view with posts
├── PostEditor.tsx         # Rich text post editor
├── PostCard.tsx           # Individual post display
├── ReactionButtons.tsx    # Like/reaction interface
├── ForumSearch.tsx        # Forum search functionality
├── ModerationPanel.tsx    # Moderator tools
└── UserStats.tsx          # User forum statistics
```

#### Pages Structure

```typescript
// web-app/src/app/forum/
├── page.tsx                    # Forum home
├── category/
│   └── [slug]/
│       └── page.tsx           # Category view
├── topic/
│   └── [id]/
│       └── page.tsx           # Topic detail
├── search/
│   └── page.tsx               # Search results
├── user/
│   └── [id]/
│       └── page.tsx           # User forum profile
└── moderation/
    └── page.tsx               # Moderation dashboard
```

### 3. Mobile Application Integration

#### React Native Screens

```typescript
// mobile-new/app/forum/
├── _layout.tsx                # Forum stack navigator
├── index.tsx                  # Forum home
├── category/
│   └── [slug].tsx            # Category view
├── topic/
│   └── [id].tsx              # Topic detail
├── create-topic.tsx          # Create new topic
├── create-post.tsx           # Create new post
└── search.tsx                # Forum search
```

#### Mobile Components

```typescript
// mobile-new/components/forum/
├── ForumCategoryCard.tsx     # Category display card
├── TopicListItem.tsx         # Topic list item
├── PostView.tsx              # Post display
├── ReactionBar.tsx           # Mobile reaction interface
├── ForumSearchBar.tsx        # Search component
├── UserAvatar.tsx            # User avatar with stats
└── MobilePostEditor.tsx      # Mobile-optimized editor
```

### 4. Shared Logic

#### Hooks

```typescript
// Shared hooks for both web and mobile
export const useForumCategories = () => {
  /* ... */
};
export const useForumTopics = (categoryId?: string) => {
  /* ... */
};
export const useForumPosts = (topicId: string) => {
  /* ... */
};
export const useForumSearch = () => {
  /* ... */
};
export const useForumReactions = () => {
  /* ... */
};
export const useForumBookmarks = () => {
  /* ... */
};
export const useForumModeration = () => {
  /* ... */
};
```

#### API Service

```typescript
// web-app/src/lib/forum-api.ts
export const forumApi = {
  // Categories
  getCategories: () => Promise<ForumCategory[]>
  createCategory: (data: CreateCategoryRequest) => Promise<ForumCategory>

  // Topics
  getTopics: (params: ForumSearchParams) => Promise<ForumSearchResult>
  getTopic: (id: string) => Promise<ForumTopicWithDetails>
  createTopic: (data: CreateTopicRequest) => Promise<ForumTopic>
  updateTopic: (id: string, data: UpdateTopicRequest) => Promise<ForumTopic>

  // Posts
  getPosts: (topicId: string) => Promise<ForumPostWithDetails[]>
  createPost: (data: CreatePostRequest) => Promise<ForumPost>
  updatePost: (id: string, data: UpdatePostRequest) => Promise<ForumPost>

  // Reactions
  addReaction: (type: string, targetId: string, targetType: 'post' | 'topic') => Promise<void>
  removeReaction: (targetId: string, targetType: 'post' | 'topic') => Promise<void>

  // Bookmarks
  addBookmark: (targetId: string, targetType: 'post' | 'topic') => Promise<void>
  removeBookmark: (targetId: string, targetType: 'post' | 'topic') => Promise<void>
  getUserBookmarks: () => Promise<ForumBookmark[]>
}
```

## 📱 Mobile-Specific Considerations

### Native Features

- **Push notifications** for replies and mentions
- **Offline reading** with sync when online
- **Voice-to-text** for post creation
- **Image/video uploads** from camera or gallery
- **Haptic feedback** for reactions

### Performance Optimizations

- **Lazy loading** of posts and images
- **Virtual scrolling** for long topic lists
- **Image compression** before upload
- **Optimistic updates** for reactions

## 🔧 Advanced Features

### Search & Discovery

- **Full-text search** across topics and posts
- **Tag-based filtering** and discovery
- **Related content suggestions**
- **Trending topics** based on activity

### Gamification

- **User reputation system** based on helpful contributions
- **Achievement badges** for various milestones
- **Leaderboards** for top contributors
- **Solution marking** for helpful answers

### Content Management

- **Rich text editor** with markdown support
- **File attachments** and image embedding
- **Content versioning** and edit history
- **Bulk moderation tools**

### Analytics & Insights

- **Topic engagement metrics**
- **User activity analytics**
- **Popular content identification**
- **Community health monitoring**

## 🚦 Next Steps

1. **Apply database migrations** to your Supabase project
2. **Set up RLS policies** for proper security
3. **Implement basic API routes** for CRUD operations
4. **Create core UI components** for topic and post display
5. **Add search functionality** and advanced filtering
6. **Implement moderation tools** and user management
7. **Add mobile-specific features** and optimizations
8. **Test thoroughly** across both platforms

## 📚 Additional Resources

- [Forum Database Schema](./database/migrations/003_create_forum_tables.sql)
- [RLS Policies](./database/migrations/004_create_forum_rls_policies.sql)
- [TypeScript Types](../web-app/src/shared/types/forum.ts)
- [Supabase Documentation](https://supabase.com/docs)
- [Next.js App Router](https://nextjs.org/docs/app)
- [React Native Navigation](https://reactnavigation.org/)

---

This forum implementation provides a solid foundation for building a thriving EV community within your marketplace platform! 🚗⚡
