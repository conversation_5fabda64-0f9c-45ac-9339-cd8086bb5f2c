'use client'

import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface Tab {
  id: string
  label: string
  description: string
}

interface EVModelTabsProps {
  tabs: readonly Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
  className?: string
}

export function EVModelTabs({
  tabs,
  activeTab,
  onTabChange,
  className
}: EVModelTabsProps) {
  return (
    <div className={cn('overflow-x-auto', className)}>
      <nav className="flex space-x-1 py-4" role="tablist">
        {tabs.map((tab) => (
          <Button
            key={tab.id}
            variant={activeTab === tab.id ? 'default' : 'ghost'}
            onClick={() => onTabChange(tab.id)}
            className={cn(
              'flex-shrink-0 flex-col items-start gap-1 h-auto py-3 px-4 text-left',
              activeTab === tab.id
                ? 'bg-electric-600 text-white hover:bg-electric-700'
                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100'
            )}
            role="tab"
            aria-selected={activeTab === tab.id}
            aria-controls={`tabpanel-${tab.id}`}
          >
            <span className="font-medium">{tab.label}</span>
            <span className={cn(
              'text-xs',
              activeTab === tab.id
                ? 'text-electric-100'
                : 'text-gray-500 dark:text-gray-500'
            )}>
              {tab.description}
            </span>
          </Button>
        ))}
      </nav>
    </div>
  )
}
