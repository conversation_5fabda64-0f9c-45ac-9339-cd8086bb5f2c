import type { EVBodyType, ProductionStatus, UseCase, PriorityFactor } from '../../types'

// ===== EV BUYER GUIDE CONSTANTS =====

// Charging Connector Types
export const CHARGING_CONNECTOR_TYPES = [
  'CCS',
  'CHAdeMO',
  'Tesla Supercharger',
  'Type 2',
  'J1772',
  'GB/T',
] as const

// Price Ranges for Filtering (in cents)
export const PRICE_RANGES = [
  { label: 'Under $30k', min: 0, max: 3000000 },
  { label: '$30k - $50k', min: 3000000, max: 5000000 },
  { label: '$50k - $75k', min: 5000000, max: 7500000 },
  { label: '$75k - $100k', min: 7500000, max: 10000000 },
  { label: 'Over $100k', min: 10000000, max: 99999999 },
] as const

// Range Categories for Filtering
export const RANGE_CATEGORIES = [
  { label: 'Under 200 miles', min: 0, max: 199 },
  { label: '200-300 miles', min: 200, max: 300 },
  { label: '300-400 miles', min: 300, max: 400 },
  { label: 'Over 400 miles', min: 400, max: 999 },
] as const

// Charging Speed Categories
export const CHARGING_SPEED_CATEGORIES = [
  { label: 'Slow (Under 50kW)', min: 0, max: 49 },
  { label: 'Fast (50-150kW)', min: 50, max: 150 },
  { label: 'Ultra Fast (150kW+)', min: 150, max: 999 },
] as const

// Body Type Display Names
export const BODY_TYPE_LABELS: Record<EVBodyType, string> = {
  sedan: 'Sedan',
  suv: 'SUV',
  hatchback: 'Hatchback',
  truck: 'Pickup Truck',
  coupe: 'Coupe',
  wagon: 'Wagon',
  convertible: 'Convertible',
  crossover: 'Crossover',
  van: 'Van',
}

// Production Status Display Names
export const PRODUCTION_STATUS_LABELS: Record<ProductionStatus, string> = {
  current: 'Currently Available',
  discontinued: 'Discontinued',
  concept: 'Concept',
  upcoming: 'Coming Soon',
}

// Use Case Display Names
export const USE_CASE_LABELS: Record<UseCase, string> = {
  commuting: 'Daily Commuting',
  family: 'Family Vehicle',
  performance: 'Performance Driving',
  luxury: 'Luxury Experience',
  utility: 'Work & Utility',
  eco_friendly: 'Environmental Focus',
  first_ev: 'First Electric Vehicle',
  upgrade: 'Upgrading from Gas',
}

// Priority Factor Display Names
export const PRIORITY_FACTOR_LABELS: Record<PriorityFactor, string> = {
  price: 'Purchase Price',
  range: 'Driving Range',
  charging_speed: 'Charging Speed',
  performance: 'Performance',
  features: 'Technology & Features',
  safety: 'Safety Ratings',
  brand: 'Brand Reputation',
  efficiency: 'Energy Efficiency',
  size: 'Vehicle Size',
  cargo_space: 'Cargo Space',
}

// Default Priority Weights for Different Use Cases
export const DEFAULT_PRIORITY_WEIGHTS: Record<UseCase, Record<PriorityFactor, number>> = {
  commuting: {
    price: 25,
    range: 20,
    charging_speed: 15,
    efficiency: 15,
    performance: 5,
    features: 10,
    safety: 5,
    brand: 3,
    size: 1,
    cargo_space: 1,
  },
  family: {
    price: 20,
    range: 15,
    charging_speed: 10,
    efficiency: 10,
    performance: 5,
    features: 10,
    safety: 20,
    brand: 5,
    size: 3,
    cargo_space: 2,
  },
  performance: {
    price: 10,
    range: 10,
    charging_speed: 15,
    efficiency: 5,
    performance: 30,
    features: 15,
    safety: 10,
    brand: 3,
    size: 1,
    cargo_space: 1,
  },
  luxury: {
    price: 5,
    range: 15,
    charging_speed: 10,
    efficiency: 5,
    performance: 15,
    features: 25,
    safety: 15,
    brand: 8,
    size: 1,
    cargo_space: 1,
  },
  utility: {
    price: 20,
    range: 15,
    charging_speed: 10,
    efficiency: 15,
    performance: 10,
    features: 5,
    safety: 10,
    brand: 3,
    size: 7,
    cargo_space: 5,
  },
  eco_friendly: {
    price: 15,
    range: 20,
    charging_speed: 10,
    efficiency: 25,
    performance: 5,
    features: 10,
    safety: 10,
    brand: 3,
    size: 1,
    cargo_space: 1,
  },
  first_ev: {
    price: 25,
    range: 20,
    charging_speed: 15,
    efficiency: 10,
    performance: 5,
    features: 10,
    safety: 10,
    brand: 3,
    size: 1,
    cargo_space: 1,
  },
  upgrade: {
    price: 20,
    range: 18,
    charging_speed: 12,
    efficiency: 12,
    performance: 8,
    features: 12,
    safety: 12,
    brand: 4,
    size: 1,
    cargo_space: 1,
  },
}

// Filter Presets for Quick Selection
export const FILTER_PRESETS = [
  {
    name: 'Budget Friendly',
    description: 'Affordable EVs under $50k',
    filters: {
      priceMax: 5000000,
      productionStatus: 'current' as ProductionStatus,
    },
  },
  {
    name: 'Long Range',
    description: 'EVs with 300+ miles range',
    filters: {
      rangeMin: 300,
      productionStatus: 'current' as ProductionStatus,
    },
  },
  {
    name: 'Fast Charging',
    description: 'EVs with 150kW+ charging',
    filters: {
      chargingSpeedMin: 150,
      productionStatus: 'current' as ProductionStatus,
    },
  },
  {
    name: 'Family SUVs',
    description: 'Family-friendly SUVs and crossovers',
    filters: {
      bodyTypes: ['suv', 'crossover'] as EVBodyType[],
      seatingCapacity: 5,
      productionStatus: 'current' as ProductionStatus,
    },
  },
  {
    name: 'Performance',
    description: 'High-performance EVs',
    filters: {
      accelerationMax: 4.0,
      productionStatus: 'current' as ProductionStatus,
    },
  },
  {
    name: 'Best Value',
    description: "Editor's choice for best value",
    filters: {
      bestValue: true,
      productionStatus: 'current' as ProductionStatus,
    },
  },
] as const

// Comparison Categories for Side-by-Side View
export const COMPARISON_CATEGORIES = [
  {
    name: 'Pricing',
    fields: ['price_msrp', 'price_base', 'total_cost_ownership'],
  },
  {
    name: 'Range & Efficiency',
    fields: [
      'range_epa_miles',
      'range_real_world_miles',
      'efficiency_mpge',
      'battery_capacity_kwh',
    ],
  },
  {
    name: 'Charging',
    fields: [
      'charging_speed_dc_kw',
      'charging_speed_ac_kw',
      'charging_time_10_80_minutes',
      'charging_ports',
    ],
  },
  {
    name: 'Performance',
    fields: ['acceleration_0_60_mph', 'top_speed_mph', 'motor_power_hp', 'drivetrain'],
  },
  {
    name: 'Dimensions',
    fields: [
      'length_inches',
      'width_inches',
      'height_inches',
      'cargo_volume_cubic_ft',
      'seating_capacity',
    ],
  },
  {
    name: 'Safety & Warranty',
    fields: ['safety_ratings', 'warranty_info'],
  },
] as const

// Popular EV Makes (for autocomplete and suggestions)
export const POPULAR_EV_MAKES = [
  'Tesla',
  'BMW',
  'Mercedes-Benz',
  'Audi',
  'Nissan',
  'Ford',
  'Chevrolet',
  'Volkswagen',
  'Hyundai',
  'Kia',
  'Porsche',
  'Volvo',
  'Polestar',
  'Lucid',
  'Rivian',
] as const

// Recommended Daily Driving Miles Categories
export const DAILY_DRIVING_CATEGORIES = [
  { label: 'Under 25 miles', value: 25 },
  { label: '25-50 miles', value: 50 },
  { label: '50-100 miles', value: 100 },
  { label: '100-150 miles', value: 150 },
  { label: 'Over 150 miles', value: 200 },
] as const

// Budget Categories for Preferences
export const BUDGET_CATEGORIES = [
  { label: 'Under $30,000', min: 0, max: 3000000 },
  { label: '$30,000 - $50,000', min: 3000000, max: 5000000 },
  { label: '$50,000 - $75,000', min: 5000000, max: 7500000 },
  { label: '$75,000 - $100,000', min: 7500000, max: 10000000 },
  { label: '$100,000 - $150,000', min: 10000000, max: 15000000 },
  { label: 'Over $150,000', min: 15000000, max: 99999999 },
] as const

// API Pagination Defaults
export const DEFAULT_PAGE_SIZE = 20
export const MAX_PAGE_SIZE = 100
export const MAX_COMPARISON_MODELS = 4

// Search Configuration
export const MIN_SEARCH_LENGTH = 2
export const SEARCH_DEBOUNCE_MS = 300
export const MAX_SEARCH_SUGGESTIONS = 5

// Image Placeholder URLs
export const PLACEHOLDER_IMAGES = {
  ev_model:
    'https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=800&h=600&fit=crop&crop=center',
  manufacturer_logo: 'https://via.placeholder.com/100x100/6366f1/ffffff?text=Logo',
  no_image: 'https://via.placeholder.com/400x300/e5e7eb/9ca3af?text=No+Image',
} as const

// Feature Categories for EV Models
export const FEATURE_CATEGORIES = [
  'Autopilot/Self-Driving',
  'Premium Audio',
  'Panoramic Roof',
  'Heated Seats',
  'Ventilated Seats',
  'Wireless Charging',
  'Premium Interior',
  'Air Suspension',
  'Towing Package',
  'Off-Road Package',
  'Performance Package',
  'Cold Weather Package',
] as const
