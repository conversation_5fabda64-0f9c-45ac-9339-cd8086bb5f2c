'use client'

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react'
import type { EVModel } from '@/shared/types'

// Maximum number of models that can be compared at once
const MAX_COMPARISON_MODELS = 4

// Comparison state interface
interface ComparisonState {
  models: EVModel[]
  isOpen: boolean
  maxReached: boolean
}

// Action types for the comparison reducer
type ComparisonAction =
  | { type: 'ADD_MODEL'; payload: EVModel }
  | { type: 'REMOVE_MODEL'; payload: string } // model ID
  | { type: 'CLEAR_ALL' }
  | { type: 'TOGGLE_PANEL' }
  | { type: 'OPEN_PANEL' }
  | { type: 'CLOSE_PANEL' }
  | { type: 'LOAD_FROM_STORAGE'; payload: EVModel[] }

// Initial state
const initialState: ComparisonState = {
  models: [],
  isOpen: false,
  maxReached: false
}

// Comparison reducer
function comparisonReducer(state: ComparisonState, action: ComparisonAction): ComparisonState {
  switch (action.type) {
    case 'ADD_MODEL': {
      // Don't add if already exists or max reached
      if (state.models.some(model => model.id === action.payload.id)) {
        return state
      }
      
      if (state.models.length >= MAX_COMPARISON_MODELS) {
        return { ...state, maxReached: true }
      }

      const newModels = [...state.models, action.payload]
      return {
        ...state,
        models: newModels,
        maxReached: newModels.length >= MAX_COMPARISON_MODELS
      }
    }

    case 'REMOVE_MODEL': {
      const newModels = state.models.filter(model => model.id !== action.payload)
      return {
        ...state,
        models: newModels,
        maxReached: false,
        isOpen: newModels.length > 0 ? state.isOpen : false
      }
    }

    case 'CLEAR_ALL':
      return {
        ...state,
        models: [],
        maxReached: false,
        isOpen: false
      }

    case 'TOGGLE_PANEL':
      return {
        ...state,
        isOpen: state.models.length > 0 ? !state.isOpen : false
      }

    case 'OPEN_PANEL':
      return {
        ...state,
        isOpen: state.models.length > 0
      }

    case 'CLOSE_PANEL':
      return {
        ...state,
        isOpen: false
      }

    case 'LOAD_FROM_STORAGE':
      return {
        ...state,
        models: action.payload.slice(0, MAX_COMPARISON_MODELS),
        maxReached: action.payload.length >= MAX_COMPARISON_MODELS
      }

    default:
      return state
  }
}

// Context interface
interface ComparisonContextType {
  state: ComparisonState
  addModel: (model: EVModel) => void
  removeModel: (modelId: string) => void
  clearAll: () => void
  togglePanel: () => void
  openPanel: () => void
  closePanel: () => void
  isModelInComparison: (modelId: string) => boolean
  canAddMore: boolean
  comparisonCount: number
}

// Create context
const ComparisonContext = createContext<ComparisonContextType | undefined>(undefined)

// Local storage key
const STORAGE_KEY = 'ev-comparison-models'

// Provider component
interface ComparisonProviderProps {
  children: ReactNode
}

export function ComparisonProvider({ children }: ComparisonProviderProps) {
  const [state, dispatch] = useReducer(comparisonReducer, initialState)

  // Load from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const models = JSON.parse(stored) as EVModel[]
        dispatch({ type: 'LOAD_FROM_STORAGE', payload: models })
      }
    } catch (error) {
      console.error('Failed to load comparison models from storage:', error)
    }
  }, [])

  // Save to localStorage whenever models change
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state.models))
    } catch (error) {
      console.error('Failed to save comparison models to storage:', error)
    }
  }, [state.models])

  // Action creators
  const addModel = (model: EVModel) => {
    dispatch({ type: 'ADD_MODEL', payload: model })
  }

  const removeModel = (modelId: string) => {
    dispatch({ type: 'REMOVE_MODEL', payload: modelId })
  }

  const clearAll = () => {
    dispatch({ type: 'CLEAR_ALL' })
  }

  const togglePanel = () => {
    dispatch({ type: 'TOGGLE_PANEL' })
  }

  const openPanel = () => {
    dispatch({ type: 'OPEN_PANEL' })
  }

  const closePanel = () => {
    dispatch({ type: 'CLOSE_PANEL' })
  }

  const isModelInComparison = (modelId: string) => {
    return state.models.some(model => model.id === modelId)
  }

  const canAddMore = state.models.length < MAX_COMPARISON_MODELS
  const comparisonCount = state.models.length

  const contextValue: ComparisonContextType = {
    state,
    addModel,
    removeModel,
    clearAll,
    togglePanel,
    openPanel,
    closePanel,
    isModelInComparison,
    canAddMore,
    comparisonCount
  }

  return (
    <ComparisonContext.Provider value={contextValue}>
      {children}
    </ComparisonContext.Provider>
  )
}

// Custom hook to use comparison context
export function useComparison() {
  const context = useContext(ComparisonContext)
  if (context === undefined) {
    throw new Error('useComparison must be used within a ComparisonProvider')
  }
  return context
}

// Export constants for use in other components
export { MAX_COMPARISON_MODELS }
