import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  StyleSheet,
} from "react-native";
import { Link } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useAuth } from "@/contexts/AuthContext";
import { Colors } from "@/constants/Colors";

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const { resetPassword } = useAuth();

  const handleResetPassword = async () => {
    if (!email.trim()) {
      Alert.alert("Error", "Please enter your email address");
      return;
    }

    setLoading(true);
    try {
      const { error } = await resetPassword(email);
      if (error) {
        Alert.alert("Reset Failed", error.message);
      } else {
        setSuccess(true);
      }
    } catch (err) {
      Alert.alert("Error", "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <View style={styles.container}>
        <View style={styles.innerContainer}>
          <View style={styles.formContainer}>
            <View style={{ alignItems: "center" }}>
              <View style={styles.iconContainer}>
                <Ionicons name="mail" size={32} color={Colors.light.tint} />
              </View>
              <Text style={styles.subHeaderText}>Check your email</Text>
              <Text style={styles.promptText}>
                We&apos;ve sent a password reset link to{"\n"}
                <Text style={{ fontWeight: "bold" }}>{email}</Text>
              </Text>
              <Text
                style={[
                  styles.hintText,
                  { textAlign: "center", marginBottom: 24 },
                ]}
              >
                Didn&apos;t receive the email? Check your spam folder or try
                again.
              </Text>

              <View style={{ width: "100%" }}>
                <TouchableOpacity
                  onPress={() => setSuccess(false)}
                  style={[styles.button, styles.tryAgainButton]}
                >
                  <Text style={[styles.buttonText, styles.tryAgainButtonText]}>
                    Try again
                  </Text>
                </TouchableOpacity>
                <Link href="/auth/signin" asChild>
                  <TouchableOpacity style={[styles.button, { marginTop: 12 }]}>
                    <Text style={styles.buttonText}>Back to Sign In</Text>
                  </TouchableOpacity>
                </Link>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.innerContainer}>
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <Ionicons name="flash" size={32} color={Colors.light.tint} />
              <Text style={styles.headerText}>GreenMilesEV</Text>
            </View>
            <Text style={styles.subHeaderText}>Reset your password</Text>
            <Text style={styles.promptText}>
              Enter your email to receive a reset link
            </Text>
          </View>

          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>Forgot Password</Text>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Email</Text>
              <View style={styles.inputWrapper}>
                <TextInput
                  value={email}
                  onChangeText={setEmail}
                  placeholder="Enter your email address"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                  style={styles.input}
                />
              </View>
            </View>

            <TouchableOpacity
              onPress={handleResetPassword}
              disabled={loading || !email.trim()}
              style={[
                styles.button,
                (loading || !email.trim()) && styles.buttonDisabled,
              ]}
            >
              {loading ? (
                <View style={styles.loader}>
                  <ActivityIndicator color="white" size="small" />
                  <Text style={styles.buttonText}>Sending reset link...</Text>
                </View>
              ) : (
                <Text style={styles.buttonText}>Send Reset Link</Text>
              )}
            </TouchableOpacity>

            <View style={styles.backButtonContainer}>
              <Link href="/auth/signin" asChild>
                <TouchableOpacity disabled={loading} style={styles.backButton}>
                  <Ionicons
                    name="arrow-back"
                    size={16}
                    color={Colors.light.tint}
                  />
                  <Text style={styles.signinLink}>Back to Sign In</Text>
                </TouchableOpacity>
              </Link>
            </View>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f0fdf4",
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  innerContainer: {
    flex: 1,
    justifyContent: "center",
    paddingVertical: 48,
  },
  header: {
    alignItems: "center",
    marginBottom: 32,
  },
  logoContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  headerText: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#1f2937",
    marginLeft: 8,
  },
  subHeaderText: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 8,
    textAlign: "center",
  },
  promptText: {
    color: "#4b5563",
    textAlign: "center",
  },
  formContainer: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 24,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#374151",
    marginBottom: 8,
  },
  inputWrapper: {
    backgroundColor: "#f9fafb",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e5e7eb",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  input: {
    flex: 1,
    color: "#1f2937",
    fontSize: 16,
  },
  button: {
    backgroundColor: Colors.light.tint,
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: "center",
    marginTop: 16,
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  loader: {
    flexDirection: "row",
    alignItems: "center",
  },
  buttonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 16,
    marginLeft: 8,
  },
  backButtonContainer: {
    marginTop: 24,
    alignItems: "center",
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
  },
  signinLink: {
    color: Colors.light.tint,
    fontWeight: "500",
    marginLeft: 8,
  },
  iconContainer: {
    backgroundColor: "#dcfce7",
    borderRadius: 50,
    padding: 16,
    marginBottom: 16,
  },
  hintText: {
    fontSize: 14,
    color: "#6b7280",
    marginTop: 4,
  },
  tryAgainButton: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#e5e7eb",
  },
  tryAgainButtonText: {
    color: "#374151",
  },
});
