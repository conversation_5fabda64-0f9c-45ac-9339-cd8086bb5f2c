import React, { useState } from "react";
import { View, Text, ScrollView, StyleSheet } from "react-native";
import Slider from "@react-native-community/slider";
import { EVModel } from "../../../types";

interface EVDetailsCostAnalysisProps {
  model: EVModel;
}

export function EVDetailsCostAnalysis({ model }: EVDetailsCostAnalysisProps) {
  const [loanTerm, setLoanTerm] = useState(60); // months
  const [downPayment, setDownPayment] = useState(20); // percentage
  const [interestRate, setInterestRate] = useState(4.5); // percentage
  const [milesPerYear, setMilesPerYear] = useState(12000);
  const [electricityRate, setElectricityRate] = useState(0.13); // per kWh
  const [gasPrice, setGasPrice] = useState(3.5); // per gallon

  const basePrice = model.price_msrp ? model.price_msrp / 100 : 0;

  // Calculate financing
  const downPaymentAmount = basePrice * (downPayment / 100);
  const loanAmount = basePrice - downPaymentAmount;
  const monthlyRate = interestRate / 100 / 12;
  const monthlyPayment =
    loanAmount > 0
      ? (loanAmount * monthlyRate * Math.pow(1 + monthlyRate, loanTerm)) /
        (Math.pow(1 + monthlyRate, loanTerm) - 1)
      : 0;

  // Calculate energy costs
  const annualEnergyConsumption = model.efficiency_mpge
    ? (milesPerYear / model.efficiency_mpge) * 33.7 // kWh equivalent
    : 0;
  const annualElectricityCost = annualEnergyConsumption * electricityRate;

  // Calculate gas car comparison
  const averageGasMPG = 28; // Average new car MPG
  const annualGasConsumption = milesPerYear / averageGasMPG;
  const annualGasCost = annualGasConsumption * gasPrice;
  const annualFuelSavings = annualGasCost - annualElectricityCost;

  // 5-year total cost of ownership
  const fiveYearFinancing = monthlyPayment * Math.min(loanTerm, 60);
  const fiveYearElectricity = annualElectricityCost * 5;
  const fiveYearMaintenance = 2500; // Estimated EV maintenance over 5 years
  const fiveYearInsurance = 6000; // Estimated insurance over 5 years
  const fiveYearDepreciation = basePrice * 0.6; // 60% depreciation
  const fiveYearTotalCost =
    fiveYearFinancing +
    fiveYearElectricity +
    fiveYearMaintenance +
    fiveYearInsurance;

  // Gas car 5-year comparison
  const gasCarPrice = basePrice * 0.7; // Assume comparable gas car is 30% less
  const fiveYearGasCost = annualGasCost * 5;
  const fiveYearGasCarMaintenance = 8000; // Higher maintenance for gas cars
  const fiveYearGasCarTotal =
    gasCarPrice +
    fiveYearGasCost +
    fiveYearGasCarMaintenance +
    fiveYearInsurance;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const CostCard = ({
    title,
    amount,
    subtitle,
    colorStyle,
  }: {
    title: string;
    amount: number;
    subtitle?: string;
    colorStyle?: object;
  }) => (
    <View style={styles.costCard}>
      <Text style={styles.costCardTitle}>{title}</Text>
      <Text style={[styles.costCardAmount, colorStyle]}>
        {formatCurrency(amount)}
      </Text>
      {subtitle && <Text style={styles.costCardSubtitle}>{subtitle}</Text>}
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Purchase Price</Text>
        <View style={styles.priceDetails}>
          <View style={styles.priceRow}>
            <Text style={styles.priceLabel}>MSRP</Text>
            <Text style={styles.priceValue}>{formatCurrency(basePrice)}</Text>
          </View>

          {model.price_base && model.price_base !== model.price_msrp && (
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Base Price</Text>
              <Text style={styles.priceValue}>
                {formatCurrency(model.price_base / 100)}
              </Text>
            </View>
          )}

          <View style={styles.taxCreditRow}>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Federal Tax Credit</Text>
              <Text style={[styles.priceValue, styles.taxCreditText]}>
                -$7,500
              </Text>
            </View>
            <Text style={styles.disclaimerText}>
              *Subject to eligibility and availability
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>Financing Calculator</Text>
        <View style={styles.sliderContainer}>
          <View style={styles.sliderLabelRow}>
            <Text style={styles.sliderLabel}>Down Payment</Text>
            <Text style={styles.sliderValue}>
              {downPayment}% ({formatCurrency(downPaymentAmount)})
            </Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={0}
            maximumValue={50}
            step={5}
            value={downPayment}
            onValueChange={setDownPayment}
            minimumTrackTintColor="#22c55e"
            maximumTrackTintColor="#d1d5db"
            thumbTintColor="#22c55e"
          />
        </View>

        <View style={styles.sliderContainer}>
          <View style={styles.sliderLabelRow}>
            <Text style={styles.sliderLabel}>Loan Term</Text>
            <Text style={styles.sliderValue}>{loanTerm} months</Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={24}
            maximumValue={84}
            step={12}
            value={loanTerm}
            onValueChange={setLoanTerm}
            minimumTrackTintColor="#22c55e"
            maximumTrackTintColor="#d1d5db"
            thumbTintColor="#22c55e"
          />
        </View>

        <View style={styles.sliderContainer}>
          <View style={styles.sliderLabelRow}>
            <Text style={styles.sliderLabel}>Interest Rate</Text>
            <Text style={styles.sliderValue}>{interestRate.toFixed(1)}%</Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={1.0}
            maximumValue={10.0}
            step={0.1}
            value={interestRate}
            onValueChange={setInterestRate}
            minimumTrackTintColor="#22c55e"
            maximumTrackTintColor="#d1d5db"
            thumbTintColor="#22c55e"
          />
        </View>

        <View style={styles.monthlyPaymentContainer}>
          <Text style={styles.monthlyPaymentLabel}>
            Estimated Monthly Payment
          </Text>
          <Text style={styles.monthlyPaymentValue}>
            {formatCurrency(monthlyPayment)}
          </Text>
        </View>
      </View>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>Operating Costs</Text>

        <View style={styles.sliderContainer}>
          <View style={styles.sliderLabelRow}>
            <Text style={styles.sliderLabel}>Annual Mileage</Text>
            <Text style={styles.sliderValue}>
              {milesPerYear.toLocaleString()} miles
            </Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={5000}
            maximumValue={25000}
            step={1000}
            value={milesPerYear}
            onValueChange={setMilesPerYear}
            minimumTrackTintColor="#22c55e"
            maximumTrackTintColor="#d1d5db"
            thumbTintColor="#22c55e"
          />
        </View>

        <View style={styles.sliderContainer}>
          <View style={styles.sliderLabelRow}>
            <Text style={styles.sliderLabel}>Electricity Rate</Text>
            <Text style={styles.sliderValue}>
              ${electricityRate.toFixed(2)}/kWh
            </Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={0.08}
            maximumValue={0.3}
            step={0.01}
            value={electricityRate}
            onValueChange={setElectricityRate}
            minimumTrackTintColor="#22c55e"
            maximumTrackTintColor="#d1d5db"
            thumbTintColor="#22c55e"
          />
        </View>

        <View style={{ gap: 12 }}>
          <View style={{ flexDirection: "row", gap: 12 }}>
            <CostCard
              title="Annual Electricity"
              amount={annualElectricityCost}
              subtitle={`${annualEnergyConsumption.toFixed(0)} kWh/year`}
            />
            <CostCard
              title="Annual Savings"
              amount={annualFuelSavings}
              subtitle="vs. gas car"
              colorStyle={styles.savingsText}
            />
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    backgroundColor: "white",
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
    marginBottom: 16,
  },
  priceDetails: {
    gap: 12,
  },
  priceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  priceLabel: {
    color: "#374151",
  },
  priceValue: {
    fontWeight: "600",
    color: "#111827",
  },
  taxCreditRow: {
    borderTopWidth: 1,
    borderTopColor: "#e5e7eb",
    paddingTop: 12,
  },
  taxCreditText: {
    color: "#16a34a",
  },
  disclaimerText: {
    fontSize: 12,
    color: "#6b7280",
    marginTop: 4,
  },
  sliderContainer: {
    marginBottom: 16,
  },
  sliderLabelRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  sliderLabel: {
    color: "#374151",
  },
  sliderValue: {
    fontWeight: "500",
    color: "#111827",
  },
  slider: {
    width: "100%",
    height: 40,
  },
  monthlyPaymentContainer: {
    backgroundColor: "#eef2ff",
    borderRadius: 8,
    padding: 16,
  },
  monthlyPaymentLabel: {
    textAlign: "center",
    fontSize: 14,
    color: "#4b5563",
    marginBottom: 4,
  },
  monthlyPaymentValue: {
    textAlign: "center",
    fontSize: 24,
    fontWeight: "bold",
    color: "#4f46e5",
  },
  costCard: {
    backgroundColor: "#f9fafb",
    borderRadius: 8,
    padding: 12,
    flex: 1,
  },
  costCardTitle: {
    fontSize: 14,
    color: "#4b5563",
    marginBottom: 4,
  },
  costCardAmount: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#111827",
  },
  costCardSubtitle: {
    fontSize: 12,
    color: "#6b7280",
    marginTop: 4,
  },
  savingsText: {
    color: "#16a34a",
  },
});
