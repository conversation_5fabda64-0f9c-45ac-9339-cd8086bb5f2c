'use client'

import { Toaster } from 'react-hot-toast'

export function ToastProvider() {
  return (
    <Toaster
      position="top-right"
      toastOptions={{
        // Default options
        duration: 4000,
        style: {
          background: 'var(--background)',
          color: 'var(--foreground)',
          border: '1px solid var(--border)',
        },
        // Success toast styling
        success: {
          duration: 3000,
          style: {
            background: 'rgb(34 197 94)', // green-500
            color: 'white',
            border: '1px solid rgb(34 197 94)',
          },
          iconTheme: {
            primary: 'white',
            secondary: 'rgb(34 197 94)',
          },
        },
        // Error toast styling
        error: {
          duration: 5000,
          style: {
            background: 'rgb(239 68 68)', // red-500
            color: 'white',
            border: '1px solid rgb(239 68 68)',
          },
          iconTheme: {
            primary: 'white',
            secondary: 'rgb(239 68 68)',
          },
        },
        // Loading toast styling
        loading: {
          style: {
            background: 'rgb(59 130 246)', // blue-500
            color: 'white',
            border: '1px solid rgb(59 130 246)',
          },
          iconTheme: {
            primary: 'white',
            secondary: 'rgb(59 130 246)',
          },
        },
      }}
    />
  )
}
