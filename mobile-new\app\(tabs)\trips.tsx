import React from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";

export default function TripsScreen() {
  return (
    <ScrollView style={styles.container}>
      {/* Trip Summary */}
      <View style={styles.tripSummaryContainer}>
        <Text style={styles.thisMonthText}>This Month</Text>

        <View style={styles.summaryMetricsContainer}>
          <View style={styles.metricItem}>
            <Text style={styles.metricValue}>847</Text>
            <Text style={styles.metricLabel}>Miles Driven</Text>
          </View>
          <View style={styles.metricItem}>
            <Text style={styles.metricValue}>23</Text>
            <Text style={styles.metricLabel}>Trips</Text>
          </View>
          <View style={styles.metricItem}>
            <Text style={styles.metricValue}>4.2</Text>
            <Text style={styles.metricLabel}>mi/kWh</Text>
          </View>
        </View>

        <TouchableOpacity style={styles.planTripButton}>
          <View style={styles.planTripButtonContent}>
            <Ionicons name="add" size={20} color="white" />
            <Text style={styles.planTripButtonText}>Plan New Trip</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Recent Trips */}
      <View style={styles.recentTripsContainer}>
        <Text style={styles.recentTripsTitle}>Recent Trips</Text>

        <View style={styles.tripsListContainer}>
          <TouchableOpacity style={styles.tripItem}>
            <View style={styles.tripItemHeader}>
              <Text style={styles.tripName}>Home → Office</Text>
              <Text style={styles.tripDate}>Today, 8:30 AM</Text>
            </View>
            <View style={styles.tripDetailsContainer}>
              <View style={styles.tripDetail}>
                <Ionicons name="location-outline" size={16} color="#6b7280" />
                <Text style={styles.tripDetailText}>24.5 miles</Text>
              </View>
              <View style={styles.tripDetail}>
                <Ionicons name="time-outline" size={16} color="#6b7280" />
                <Text style={styles.tripDetailText}>35 min</Text>
              </View>
              <View style={styles.tripDetail}>
                <Ionicons
                  name="battery-half-outline"
                  size={16}
                  color="#22c55e"
                />
                <Text style={[styles.tripDetailText, styles.electricText]}>
                  -12%
                </Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.tripItem}>
            <View style={styles.tripItemHeader}>
              <Text style={styles.tripName}>Office → Grocery Store</Text>
              <Text style={styles.tripDate}>Yesterday, 6:15 PM</Text>
            </View>
            <View style={styles.tripDetailsContainer}>
              <View style={styles.tripDetail}>
                <Ionicons name="location-outline" size={16} color="#6b7280" />
                <Text style={styles.tripDetailText}>8.2 miles</Text>
              </View>
              <View style={styles.tripDetail}>
                <Ionicons name="time-outline" size={16} color="#6b7280" />
                <Text style={styles.tripDetailText}>18 min</Text>
              </View>
              <View style={styles.tripDetail}>
                <Ionicons
                  name="battery-half-outline"
                  size={16}
                  color="#22c55e"
                />
                <Text style={[styles.tripDetailText, styles.electricText]}>
                  -4%
                </Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.tripItem}>
            <View style={styles.tripItemHeader}>
              <Text style={styles.tripName}>Home → Airport</Text>
              <Text style={styles.tripDate}>3 days ago</Text>
            </View>
            <View style={styles.tripDetailsContainer}>
              <View style={styles.tripDetail}>
                <Ionicons name="location-outline" size={16} color="#6b7280" />
                <Text style={styles.tripDetailText}>42.8 miles</Text>
              </View>
              <View style={styles.tripDetail}>
                <Ionicons name="time-outline" size={16} color="#6b7280" />
                <Text style={styles.tripDetailText}>1h 5m</Text>
              </View>
              <View style={styles.tripDetail}>
                <Ionicons
                  name="battery-half-outline"
                  size={16}
                  color="#22c55e"
                />
                <Text style={[styles.tripDetailText, styles.electricText]}>
                  -18%
                </Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.tripItem, { borderBottomWidth: 0 }]}>
            <View style={styles.tripItemHeader}>
              <Text style={styles.tripName}>Weekend Road Trip</Text>
              <Text style={styles.tripDate}>1 week ago</Text>
            </View>
            <View style={styles.tripDetailsContainer}>
              <View style={styles.tripDetail}>
                <Ionicons name="location-outline" size={16} color="#6b7280" />
                <Text style={styles.tripDetailText}>156.3 miles</Text>
              </View>
              <View style={styles.tripDetail}>
                <Ionicons name="time-outline" size={16} color="#6b7280" />
                <Text style={styles.tripDetailText}>2h 45m</Text>
              </View>
              <View style={styles.tripDetail}>
                <Ionicons
                  name="battery-half-outline"
                  size={16}
                  color="#22c55e"
                />
                <Text style={[styles.tripDetailText, styles.electricText]}>
                  -65%
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* Trip Analytics */}
      <View style={styles.analyticsContainer}>
        <Text style={styles.analyticsTitle}>Analytics</Text>

        <View style={styles.analyticsCard}>
          <View style={styles.analyticsHeader}>
            <Text style={styles.analyticsCardTitle}>Efficiency Trend</Text>
            <TouchableOpacity>
              <Text style={styles.viewDetailsText}>View Details</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.analyticsRow}>
            <Text style={styles.analyticsLabel}>Average Efficiency</Text>
            <Text style={styles.analyticsValue}>4.2 mi/kWh</Text>
          </View>

          <View style={styles.analyticsRow}>
            <Text style={styles.analyticsLabel}>Carbon Saved</Text>
            <Text style={[styles.analyticsValue, styles.electricText]}>
              127 lbs CO₂
            </Text>
          </View>

          <View style={styles.analyticsRow}>
            <Text style={styles.analyticsLabel}>Money Saved</Text>
            <Text style={[styles.analyticsValue, styles.electricText]}>
              $89.50
            </Text>
          </View>
        </View>
      </View>

      {/* Bottom Spacing */}
      <View style={{ height: 24 }} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f9fafb",
  },
  tripSummaryContainer: {
    backgroundColor: "white",
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    padding: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  thisMonthText: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#111827",
    marginBottom: 16,
  },
  summaryMetricsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  metricItem: {
    alignItems: "center",
  },
  metricValue: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#4f46e5",
  },
  metricLabel: {
    fontSize: 14,
    color: "#4b5563",
  },
  planTripButton: {
    backgroundColor: Colors.light.tint,
    borderRadius: 8,
    padding: 12,
    alignItems: "center",
  },
  planTripButtonContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  planTripButtonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 8,
  },
  recentTripsContainer: {
    marginHorizontal: 16,
    marginTop: 24,
  },
  recentTripsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
    marginBottom: 12,
  },
  tripsListContainer: {
    backgroundColor: "white",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  tripItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  tripItemHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  tripName: {
    fontWeight: "500",
    color: "#111827",
  },
  tripDate: {
    fontSize: 12,
    color: "#4b5563",
  },
  tripDetailsContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  tripDetail: {
    flexDirection: "row",
    alignItems: "center",
  },
  tripDetailText: {
    fontSize: 14,
    color: "#4b5563",
    marginLeft: 4,
  },
  electricText: {
    color: "#4f46e5",
  },
  analyticsContainer: {
    marginHorizontal: 16,
    marginTop: 24,
  },
  analyticsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
    marginBottom: 12,
  },
  analyticsCard: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  analyticsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  analyticsCardTitle: {
    fontWeight: "500",
    color: "#111827",
  },
  viewDetailsText: {
    fontSize: 14,
    color: "#4f46e5",
  },
  analyticsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  analyticsLabel: {
    fontSize: 14,
    color: "#4b5563",
  },
  analyticsValue: {
    fontSize: 14,
    fontWeight: "500",
    color: "#111827",
  },
});
