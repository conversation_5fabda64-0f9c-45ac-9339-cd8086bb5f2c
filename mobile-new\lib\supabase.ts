import "react-native-url-polyfill/auto";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { createClient } from "@supabase/supabase-js";
import type { Database } from "../types/database";

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing Supabase environment variables");
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false, // Disable for mobile
  },
});

// Export types for convenience
export type { Database } from "../types/database";

// Helper functions for EV models API
export const evModelsApi = {
  // Get all EV models with optional filtering
  async getAll(filters?: Record<string, any>) {
    let query = supabase
      .from("ev_models")
      .select(
        `
        *,
        ev_manufacturers!inner(
          name,
          logo_url,
          website_url
        )
      `
      )
      .eq("production_status", "current");

    // Apply filters if provided
    if (filters) {
      if (filters.search) {
        query = query.or(
          `make.ilike.%${filters.search}%,model.ilike.%${filters.search}%`
        );
      }
      if (filters.make) {
        query = query.eq("make", filters.make);
      }
      if (filters.bodyType) {
        query = query.eq("body_type", filters.bodyType);
      }
      if (filters.priceMax) {
        query = query.lte("price_msrp", parseInt(filters.priceMax));
      }
      if (filters.rangeMin) {
        query = query.gte("range_epa_miles", parseInt(filters.rangeMin));
      }
    }

    return query.order("popularity_score", { ascending: false });
  },

  // Get a single EV model by ID
  async getById(id: string) {
    return supabase
      .from("ev_models")
      .select(
        `
        *,
        ev_manufacturers!inner(
          name,
          logo_url,
          website_url,
          headquarters_country,
          founded_year
        )
      `
      )
      .eq("id", id)
      .single();
  },

  // Get featured models
  async getFeatured(limit = 6) {
    return supabase
      .from("ev_models")
      .select(
        `
        *,
        ev_manufacturers!inner(
          name,
          logo_url
        )
      `
      )
      .eq("production_status", "current")
      .eq("is_featured", true)
      .order("popularity_score", { ascending: false })
      .limit(limit);
  },

  // Search with intelligent suggestions
  async search(query: string, limit = 10) {
    return supabase
      .from("ev_models")
      .select(
        `
        *,
        ev_manufacturers!inner(
          name,
          logo_url
        )
      `
      )
      .eq("production_status", "current")
      .or(`make.ilike.%${query}%,model.ilike.%${query}%,trim.ilike.%${query}%`)
      .order("popularity_score", { ascending: false })
      .limit(limit);
  },
};
