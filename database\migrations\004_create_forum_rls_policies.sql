-- Migration: Create RLS Policies for Forum Tables
-- This ensures proper security and data access control for the forum system

-- Enable RLS on all forum tables
ALTER TABLE forum_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_topics ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_bookmarks ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_moderators ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_user_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_flags ENABLE ROW LEVEL SECURITY;

-- Forum Categories Policies
-- Everyone can view active categories
CREATE POLICY "Forum categories are viewable by everyone" ON forum_categories
    FOR SELECT USING (is_active = true);

-- Only moderators/admins can insert/update/delete categories
CREATE POLICY "Only moderators can manage categories" ON forum_categories
    FOR ALL USING (
        auth.uid() IN (
            SELECT user_id FROM forum_moderators 
            WHERE is_active = true 
            AND (category_id IS NULL OR category_id = forum_categories.id)
            AND role IN ('admin', 'super_admin')
        )
    );

-- Forum Topics Policies
-- Everyone can view approved topics in active categories
CREATE POLICY "Forum topics are viewable by everyone" ON forum_topics
    FOR SELECT USING (
        is_approved = true 
        AND category_id IN (
            SELECT id FROM forum_categories WHERE is_active = true
        )
    );

-- Authenticated users can create topics
CREATE POLICY "Authenticated users can create topics" ON forum_topics
    FOR INSERT WITH CHECK (
        auth.role() = 'authenticated' 
        AND auth.uid() = author_id
        AND category_id IN (
            SELECT id FROM forum_categories WHERE is_active = true
        )
    );

-- Users can update their own topics, moderators can update any
CREATE POLICY "Users can update own topics, moderators can update any" ON forum_topics
    FOR UPDATE USING (
        auth.uid() = author_id 
        OR auth.uid() IN (
            SELECT user_id FROM forum_moderators 
            WHERE is_active = true 
            AND (category_id IS NULL OR category_id = forum_topics.category_id)
        )
    );

-- Users can delete their own topics (if no replies), moderators can delete any
CREATE POLICY "Users can delete own topics, moderators can delete any" ON forum_topics
    FOR DELETE USING (
        (auth.uid() = author_id AND post_count <= 1)
        OR auth.uid() IN (
            SELECT user_id FROM forum_moderators 
            WHERE is_active = true 
            AND (category_id IS NULL OR category_id = forum_topics.category_id)
        )
    );

-- Forum Posts Policies
-- Everyone can view approved posts in approved topics
CREATE POLICY "Forum posts are viewable by everyone" ON forum_posts
    FOR SELECT USING (
        is_approved = true 
        AND topic_id IN (
            SELECT id FROM forum_topics WHERE is_approved = true
        )
    );

-- Authenticated users can create posts
CREATE POLICY "Authenticated users can create posts" ON forum_posts
    FOR INSERT WITH CHECK (
        auth.role() = 'authenticated' 
        AND auth.uid() = author_id
        AND topic_id IN (
            SELECT id FROM forum_topics 
            WHERE is_approved = true AND is_locked = false
        )
    );

-- Users can update their own posts, moderators can update any
CREATE POLICY "Users can update own posts, moderators can update any" ON forum_posts
    FOR UPDATE USING (
        auth.uid() = author_id 
        OR auth.uid() IN (
            SELECT user_id FROM forum_moderators 
            WHERE is_active = true 
            AND (category_id IS NULL OR category_id IN (
                SELECT category_id FROM forum_topics WHERE id = forum_posts.topic_id
            ))
        )
    );

-- Users can delete their own posts (if no replies), moderators can delete any
CREATE POLICY "Users can delete own posts, moderators can delete any" ON forum_posts
    FOR DELETE USING (
        (auth.uid() = author_id AND reply_count = 0)
        OR auth.uid() IN (
            SELECT user_id FROM forum_moderators 
            WHERE is_active = true 
            AND (category_id IS NULL OR category_id IN (
                SELECT category_id FROM forum_topics WHERE id = forum_posts.topic_id
            ))
        )
    );

-- Forum Reactions Policies
-- Everyone can view reactions
CREATE POLICY "Forum reactions are viewable by everyone" ON forum_reactions
    FOR SELECT USING (true);

-- Authenticated users can manage their own reactions
CREATE POLICY "Users can manage own reactions" ON forum_reactions
    FOR ALL USING (auth.uid() = user_id);

-- Forum Bookmarks Policies
-- Users can only see and manage their own bookmarks
CREATE POLICY "Users can manage own bookmarks" ON forum_bookmarks
    FOR ALL USING (auth.uid() = user_id);

-- Forum Moderators Policies
-- Everyone can view active moderators
CREATE POLICY "Forum moderators are viewable by everyone" ON forum_moderators
    FOR SELECT USING (is_active = true);

-- Only super_admins can manage moderators
CREATE POLICY "Only super admins can manage moderators" ON forum_moderators
    FOR ALL USING (
        auth.uid() IN (
            SELECT user_id FROM forum_moderators 
            WHERE is_active = true 
            AND role = 'super_admin'
        )
    );

-- Forum User Stats Policies
-- Everyone can view user stats
CREATE POLICY "Forum user stats are viewable by everyone" ON forum_user_stats
    FOR SELECT USING (true);

-- Users can only update their own stats (typically done via triggers)
CREATE POLICY "Users can update own stats" ON forum_user_stats
    FOR ALL USING (auth.uid() = user_id);

-- Forum Flags Policies
-- Moderators can view all flags
CREATE POLICY "Moderators can view flags" ON forum_flags
    FOR SELECT USING (
        auth.uid() IN (
            SELECT user_id FROM forum_moderators 
            WHERE is_active = true
        )
        OR auth.uid() = flagger_id
    );

-- Authenticated users can create flags
CREATE POLICY "Authenticated users can create flags" ON forum_flags
    FOR INSERT WITH CHECK (
        auth.role() = 'authenticated' 
        AND auth.uid() = flagger_id
    );

-- Only moderators can update flags (resolve them)
CREATE POLICY "Only moderators can update flags" ON forum_flags
    FOR UPDATE USING (
        auth.uid() IN (
            SELECT user_id FROM forum_moderators 
            WHERE is_active = true
        )
    );

-- Create helper functions for moderator checks
CREATE OR REPLACE FUNCTION is_forum_moderator(check_category_id UUID DEFAULT NULL)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM forum_moderators 
        WHERE user_id = auth.uid() 
        AND is_active = true 
        AND (category_id IS NULL OR category_id = check_category_id OR check_category_id IS NULL)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION is_forum_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM forum_moderators 
        WHERE user_id = auth.uid() 
        AND is_active = true 
        AND role IN ('admin', 'super_admin')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 