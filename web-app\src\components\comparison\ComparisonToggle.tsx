'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { GitCompare } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useComparison } from '@/contexts/ComparisonContext'

interface ComparisonToggleProps {
  className?: string
}

export function ComparisonToggle({ className }: ComparisonToggleProps) {
  const { state, togglePanel } = useComparison()

  if (state.models.length === 0) {
    return null
  }

  return (
    <Button
      variant={state.isOpen ? 'default' : 'outline'}
      onClick={togglePanel}
      className={cn(
        'relative transition-colors',
        state.isOpen && 'bg-electric-600 text-white hover:bg-electric-700',
        className
      )}
    >
      <GitCompare className="mr-2 h-4 w-4" />
      Compare
      <Badge
        variant={state.isOpen ? 'secondary' : 'default'}
        className={cn(
          'ml-2 h-5 w-5 rounded-full p-0 text-xs',
          state.isOpen && 'bg-white text-electric-600'
        )}
      >
        {state.models.length}
      </Badge>
    </Button>
  )
}
