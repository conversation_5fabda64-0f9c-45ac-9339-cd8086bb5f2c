-- Migration: Add Default Forum Categories
-- This creates initial categories for the GreenMilesEV forum

-- Insert main categories (skip if they already exist)
INSERT INTO forum_categories (name, description, slug, icon, color, sort_order, is_active, requires_approval) VALUES
-- General Discussion
('General Discussion', 'General conversations about electric vehicles and the community', 'general-discussion', '💬', '#3B82F6', 1, true, false),

-- EV Reviews & Experiences  
('EV Reviews & Experiences', 'Share your real-world experiences with electric vehicles', 'ev-reviews-experiences', '⭐', '#10B981', 2, true, false),

-- Charging & Infrastructure
('Charging & Infrastructure', 'Discuss charging stations, home charging, and EV infrastructure', 'charging-infrastructure', '⚡', '#F59E0B', 3, true, false),

-- Technical & Maintenance
('Technical & Maintenance', 'Technical discussions, maintenance tips, and troubleshooting', 'technical-maintenance', '🔧', '#8B5CF6', 4, true, false),

-- EV News & Updates
('EV News & Updates', 'Latest news, updates, and announcements in the EV world', 'ev-news-updates', '📰', '#EF4444', 5, true, true),

-- Buying & Selling
('Buying & Selling', 'Marketplace for EV-related items and advice on purchasing decisions', 'buying-selling', '🛒', '#06B6D4', 6, true, true),

-- Off-Topic
('Off-Topic', 'Non-EV related discussions and community chat', 'off-topic', '🎭', '#6B7280', 7, true, false)
ON CONFLICT (name) DO NOTHING;

-- Insert subcategories for General Discussion
INSERT INTO forum_categories (name, description, slug, icon, color, sort_order, is_active, requires_approval, parent_category_id) VALUES
('New Member Introductions', 'Welcome new members to the community', 'new-member-introductions', '👋', '#3B82F6', 1, true, false, 
    (SELECT id FROM forum_categories WHERE slug = 'general-discussion')),
('Community Events', 'EV meetups, events, and gatherings', 'community-events', '📅', '#3B82F6', 2, true, false,
    (SELECT id FROM forum_categories WHERE slug = 'general-discussion'))
ON CONFLICT (name) DO NOTHING;

-- Insert subcategories for EV Reviews & Experiences
INSERT INTO forum_categories (name, description, slug, icon, color, sort_order, is_active, requires_approval, parent_category_id) VALUES
('Tesla Experiences', 'Tesla Model S, 3, X, Y discussions and reviews', 'tesla-experiences', '🚗', '#10B981', 1, true, false,
    (SELECT id FROM forum_categories WHERE slug = 'ev-reviews-experiences')),
('Non-Tesla EVs', 'Reviews and experiences with other EV brands', 'non-tesla-evs', '🚙', '#10B981', 2, true, false,
    (SELECT id FROM forum_categories WHERE slug = 'ev-reviews-experiences')),
('Long Road Trips', 'Long-distance travel experiences with EVs', 'long-road-trips', '🛣️', '#10B981', 3, true, false,
    (SELECT id FROM forum_categories WHERE slug = 'ev-reviews-experiences'))
ON CONFLICT (name) DO NOTHING;

-- Insert subcategories for Charging & Infrastructure
INSERT INTO forum_categories (name, description, slug, icon, color, sort_order, is_active, requires_approval, parent_category_id) VALUES
('Home Charging', 'Home charging setups, Level 1/2 chargers, and installation', 'home-charging', '🏠', '#F59E0B', 1, true, false,
    (SELECT id FROM forum_categories WHERE slug = 'charging-infrastructure')),
('Public Charging', 'Public charging networks, apps, and experiences', 'public-charging', '🏪', '#F59E0B', 2, true, false,
    (SELECT id FROM forum_categories WHERE slug = 'charging-infrastructure')),
('DC Fast Charging', 'Superchargers, CCS, CHAdeMO, and high-speed charging', 'dc-fast-charging', '⚡', '#F59E0B', 3, true, false,
    (SELECT id FROM forum_categories WHERE slug = 'charging-infrastructure'))
ON CONFLICT (name) DO NOTHING;

-- Insert subcategories for Technical & Maintenance
INSERT INTO forum_categories (name, description, slug, icon, color, sort_order, is_active, requires_approval, parent_category_id) VALUES
('Battery & Range', 'Battery health, range optimization, and degradation discussions', 'battery-range', '🔋', '#8B5CF6', 1, true, false,
    (SELECT id FROM forum_categories WHERE slug = 'technical-maintenance')),
('Software & Updates', 'OTA updates, infotainment systems, and software features', 'software-updates', '💾', '#8B5CF6', 2, true, false,
    (SELECT id FROM forum_categories WHERE slug = 'technical-maintenance')),
('DIY & Modifications', 'DIY projects, modifications, and customizations', 'diy-modifications', '🛠️', '#8B5CF6', 3, true, false,
    (SELECT id FROM forum_categories WHERE slug = 'technical-maintenance'));

-- Update category counts (this will be handled by triggers in production)
UPDATE forum_categories SET 
    topic_count = 0,
    post_count = 0,
    last_activity_at = now()
WHERE id IN (SELECT id FROM forum_categories);

-- Create a sample welcome topic (optional - will be skipped if topic with same title exists)
-- This would typically be done through the application, but we can create one for demo
DO $$
DECLARE
    general_category_id UUID;
    first_user_id UUID;
BEGIN
    -- Get the general discussion category ID
    SELECT id INTO general_category_id FROM forum_categories WHERE slug = 'general-discussion' LIMIT 1;
    
    -- Get the first user ID (or NULL if no users exist)
    SELECT id INTO first_user_id FROM auth.users ORDER BY created_at LIMIT 1;
    
    -- Only insert if category exists and topic doesn't already exist
    IF general_category_id IS NOT NULL THEN
        INSERT INTO forum_topics (
            category_id,
            author_id,
            title,
            slug,
            content,
            content_type,
            is_pinned,
            is_approved,
            view_count,
            post_count,
            like_count,
            tags
        ) VALUES (
            general_category_id,
            first_user_id, -- Can be NULL if no users exist yet
            'Welcome to the GreenMilesEV Community Forum!',
            'welcome-to-greenmiles-ev-forum',
            '# Welcome to the GreenMilesEV Community! 🚗⚡

We''re excited to have you join our community of electric vehicle enthusiasts! This forum is your go-to place for:

- **Sharing experiences** with your EVs
- **Getting advice** on purchasing decisions  
- **Discussing charging** and infrastructure
- **Learning about** the latest EV technology
- **Connecting** with fellow EV owners

## Getting Started

1. **Introduce yourself** in the New Member Introductions section
2. **Browse categories** to find topics that interest you
3. **Search existing topics** before creating new ones
4. **Be respectful** and follow our community guidelines

## Community Guidelines

- Be kind and respectful to all members
- Stay on topic in each category
- Use descriptive titles for your topics
- Search before posting to avoid duplicates
- Share your real experiences to help others

Happy discussing! 🌱',
            'markdown',
            true,
            true,
            0,
            0,
            0,
            ARRAY['welcome', 'community', 'guidelines']
        ) ON CONFLICT (title) DO NOTHING;
    END IF;
END $$; 