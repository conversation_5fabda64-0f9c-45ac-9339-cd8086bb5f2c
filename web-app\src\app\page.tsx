import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Car, MapPin, BarChart3, Leaf, Users, Zap } from 'lucide-react'

export default function HomePage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      {/* Hero Section */}
      <section className="flex flex-1 items-center justify-center bg-gradient-to-br from-electric-50 to-green-50 py-20 dark:from-gray-900 dark:to-gray-800">
        <div className="container space-y-8 text-center">
          <Badge variant="secondary" className="mb-4">
            <Leaf className="mr-1 h-3 w-3" />
            Sustainable Transportation
          </Badge>
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white md:text-6xl">
            Drive the Future with <span className="text-electric-600">Electric Vehicles</span>
          </h1>
          <p className="mx-auto max-w-2xl text-xl text-gray-600 dark:text-gray-300">
            Comprehensive platform for electric vehicle management, charging station discovery, and
            sustainable transportation planning.
          </p>
          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <Button size="lg" className="bg-electric-600 hover:bg-electric-700">
              <Car className="mr-2 h-4 w-4" />
              Start Your Journey
            </Button>
            <Button size="lg" variant="outline">
              <MapPin className="mr-2 h-4 w-4" />
              Find Charging Stations
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="bg-white py-20 dark:bg-gray-900">
        <div className="container">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white md:text-4xl">
              Everything You Need for EV Management
            </h2>
            <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
              From vehicle tracking to charging optimization, we provide all the tools for a
              seamless electric vehicle experience.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <Card className="border-0 shadow-lg transition-shadow hover:shadow-xl">
              <CardHeader>
                <Car className="mb-2 h-10 w-10 text-electric-600" />
                <CardTitle>Vehicle Management</CardTitle>
                <CardDescription>
                  Track your EV's performance, maintenance, and efficiency metrics in real-time.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg transition-shadow hover:shadow-xl">
              <CardHeader>
                <MapPin className="mb-2 h-10 w-10 text-electric-600" />
                <CardTitle>Charging Network</CardTitle>
                <CardDescription>
                  Find nearby charging stations with real-time availability and pricing information.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg transition-shadow hover:shadow-xl">
              <CardHeader>
                <BarChart3 className="mb-2 h-10 w-10 text-electric-600" />
                <CardTitle>Analytics Dashboard</CardTitle>
                <CardDescription>
                  Monitor energy consumption, cost savings, and environmental impact.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg transition-shadow hover:shadow-xl">
              <CardHeader>
                <Zap className="mb-2 h-10 w-10 text-electric-600" />
                <CardTitle>Smart Charging</CardTitle>
                <CardDescription>
                  Optimize charging schedules based on energy prices and grid demand.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg transition-shadow hover:shadow-xl">
              <CardHeader>
                <Leaf className="mb-2 h-10 w-10 text-electric-600" />
                <CardTitle>Carbon Tracking</CardTitle>
                <CardDescription>
                  Track your carbon footprint reduction and environmental contributions.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg transition-shadow hover:shadow-xl">
              <CardHeader>
                <Users className="mb-2 h-10 w-10 text-electric-600" />
                <CardTitle>Community</CardTitle>
                <CardDescription>
                  Connect with other EV owners and share experiences and tips.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
