import { renderHook, act } from '@testing-library/react'
import { ReactNode } from 'react'
import { ComparisonProvider, useComparison } from '@/contexts/ComparisonContext'
import type { EVModel } from '@/shared/types'

// Mock EV models for testing
const mockEVModel1: EVModel = {
  id: '1',
  make: 'Tesla',
  model: 'Model 3',
  year: 2024,
  trim: 'Long Range',
  body_type: 'sedan',
  price_msrp: 4799900, // $47,999
  range_epa_miles: 358,
  efficiency_mpge: 132,
  acceleration_0_60_mph: 4.2,
  charging_speed_dc_kw: 250,
  battery_capacity_kwh: 75,
  seating_capacity: 5,
  production_status: 'current',
  images: ['tesla-model3.jpg'],
  is_featured: true,
  popularity_score: 95,
  editor_choice: true,
  best_value: false,
  manufacturer: {
    name: 'Tesla',
    logo_url: 'tesla-logo.png'
  }
}

const mockEVModel2: EVModel = {
  id: '2',
  make: 'BMW',
  model: 'iX',
  year: 2024,
  trim: 'xDrive50',
  body_type: 'suv',
  price_msrp: 8399900, // $83,999
  range_epa_miles: 324,
  efficiency_mpge: 86,
  acceleration_0_60_mph: 4.6,
  charging_speed_dc_kw: 200,
  battery_capacity_kwh: 111.5,
  seating_capacity: 5,
  production_status: 'current',
  images: ['bmw-ix.jpg'],
  is_featured: true,
  popularity_score: 88,
  editor_choice: false,
  best_value: false,
  manufacturer: {
    name: 'BMW',
    logo_url: 'bmw-logo.png'
  }
}

const mockEVModel3: EVModel = {
  id: '3',
  make: 'Hyundai',
  model: 'IONIQ 5',
  year: 2024,
  trim: 'SEL',
  body_type: 'suv',
  price_msrp: 4199900, // $41,999
  range_epa_miles: 303,
  efficiency_mpge: 114,
  acceleration_0_60_mph: 5.1,
  charging_speed_dc_kw: 235,
  battery_capacity_kwh: 77.4,
  seating_capacity: 5,
  production_status: 'current',
  images: ['hyundai-ioniq5.jpg'],
  is_featured: true,
  popularity_score: 92,
  editor_choice: false,
  best_value: true,
  manufacturer: {
    name: 'Hyundai',
    logo_url: 'hyundai-logo.png'
  }
}

// Wrapper component for testing
const wrapper = ({ children }: { children: ReactNode }) => (
  <ComparisonProvider>{children}</ComparisonProvider>
)

describe('ComparisonContext', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear()
  })

  describe('Basic Comparison Operations', () => {
    it('should initialize with empty comparison list', () => {
      const { result } = renderHook(() => useComparison(), { wrapper })
      
      expect(result.current.comparisonList).toEqual([])
      expect(result.current.comparisonCount).toBe(0)
    })

    it('should add model to comparison', () => {
      const { result } = renderHook(() => useComparison(), { wrapper })
      
      act(() => {
        result.current.addToComparison(mockEVModel1)
      })
      
      expect(result.current.comparisonList).toHaveLength(1)
      expect(result.current.comparisonList[0]).toEqual(mockEVModel1)
      expect(result.current.comparisonCount).toBe(1)
    })

    it('should remove model from comparison', () => {
      const { result } = renderHook(() => useComparison(), { wrapper })
      
      act(() => {
        result.current.addToComparison(mockEVModel1)
        result.current.addToComparison(mockEVModel2)
      })
      
      expect(result.current.comparisonList).toHaveLength(2)
      
      act(() => {
        result.current.removeFromComparison('1')
      })
      
      expect(result.current.comparisonList).toHaveLength(1)
      expect(result.current.comparisonList[0]).toEqual(mockEVModel2)
    })

    it('should clear all comparisons', () => {
      const { result } = renderHook(() => useComparison(), { wrapper })
      
      act(() => {
        result.current.addToComparison(mockEVModel1)
        result.current.addToComparison(mockEVModel2)
      })
      
      expect(result.current.comparisonList).toHaveLength(2)
      
      act(() => {
        result.current.clearComparison()
      })
      
      expect(result.current.comparisonList).toHaveLength(0)
      expect(result.current.comparisonCount).toBe(0)
    })

    it('should check if model is in comparison', () => {
      const { result } = renderHook(() => useComparison(), { wrapper })
      
      act(() => {
        result.current.addToComparison(mockEVModel1)
      })
      
      expect(result.current.isInComparison('1')).toBe(true)
      expect(result.current.isInComparison('2')).toBe(false)
    })
  })

  describe('Comparison Limits', () => {
    it('should enforce maximum comparison limit', () => {
      const { result } = renderHook(() => useComparison(), { wrapper })
      
      // Add maximum allowed models (assuming limit is 4)
      act(() => {
        result.current.addToComparison(mockEVModel1)
        result.current.addToComparison(mockEVModel2)
        result.current.addToComparison(mockEVModel3)
        result.current.addToComparison({
          ...mockEVModel1,
          id: '4',
          make: 'Ford',
          model: 'Mustang Mach-E'
        })
      })
      
      expect(result.current.comparisonList).toHaveLength(4)
      expect(result.current.canAddMore).toBe(false)
      
      // Try to add one more - should not be added
      act(() => {
        result.current.addToComparison({
          ...mockEVModel1,
          id: '5',
          make: 'Rivian',
          model: 'R1T'
        })
      })
      
      expect(result.current.comparisonList).toHaveLength(4)
    })

    it('should not add duplicate models', () => {
      const { result } = renderHook(() => useComparison(), { wrapper })
      
      act(() => {
        result.current.addToComparison(mockEVModel1)
        result.current.addToComparison(mockEVModel1) // Try to add same model again
      })
      
      expect(result.current.comparisonList).toHaveLength(1)
    })
  })

  describe('LocalStorage Persistence', () => {
    it('should persist comparison list to localStorage', () => {
      const { result } = renderHook(() => useComparison(), { wrapper })
      
      act(() => {
        result.current.addToComparison(mockEVModel1)
      })
      
      const stored = localStorage.getItem('ev-comparison-list')
      expect(stored).toBeTruthy()
      
      const parsed = JSON.parse(stored!)
      expect(parsed).toHaveLength(1)
      expect(parsed[0].id).toBe('1')
    })

    it('should load comparison list from localStorage on initialization', () => {
      // Pre-populate localStorage
      localStorage.setItem('ev-comparison-list', JSON.stringify([mockEVModel1, mockEVModel2]))
      
      const { result } = renderHook(() => useComparison(), { wrapper })
      
      expect(result.current.comparisonList).toHaveLength(2)
      expect(result.current.comparisonList[0].id).toBe('1')
      expect(result.current.comparisonList[1].id).toBe('2')
    })
  })

  describe('Decision Matrix Integration', () => {
    it('should provide comparison data for decision matrix', () => {
      const { result } = renderHook(() => useComparison(), { wrapper })
      
      act(() => {
        result.current.addToComparison(mockEVModel1)
        result.current.addToComparison(mockEVModel2)
        result.current.addToComparison(mockEVModel3)
      })
      
      expect(result.current.comparisonList).toHaveLength(3)
      
      // Verify models have different characteristics for meaningful comparison
      const models = result.current.comparisonList
      expect(models.some(m => m.body_type === 'sedan')).toBe(true)
      expect(models.some(m => m.body_type === 'suv')).toBe(true)
      expect(models.some(m => m.best_value === true)).toBe(true)
    })
  })
})
