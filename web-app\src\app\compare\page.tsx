'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { <PERSON><PERSON> } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { ComparisonTable } from '@/components/comparison/ComparisonTable'
import { ComparisonHeader } from '@/components/comparison/ComparisonHeader'
import { DecisionMatrix } from '@/components/comparison/DecisionMatrix'
import { RecommendationEngine } from '@/components/comparison/RecommendationEngine'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import type { EVModel } from '@/shared/types'

function ComparePageContent() {
  const searchParams = useSearchParams()
  const [models, setModels] = useState<EVModel[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'comparison' | 'decision' | 'recommendations'>(
    'comparison'
  )

  useEffect(() => {
    const fetchModels = async () => {
      const modelIds = searchParams.get('models')

      if (!modelIds) {
        setError('No models specified for comparison')
        setLoading(false)
        return
      }

      const ids = modelIds.split(',').filter(Boolean)

      if (ids.length < 2) {
        setError('At least 2 models are required for comparison')
        setLoading(false)
        return
      }

      if (ids.length > 4) {
        setError('Maximum 4 models can be compared at once')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)

        // Fetch all models in parallel
        const modelPromises = ids.map((id) =>
          fetch(`/api/ev-models/${id}`).then((res) => {
            if (!res.ok) throw new Error(`Failed to fetch model ${id}`)
            return res.json()
          })
        )

        const responses = await Promise.all(modelPromises)
        const fetchedModels = responses.map((response) => response.data).filter(Boolean)

        if (fetchedModels.length === 0) {
          throw new Error('No valid models found')
        }

        setModels(fetchedModels)
      } catch (err) {
        console.error('Error fetching models for comparison:', err)
        setError(err instanceof Error ? err.message : 'Failed to load models for comparison')
      } finally {
        setLoading(false)
      }
    }

    fetchModels()
  }, [searchParams])

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header variant="dashboard" />
        <main className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <LoadingSpinner size="xl" className="mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Loading comparison...</p>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (error || models.length === 0) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header variant="dashboard" />
        <main className="flex flex-1 items-center justify-center">
          <div className="max-w-md text-center">
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error || 'No models available for comparison'}</AlertDescription>
            </Alert>
            <div className="space-y-4">
              <Button asChild>
                <Link href="/ev-models">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Browse EV Models
                </Link>
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header variant="dashboard" />

      <main className="flex-1">
        {/* Breadcrumb */}
        <div className="border-b bg-gray-50 py-4 dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <nav className="flex items-center space-x-2 text-sm">
              <Link
                href="/ev-models"
                className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
              >
                EV Models
              </Link>
              <span className="text-gray-400">/</span>
              <span className="text-gray-900 dark:text-gray-100">
                Compare {models.length} Models
              </span>
            </nav>
          </div>
        </div>

        {/* Comparison Header */}
        <ComparisonHeader models={models} />

        {/* Tabs Navigation */}
        <div className="border-b bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <nav className="flex space-x-8 py-4">
              <button
                onClick={() => setActiveTab('comparison')}
                className={`pb-2 text-sm font-medium transition-colors ${
                  activeTab === 'comparison'
                    ? 'border-b-2 border-electric-600 text-electric-600'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100'
                }`}
              >
                Side-by-Side Comparison
              </button>
              <button
                onClick={() => setActiveTab('decision')}
                className={`pb-2 text-sm font-medium transition-colors ${
                  activeTab === 'decision'
                    ? 'border-b-2 border-electric-600 text-electric-600'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100'
                }`}
              >
                Decision Matrix
              </button>
              <button
                onClick={() => setActiveTab('recommendations')}
                className={`pb-2 text-sm font-medium transition-colors ${
                  activeTab === 'recommendations'
                    ? 'border-b-2 border-electric-600 text-electric-600'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100'
                }`}
              >
                Smart Recommendations
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-gray-50 py-8 dark:bg-gray-900">
          <div className="container mx-auto px-4">
            {activeTab === 'comparison' && <ComparisonTable models={models} />}
            {activeTab === 'decision' && <DecisionMatrix models={models} />}
            {activeTab === 'recommendations' && <RecommendationEngine models={models} />}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}

export default function ComparePage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen flex-col">
          <Header variant="dashboard" />
          <main className="flex flex-1 items-center justify-center">
            <LoadingSpinner size="xl" />
          </main>
          <Footer />
        </div>
      }
    >
      <ComparePageContent />
    </Suspense>
  )
}
