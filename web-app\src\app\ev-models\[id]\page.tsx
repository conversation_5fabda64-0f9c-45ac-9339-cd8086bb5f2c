'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { EVModelHero } from '@/components/ev-models/details/EVModelHero'
import { EVModelTabs } from '@/components/ev-models/details/EVModelTabs'
import { EVModelOverview } from '@/components/ev-models/details/EVModelOverview'
import { EVModelSpecs } from '@/components/ev-models/details/EVModelSpecs'
import { EVModelRealWorld } from '@/components/ev-models/details/EVModelRealWorld'
import { EVModelCostAnalysis } from '@/components/ev-models/details/EVModelCostAnalysis'
import { EVModelComparison } from '@/components/ev-models/details/EVModelComparison'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { useEVModelDetails } from '@/hooks/useEVModelDetails'
import type { EVModelWithDetails } from '@/shared/types'

type TabType = 'overview' | 'specs' | 'real-world' | 'cost-analysis' | 'comparison'

export default function EVModelDetailsPage() {
  const params = useParams()
  const id = params.id as string
  const [activeTab, setActiveTab] = useState<TabType>('overview')

  const {
    evModel,
    loading,
    error,
    refetch
  } = useEVModelDetails(id)

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header variant="dashboard" />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <LoadingSpinner size="xl" className="mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Loading EV details...</p>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (error || !evModel) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header variant="dashboard" />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>
                {error || 'EV model not found'}
              </AlertDescription>
            </Alert>
            <div className="space-y-4">
              <Button onClick={refetch} variant="outline">
                Try Again
              </Button>
              <Button asChild>
                <Link href="/ev-models">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to EV Models
                </Link>
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  const tabs = [
    { id: 'overview', label: 'Overview', description: 'Key highlights and buyer insights' },
    { id: 'specs', label: 'Specifications', description: 'Detailed technical specifications' },
    { id: 'real-world', label: 'Real-World Data', description: 'Actual performance and efficiency' },
    { id: 'cost-analysis', label: 'Cost Analysis', description: 'Total cost of ownership calculator' },
    { id: 'comparison', label: 'Compare', description: 'Similar models and alternatives' }
  ] as const

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <EVModelOverview evModel={evModel} />
      case 'specs':
        return <EVModelSpecs evModel={evModel} />
      case 'real-world':
        return <EVModelRealWorld evModel={evModel} />
      case 'cost-analysis':
        return <EVModelCostAnalysis evModel={evModel} />
      case 'comparison':
        return <EVModelComparison evModel={evModel} />
      default:
        return <EVModelOverview evModel={evModel} />
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header variant="dashboard" />
      
      <main className="flex-1">
        {/* Breadcrumb */}
        <div className="border-b bg-gray-50 py-4 dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <nav className="flex items-center space-x-2 text-sm">
              <Link 
                href="/ev-models" 
                className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
              >
                EV Models
              </Link>
              <span className="text-gray-400">/</span>
              <span className="text-gray-900 dark:text-gray-100">
                {evModel.make} {evModel.model}
              </span>
            </nav>
          </div>
        </div>

        {/* Hero Section */}
        <EVModelHero evModel={evModel} />

        {/* Tabs Navigation */}
        <div className="border-b bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <EVModelTabs
              tabs={tabs}
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-gray-50 py-8 dark:bg-gray-900">
          <div className="container mx-auto px-4">
            {renderTabContent()}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
