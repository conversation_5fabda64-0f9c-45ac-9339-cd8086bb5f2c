import React from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { EVModel } from "../../../types";
import { EVModelCard } from "../../EVModelCard";

interface EVDetailsOverviewProps {
  model: EVModel;
  similarModels: EVModel[];
}

export function EVDetailsOverview({
  model,
  similarModels,
}: EVDetailsOverviewProps) {
  const formatRange = (range: number | null) => {
    if (!range) return "N/A";
    return `${range} mi`;
  };

  const getEfficiencyRating = () => {
    if (!model.efficiency_mpge) return null;
    if (model.efficiency_mpge >= 130)
      return {
        rating: "Excellent",
        color: styles.excellentText,
        bgColor: styles.excellentBg,
      };
    if (model.efficiency_mpge >= 110)
      return {
        rating: "Very Good",
        color: styles.veryGoodText,
        bgColor: styles.veryGoodBg,
      };
    if (model.efficiency_mpge >= 90)
      return { rating: "Good", color: styles.goodText, bgColor: styles.goodBg };
    return {
      rating: "Average",
      color: styles.averageText,
      bgColor: styles.averageBg,
    };
  };

  const getValueProposition = () => {
    const propositions = [];
    if (model.best_value) propositions.push("Best Value");
    if (model.editor_choice) propositions.push("Editor's Choice");
    if (model.is_featured) propositions.push("Featured");

    const pricePerMile =
      model.price_msrp && model.range_epa_miles
        ? model.price_msrp / 100 / model.range_epa_miles
        : null;

    if (pricePerMile && pricePerMile < 200)
      propositions.push("Great Range Value");
    if (model.efficiency_mpge && model.efficiency_mpge > 120)
      propositions.push("Highly Efficient");

    return propositions;
  };

  const getProsCons = () => {
    const pros = [];
    const cons = [];

    if (model.range_epa_miles && model.range_epa_miles > 300)
      pros.push("Long driving range");
    if (model.efficiency_mpge && model.efficiency_mpge > 120)
      pros.push("Excellent efficiency");
    if (model.acceleration_0_60_mph && model.acceleration_0_60_mph < 4.0)
      pros.push("Quick acceleration");
    if (model.charging_speed_dc_kw && model.charging_speed_dc_kw > 150)
      pros.push("Fast charging capability");
    if (model.seating_capacity && model.seating_capacity >= 7)
      pros.push("Spacious seating");
    if (model.best_value) pros.push("Great value for money");

    if (model.price_msrp && model.price_msrp > 8000000)
      cons.push("Premium pricing");
    if (model.range_epa_miles && model.range_epa_miles < 200)
      cons.push("Limited range");
    if (model.acceleration_0_60_mph && model.acceleration_0_60_mph > 8.0)
      cons.push("Slower acceleration");
    if (model.charging_speed_dc_kw && model.charging_speed_dc_kw < 100)
      cons.push("Slower charging");

    return { pros, cons };
  };

  const { pros, cons } = getProsCons();
  const efficiencyRating = getEfficiencyRating();
  const valueProps = getValueProposition();

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Key Highlights</Text>

        <View style={styles.highlightsContainer}>
          <View style={styles.highlightRow}>
            <View style={styles.highlightName}>
              <Ionicons name="speedometer" size={20} color="#6b7280" />
              <Text style={styles.highlightText}>EPA Range</Text>
            </View>
            <Text style={styles.highlightValue}>
              {formatRange(model.range_epa_miles)}
            </Text>
          </View>

          {model.efficiency_mpge && (
            <View style={styles.highlightRow}>
              <View style={styles.highlightName}>
                <Ionicons name="leaf" size={20} color="#6b7280" />
                <Text style={styles.highlightText}>Efficiency</Text>
              </View>
              <View style={styles.highlightName}>
                <Text style={[styles.highlightValue, { marginRight: 8 }]}>
                  {model.efficiency_mpge} MPGe
                </Text>
                {efficiencyRating && (
                  <View
                    style={[styles.efficiencyBadge, efficiencyRating.bgColor]}
                  >
                    <Text
                      style={[
                        styles.efficiencyBadgeText,
                        efficiencyRating.color,
                      ]}
                    >
                      {efficiencyRating.rating}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          )}

          {model.acceleration_0_60_mph && (
            <View style={styles.highlightRow}>
              <View style={styles.highlightName}>
                <Ionicons name="flash" size={20} color="#6b7280" />
                <Text style={styles.highlightText}>0-60 mph</Text>
              </View>
              <Text style={styles.highlightValue}>
                {model.acceleration_0_60_mph}s
              </Text>
            </View>
          )}

          {model.charging_speed_dc_kw && (
            <View style={styles.highlightRow}>
              <View style={styles.highlightName}>
                <Ionicons name="flash-outline" size={20} color="#6b7280" />
                <Text style={styles.highlightText}>DC Fast Charging</Text>
              </View>
              <Text style={styles.highlightValue}>
                {model.charging_speed_dc_kw} kW
              </Text>
            </View>
          )}

          {model.seating_capacity && (
            <View style={styles.highlightRow}>
              <View style={styles.highlightName}>
                <Ionicons name="people" size={20} color="#6b7280" />
                <Text style={styles.highlightText}>Seating</Text>
              </View>
              <Text style={styles.highlightValue}>
                {model.seating_capacity} seats
              </Text>
            </View>
          )}
        </View>
      </View>

      {valueProps.length > 0 && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Why This Vehicle</Text>
          <View style={styles.valuePropsContainer}>
            {valueProps.map((prop, index) => (
              <View key={index} style={styles.valuePropBadge}>
                <Text style={styles.valuePropText}>{prop}</Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {(pros.length > 0 || cons.length > 0) && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Pros & Cons</Text>
          <View style={{ marginTop: 16 }}>
            {pros.length > 0 && (
              <View>
                <Text style={styles.prosTitle}>Pros</Text>
                {pros.map((pro, index) => (
                  <View key={index} style={styles.proConItem}>
                    <Ionicons
                      name="checkmark-circle"
                      size={16}
                      color="#22c55e"
                    />
                    <Text style={styles.proConText}>{pro}</Text>
                  </View>
                ))}
              </View>
            )}

            {cons.length > 0 && (
              <View style={{ marginTop: pros.length > 0 ? 16 : 0 }}>
                <Text style={styles.consTitle}>Considerations</Text>
                {cons.map((con, index) => (
                  <View key={index} style={styles.proConItem}>
                    <Ionicons
                      name="information-circle"
                      size={16}
                      color="#f59e0b"
                    />
                    <Text style={styles.proConText}>{con}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        </View>
      )}

      <View style={styles.card}>
        <Text style={styles.cardTitle}>Ideal For</Text>
        <View style={styles.idealForContainer}>
          {model.body_type === "suv" && (
            <View style={styles.idealForItem}>
              <Ionicons name="people" size={16} color="#6b7280" />
              <Text style={styles.idealForText}>
                Families needing space and versatility
              </Text>
            </View>
          )}
          {model.range_epa_miles && model.range_epa_miles > 300 && (
            <View style={styles.idealForItem}>
              <Ionicons name="car" size={16} color="#6b7280" />
              <Text style={styles.idealForText}>
                Long-distance commuters and road trips
              </Text>
            </View>
          )}
          {model.efficiency_mpge && model.efficiency_mpge > 120 && (
            <View style={styles.idealForItem}>
              <Ionicons name="leaf" size={16} color="#6b7280" />
              <Text style={styles.idealForText}>Eco-conscious drivers</Text>
            </View>
          )}
          {model.acceleration_0_60_mph && model.acceleration_0_60_mph < 5.0 && (
            <View style={styles.idealForItem}>
              <Ionicons name="flash" size={16} color="#6b7280" />
              <Text style={styles.idealForText}>Performance enthusiasts</Text>
            </View>
          )}
        </View>
      </View>

      {similarModels.length > 0 && (
        <View style={styles.card}>
          <View style={styles.similarVehiclesHeader}>
            <Text style={styles.cardTitle}>Similar Vehicles</Text>
            <TouchableOpacity onPress={() => router.push("/(tabs)/browse")}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.similarVehiclesContainer}>
              {similarModels.map((similarModel) => (
                <View key={similarModel.id} style={{ width: 200 }}>
                  <EVModelCard
                    model={similarModel}
                    viewMode="grid"
                    onPress={() =>
                      router.push(`/ev-models/${similarModel.id}` as any)
                    }
                    onCompareToggle={() => {}}
                    isInComparison={false}
                  />
                </View>
              ))}
            </View>
          </ScrollView>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    backgroundColor: "white",
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
    marginBottom: 16,
  },
  highlightsContainer: {
    gap: 12,
  },
  highlightRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  highlightName: {
    flexDirection: "row",
    alignItems: "center",
  },
  highlightText: {
    color: "#374151",
    marginLeft: 8,
  },
  highlightValue: {
    fontWeight: "600",
    color: "#111827",
  },
  efficiencyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 9999,
  },
  efficiencyBadgeText: {
    fontSize: 12,
    fontWeight: "500",
  },
  excellentText: { color: "#16a34a" },
  excellentBg: { backgroundColor: "#dcfce7" },
  veryGoodText: { color: "#2563eb" },
  veryGoodBg: { backgroundColor: "#dbeafe" },
  goodText: { color: "#ca8a04" },
  goodBg: { backgroundColor: "#fef9c3" },
  averageText: { color: "#4b5563" },
  averageBg: { backgroundColor: "#f3f4f6" },
  valuePropsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  valuePropBadge: {
    backgroundColor: "#e0e7ff",
    borderRadius: 9999,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  valuePropText: {
    color: "#4338ca",
    fontSize: 14,
    fontWeight: "500",
  },
  prosTitle: {
    color: "#16a34a",
    fontWeight: "500",
    marginBottom: 8,
  },
  consTitle: {
    color: "#f97316",
    fontWeight: "500",
    marginBottom: 8,
  },
  proConItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  proConText: {
    color: "#374151",
    marginLeft: 8,
  },
  idealForContainer: {
    gap: 8,
  },
  idealForItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  idealForText: {
    color: "#374151",
    marginLeft: 8,
  },
  similarVehiclesHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  viewAllText: {
    color: "#4f46e5",
    fontWeight: "500",
  },
  similarVehiclesContainer: {
    flexDirection: "row",
    gap: 12,
  },
});
