import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { EVModel } from "../../../types";

interface EVDetailsSpecsProps {
  model: EVModel;
}

interface SpecSectionData {
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  specs: Array<{
    label: string;
    value: string | number | null;
    unit?: string;
  }>;
}

export function EVDetailsSpecs({ model }: EVDetailsSpecsProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>([
    "Performance",
  ]);

  const formatValue = (value: string | number | null, unit?: string) => {
    if (value === null || value === undefined) return "N/A";
    if (typeof value === "number") {
      return unit
        ? `${value.toLocaleString()} ${unit}`
        : value.toLocaleString();
    }
    return unit ? `${value} ${unit}` : value;
  };

  const toggleSection = (sectionTitle: string) => {
    setExpandedSections((prev) =>
      prev.includes(sectionTitle)
        ? prev.filter((s) => s !== sectionTitle)
        : [...prev, sectionTitle]
    );
  };

  const specSections: SpecSectionData[] = [
    {
      title: "Performance",
      icon: "flash",
      specs: [
        {
          label: "0-60 mph",
          value: model.acceleration_0_60_mph,
          unit: "seconds",
        },
        { label: "Top Speed", value: model.top_speed_mph, unit: "mph" },
        { label: "Motor Power", value: model.motor_power_hp, unit: "hp" },
        {
          label: "Motor Torque",
          value: model.motor_torque_lb_ft,
          unit: "lb-ft",
        },
        { label: "Drivetrain", value: model.drivetrain?.toUpperCase() ?? null },
      ],
    },
    {
      title: "Battery & Range",
      icon: "battery-charging",
      specs: [
        {
          label: "Battery Capacity",
          value: model.battery_capacity_kwh,
          unit: "kWh",
        },
        { label: "EPA Range", value: model.range_epa_miles, unit: "miles" },
        { label: "WLTP Range", value: model.range_wltp_miles, unit: "miles" },
        {
          label: "Real-World Range",
          value: model.range_real_world_miles,
          unit: "miles",
        },
        {
          label: "Efficiency (EPA)",
          value: model.efficiency_mpge,
          unit: "MPGe",
        },
      ],
    },
    {
      title: "Charging",
      icon: "flash-outline",
      specs: [
        {
          label: "DC Fast Charging",
          value: model.charging_speed_dc_kw,
          unit: "kW",
        },
        { label: "AC Charging", value: model.charging_speed_ac_kw, unit: "kW" },
        {
          label: "10-80% Charging Time",
          value: model.charging_time_10_80_minutes,
          unit: "minutes",
        },
        {
          label: "Charging Ports",
          value: model.charging_ports?.join(", ") ?? null,
        },
      ],
    },
    {
      title: "Dimensions",
      icon: "resize",
      specs: [
        { label: "Length", value: model.length_inches, unit: "inches" },
        { label: "Width", value: model.width_inches, unit: "inches" },
        { label: "Height", value: model.height_inches, unit: "inches" },
        { label: "Wheelbase", value: model.wheelbase_inches, unit: "inches" },
        {
          label: "Ground Clearance",
          value: model.ground_clearance_inches,
          unit: "inches",
        },
        { label: "Curb Weight", value: model.curb_weight_lbs, unit: "lbs" },
      ],
    },
    {
      title: "Interior & Cargo",
      icon: "car",
      specs: [
        {
          label: "Seating Capacity",
          value: model.seating_capacity,
          unit: "seats",
        },
        {
          label: "Cargo Volume",
          value: model.cargo_volume_cubic_ft,
          unit: "cubic ft",
        },
        {
          label: "Body Type",
          value: model.body_type
            ? model.body_type.charAt(0).toUpperCase() + model.body_type.slice(1)
            : null,
        },
      ],
    },
    {
      title: "Pricing",
      icon: "cash",
      specs: [
        {
          label: "MSRP",
          value: model.price_msrp
            ? `$${(model.price_msrp / 100).toLocaleString()}`
            : null,
        },
        {
          label: "Base Price",
          value: model.price_base
            ? `$${(model.price_base / 100).toLocaleString()}`
            : null,
        },
        {
          label: "As Tested Price",
          value: model.price_as_tested
            ? `$${(model.price_as_tested / 100).toLocaleString()}`
            : null,
        },
        {
          label: "Production Status",
          value: model.production_status
            ? model.production_status?.charAt(0).toUpperCase() +
              model.production_status?.slice(1)
            : null,
        },
      ],
    },
  ];

  const SpecRow = ({
    label,
    value,
    unit,
    isLast,
  }: {
    label: string;
    value: string | number | null;
    unit?: string;
    isLast: boolean;
  }) => (
    <View style={[styles.specRow, isLast && styles.lastSpecRow]}>
      <Text style={styles.specLabel}>{label}</Text>
      <Text style={styles.specValue}>{formatValue(value, unit)}</Text>
    </View>
  );

  const SpecSection = ({ section }: { section: SpecSectionData }) => {
    const isExpanded = expandedSections.includes(section.title);
    const hasValidSpecs = section.specs.some(
      (spec) => spec.value !== null && spec.value !== undefined
    );

    if (!hasValidSpecs) return null;

    return (
      <View style={styles.sectionContainer}>
        <TouchableOpacity
          onPress={() => toggleSection(section.title)}
          style={styles.sectionHeader}
        >
          <View style={styles.sectionTitleContainer}>
            <Ionicons name={section.icon} size={20} color="#6b7280" />
            <Text style={styles.sectionTitle}>{section.title}</Text>
          </View>
          <Ionicons
            name={isExpanded ? "chevron-up" : "chevron-down"}
            size={20}
            color="#6b7280"
          />
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.specsContainer}>
            {section.specs
              .filter((spec) => spec.value !== null && spec.value !== undefined)
              .map((spec, index, arr) => (
                <SpecRow
                  key={index}
                  label={spec.label}
                  value={spec.value}
                  unit={spec.unit}
                  isLast={index === arr.length - 1}
                />
              ))}
          </View>
        )}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.headerCard}>
        <Text style={styles.headerTitle}>Complete Specifications</Text>
        <Text style={styles.headerSubtitle}>
          Detailed technical specifications for the {model.make} {model.model}
          {model.trim && ` ${model.trim}`}
        </Text>
      </View>

      {specSections.map((section, index) => (
        <SpecSection key={index} section={section} />
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerCard: {
    backgroundColor: "white",
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
    marginBottom: 8,
  },
  headerSubtitle: {
    color: "#4b5563",
  },
  sectionContainer: {
    backgroundColor: "white",
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    overflow: "hidden",
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    backgroundColor: "#f9fafb",
  },
  sectionTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
    marginLeft: 12,
  },
  specsContainer: {
    padding: 16,
  },
  specRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  lastSpecRow: {
    borderBottomWidth: 0,
  },
  specLabel: {
    color: "#374151",
    flex: 1,
  },
  specValue: {
    fontWeight: "500",
    color: "#111827",
    textAlign: "right",
  },
});
