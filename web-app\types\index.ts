// Export all types
export * from "./database";

// Common types used across the application
export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
}

export interface Vehicle {
  id: string;
  user_id: string;
  make: string;
  model: string;
  year: number;
  trim?: string;
  color?: string;
  vin?: string;
  license_plate?: string;
  battery_capacity_kwh: number;
  range_miles: number;
  efficiency_miles_per_kwh: number;
  current_battery_level: number;
  current_range_miles: number;
  odometer_miles: number;
  is_primary: boolean;
  status: "active" | "inactive" | "maintenance";
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface ChargingStation {
  id: string;
  name: string;
  network: string;
  address: string;
  city: string;
  state: string;
  zip_code?: string;
  country: string;
  latitude: number;
  longitude: number;
  total_stalls: number;
  available_stalls: number;
  max_power_kw: number;
  connector_types: string[];
  pricing_per_kwh?: number;
  pricing_per_minute?: number;
  amenities: string[];
  hours_of_operation?: string;
  status: "operational" | "maintenance" | "offline";
  last_updated: string;
  created_at: string;
}

export interface ChargingSession {
  id: string;
  user_id: string;
  vehicle_id: string;
  charging_station_id?: string;
  session_type: "home" | "public" | "workplace";
  start_time: string;
  end_time?: string;
  start_battery_level: number;
  end_battery_level?: number;
  energy_added_kwh?: number;
  cost_total?: number;
  cost_per_kwh?: number;
  power_kw?: number;
  status: "in_progress" | "completed" | "interrupted" | "failed";
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Trip {
  id: string;
  user_id: string;
  vehicle_id: string;
  start_location: string;
  end_location: string;
  start_latitude?: number;
  start_longitude?: number;
  end_latitude?: number;
  end_longitude?: number;
  start_time: string;
  end_time?: string;
  distance_miles?: number;
  duration_minutes?: number;
  start_battery_level?: number;
  end_battery_level?: number;
  energy_used_kwh?: number;
  efficiency_miles_per_kwh?: number;
  average_speed_mph?: number;
  weather_conditions?: string;
  trip_type?: "commute" | "leisure" | "business" | "other";
  notes?: string;
  status: "in_progress" | "completed" | "cancelled";
  created_at: string;
  updated_at: string;
}

export interface MaintenanceRecord {
  id: string;
  user_id: string;
  vehicle_id: string;
  service_type: string;
  description: string;
  service_date: string;
  odometer_miles?: number;
  cost?: number;
  service_provider?: string;
  next_service_miles?: number;
  next_service_date?: string;
  status: "scheduled" | "completed" | "overdue";
  notes?: string;
  attachments: string[];
  created_at: string;
  updated_at: string;
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Location types
export interface Location {
  latitude: number;
  longitude: number;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
}

// Analytics types
export interface VehicleAnalytics {
  total_miles: number;
  total_trips: number;
  average_efficiency: number;
  total_energy_used: number;
  total_charging_cost: number;
  carbon_saved_lbs: number;
  money_saved: number;
}

export interface ChargingAnalytics {
  total_sessions: number;
  total_energy: number;
  total_cost: number;
  average_session_duration: number;
  favorite_networks: string[];
}

// Form types
export interface VehicleFormData {
  make: string;
  model: string;
  year: number;
  trim?: string;
  color?: string;
  vin?: string;
  license_plate?: string;
  battery_capacity_kwh: number;
  range_miles: number;
  efficiency_miles_per_kwh?: number;
}

export interface ProfileFormData {
  full_name?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
}

// Constants
export const VEHICLE_MAKES = [
  "Tesla",
  "BMW",
  "Audi",
  "Mercedes-Benz",
  "Volkswagen",
  "Nissan",
  "Chevrolet",
  "Ford",
  "Hyundai",
  "Kia",
  "Volvo",
  "Polestar",
  "Lucid",
  "Rivian",
  "Other",
] as const;

export const CHARGING_NETWORKS = [
  "Tesla Supercharger",
  "ChargePoint",
  "Electrify America",
  "EVgo",
  "Blink",
  "Volta",
  "Greenlots",
  "Other",
] as const;

export const CONNECTOR_TYPES = [
  "Tesla",
  "CCS",
  "CHAdeMO",
  "Type 2",
  "J1772",
] as const;

export const TRIP_TYPES = ["commute", "leisure", "business", "other"] as const;

export const SERVICE_TYPES = [
  "tire_rotation",
  "brake_inspection",
  "software_update",
  "battery_check",
  "general_inspection",
  "recall_service",
  "warranty_repair",
  "other",
] as const;

export type ServiceType = (typeof SERVICE_TYPES)[number];

// ===== EV BUYER'S GUIDE TYPES =====

// Production Status
export const PRODUCTION_STATUS = [
  "current",
  "discontinued",
  "concept",
  "upcoming",
] as const;

export type ProductionStatus = (typeof PRODUCTION_STATUS)[number];

// Body Types
export const EV_BODY_TYPES = [
  "sedan",
  "suv",
  "hatchback",
  "truck",
  "coupe",
  "wagon",
  "convertible",
  "crossover",
  "van",
] as const;

export type EVBodyType = (typeof EV_BODY_TYPES)[number];

// Use Cases
export const USE_CASES = [
  "commuting",
  "family",
  "performance",
  "luxury",
  "utility",
  "eco_friendly",
  "first_ev",
  "upgrade",
] as const;

export type UseCase = (typeof USE_CASES)[number];

// Drivetrain Types
export const DRIVETRAIN_TYPES = ["fwd", "rwd", "awd"] as const;

export type DrivetrainType = (typeof DRIVETRAIN_TYPES)[number];

// Sort Options for EV Models
export const EV_SORT_OPTIONS = [
  "popularity_score",
  "price_msrp",
  "range_epa_miles",
  "efficiency_mpge",
  "acceleration_0_60_mph",
  "year",
  "make",
  "model",
] as const;

export type EVSortOption = (typeof EV_SORT_OPTIONS)[number];

// Priority Factors for Decision Making
export const PRIORITY_FACTORS = [
  "price",
  "range",
  "charging_speed",
  "performance",
  "features",
  "safety",
  "brand",
  "efficiency",
  "size",
  "cargo_space",
] as const;

export type PriorityFactor = (typeof PRIORITY_FACTORS)[number];

// Extended EV Model type with additional details for the details page
export interface EVModelWithDetails extends EVModel {
  ev_manufacturers: EVManufacturer;
  buyerMetrics: {
    costPerMile: number | null;
    chargingEfficiency: number | null;
    practicalRange: number | null;
    powerToWeightRatio: number | null;
  };
  similarModels: EVModel[];
}

// Supporting Interfaces for EV Models
export interface SafetyRatings {
  nhtsa_overall?: number; // 1-5 stars
  nhtsa_frontal?: number;
  nhtsa_side?: number;
  nhtsa_rollover?: number;
  iihs_top_safety_pick?: boolean;
  iihs_top_safety_pick_plus?: boolean;
  euro_ncap_overall?: number; // 1-5 stars
  other_ratings?: Record<string, any>;
}

export interface WarrantyInfo {
  basic_years?: number;
  basic_miles?: number;
  powertrain_years?: number;
  powertrain_miles?: number;
  battery_years?: number;
  battery_miles?: number;
  corrosion_years?: number;
  roadside_assistance_years?: number;
  details?: string;
}

export interface TotalCostOwnership {
  purchase_price?: number;
  federal_tax_credit?: number;
  state_incentives?: number;
  local_incentives?: number;
  estimated_annual_fuel_cost?: number;
  estimated_annual_maintenance?: number;
  estimated_insurance?: number;
  estimated_depreciation_5_year?: number;
  estimated_total_5_year?: number;
}

export interface UserReviewsSummary {
  average_rating?: number; // 1-5
  total_reviews?: number;
  common_pros?: string[];
  common_cons?: string[];
  reliability_score?: number;
  owner_satisfaction?: number;
  would_buy_again_percentage?: number;
}

export interface ProsCons {
  pros?: string[];
  cons?: string[];
  best_for?: string[];
  not_ideal_for?: string[];
}

export interface UserPriorities {
  price_weight?: number; // 0-100
  range_weight?: number;
  charging_speed_weight?: number;
  performance_weight?: number;
  features_weight?: number;
  safety_weight?: number;
  brand_weight?: number;
  efficiency_weight?: number;
  size_weight?: number;
  cargo_space_weight?: number;
}

export interface PriorityWeights {
  [key: string]: number; // 0-100 weight for each priority factor
}

export interface LifestyleData {
  has_garage?: boolean;
  apartment_living?: boolean;
  long_commute?: boolean;
  frequent_road_trips?: boolean;
  family_size?: number;
  outdoor_activities?: boolean;
  tech_enthusiast?: boolean;
  environmental_priority?: boolean;
  budget_conscious?: boolean;
}

export interface ComparisonMetrics {
  priceRange: {
    min: number;
    max: number;
  };
  rangeComparison: {
    min: number;
    max: number;
  };
  chargingSpeed: {
    min: number;
    max: number;
  };
  acceleration: {
    fastest: number;
    slowest: number;
  };
}

export interface BuyerMetrics {
  costPerMile?: number | null;
  chargingEfficiency?: number | null;
  practicalRange?: number | null;
  powerToWeightRatio?: number | null;
}

// Main EV Model Interface (buyer-focused)
export interface EVModel {
  id: string;
  manufacturer_id: string | null;
  make: string;
  model: string;
  year: number;
  trim: string | null;
  body_type: string | null;

  // Pricing Information
  price_msrp: number | null;
  price_base: number | null;
  price_as_tested: number | null;
  production_status: ProductionStatus;
  market_regions: string[] | null;

  // Technical Specifications
  battery_capacity_kwh: number;
  range_epa_miles: number | null;
  range_wltp_miles: number | null;
  range_real_world_miles: number | null;
  efficiency_mpge: number | null;
  charging_speed_dc_kw: number | null;
  charging_speed_ac_kw: number | null;
  charging_ports: string[] | null;
  charging_time_10_80_minutes: number | null;

  // Performance
  acceleration_0_60_mph: number | null;
  top_speed_mph: number | null;
  motor_power_hp: number | null;
  motor_torque_lb_ft: number | null;
  drivetrain: string | null;

  // Physical Specifications
  length_inches: number | null;
  width_inches: number | null;
  height_inches: number | null;
  wheelbase_inches: number | null;
  ground_clearance_inches: number | null;
  cargo_volume_cubic_ft: number | null;
  seating_capacity: number | null;
  curb_weight_lbs: number | null;

  // Features & Technology
  features: Record<string, any> | null;
  safety_ratings: SafetyRatings | null;
  warranty_info: WarrantyInfo | null;
  total_cost_ownership: TotalCostOwnership | null;
  user_reviews_summary: UserReviewsSummary | null;
  pros_cons: ProsCons | null;

  // Media
  images: string[] | null;
  videos: string[] | null;
  brochure_url: string | null;

  // Buyer Decision Support
  is_featured: boolean;
  popularity_score: number;
  editor_choice: boolean;
  best_value: boolean;

  // Metadata
  created_at: string;
  updated_at: string;

  // Joined data (when included)
  ev_manufacturers?: EVManufacturer;
  buyerMetrics?: BuyerMetrics;
  similarModels?: EVModel[];
}

export interface EVManufacturer {
  id: string;
  name: string;
  logo_url: string | null;
  website_url: string | null;
  headquarters_country: string | null;
  founded_year: number | null;
  description: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  model_count?: number;
}

export interface UserFavorite {
  id: string;
  user_id: string;
  ev_model_id: string;
  notes: string | null;
  created_at: string;
  ev_models?: EVModel;
}

export interface ComparisonSession {
  id: string;
  user_id: string;
  name: string | null;
  ev_model_ids: string[] | null;
  user_priorities: UserPriorities | null;
  comparison_notes: string | null;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  ev_models?: EVModel[];
  metrics?: ComparisonMetrics;
}

export interface UserEVPreferences {
  id: string;
  user_id: string;
  budget_min: number | null;
  budget_max: number | null;
  range_requirement_miles: number | null;
  body_type_preferences: string[] | null;
  use_case: UseCase | null;
  charging_at_home: boolean | null;
  daily_driving_miles: number | null;
  priority_weights: PriorityWeights | null;
  lifestyle_data: LifestyleData | null;
  created_at: string;
  updated_at: string;
}

// API Response Types
export interface EVModelsResponse {
  data: EVModel[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: EVModelFilters;
  sort: {
    sortBy: string;
    sortOrder: "asc" | "desc";
  };
}

export interface EVModelResponse {
  data: EVModel;
}

export interface SearchResponse {
  data: EVModel[];
  suggestions: string[];
  query: string;
}

export interface FavoritesResponse {
  data: UserFavorite[];
}

export interface ComparisonsResponse {
  data: ComparisonSession[];
}

export interface ComparisonResponse {
  data: ComparisonSession;
}

export interface ManufacturersResponse {
  data: EVManufacturer[];
}

// Filter and Search Types
export interface EVModelFilters {
  make?: string;
  bodyType?: string;
  priceMin?: string;
  priceMax?: string;
  rangeMin?: string;
  productionStatus?: string;
  featured?: string;
  bestValue?: string;
  search?: string;
}

export interface SearchFilters {
  query?: string;
  make?: string[];
  bodyTypes?: EVBodyType[];
  priceRange?: {
    min: number;
    max: number;
  };
  rangeRequirement?: number;
  productionStatus?: ProductionStatus[];
  features?: string[];
  chargingPorts?: string[];
  seatingCapacity?: number;
  cargoSpace?: number;
}

export interface SortOptions {
  field: EVSortOption;
  order: "asc" | "desc";
}

// Form Data Types for EV Buyer Guide
export interface EVPreferencesFormData {
  budget_min?: number;
  budget_max?: number;
  range_requirement_miles?: number;
  body_type_preferences?: EVBodyType[];
  use_case?: UseCase;
  charging_at_home?: boolean;
  daily_driving_miles?: number;
  priority_weights?: PriorityWeights;
  lifestyle_data?: LifestyleData;
}

export interface ComparisonFormData {
  name?: string;
  ev_model_ids: string[];
  user_priorities?: UserPriorities;
  comparison_notes?: string;
  is_public?: boolean;
}

export interface FavoriteFormData {
  ev_model_id: string;
  notes?: string;
}
