// Forum Types for GreenMilesEV
// These types correspond to the database schema for the forum system

export interface ForumCategory {
  id: string
  name: string
  description: string | null
  slug: string
  icon: string | null
  color: string | null
  sort_order: number
  is_active: boolean
  requires_approval: boolean
  parent_category_id: string | null
  topic_count: number
  post_count: number
  last_post_id: string | null
  last_activity_at: string | null
  created_at: string
  updated_at: string
}

export interface ForumTopic {
  id: string
  category_id: string
  author_id: string
  title: string
  slug: string
  content: string
  content_type: 'markdown' | 'html' | 'text'
  is_pinned: boolean
  is_locked: boolean
  is_approved: boolean
  is_featured: boolean
  related_ev_model_id: string | null
  related_manufacturer_id: string | null
  related_charging_station_id: string | null
  view_count: number
  post_count: number
  like_count: number
  bookmark_count: number
  last_post_id: string | null
  last_post_at: string
  last_activity_at: string
  tags: string[] | null
  meta_description: string | null
  created_at: string
  updated_at: string
}

export interface ForumPost {
  id: string
  topic_id: string
  author_id: string
  parent_post_id: string | null
  content: string
  content_type: 'markdown' | 'html' | 'text'
  raw_content: string | null
  is_approved: boolean
  is_solution: boolean
  is_edited: boolean
  edit_reason: string | null
  edited_at: string | null
  edited_by_id: string | null
  like_count: number
  dislike_count: number
  reaction_count: Record<string, number>
  post_number: number
  reply_count: number
  thread_level: number
  is_flagged: boolean
  flag_count: number
  moderation_notes: string | null
  attachments: ForumAttachment[]
  created_at: string
  updated_at: string
}

export interface ForumReaction {
  id: string
  user_id: string
  post_id: string | null
  topic_id: string | null
  reaction_type: 'like' | 'dislike' | 'helpful' | 'solved' | 'funny' | 'insightful'
  created_at: string
}

export interface ForumBookmark {
  id: string
  user_id: string
  topic_id: string | null
  post_id: string | null
  notes: string | null
  created_at: string
}

export interface ForumModerator {
  id: string
  user_id: string
  category_id: string | null
  role: 'moderator' | 'admin' | 'super_admin'
  permissions: Record<string, boolean>
  assigned_by_id: string
  assigned_at: string
  is_active: boolean
}

export interface ForumUserStats {
  user_id: string
  topic_count: number
  post_count: number
  like_count_received: number
  solution_count: number
  reputation_score: number
  forum_rank: string
  last_post_at: string | null
  created_at: string
  updated_at: string
}

export interface ForumFlag {
  id: string
  flagger_id: string
  post_id: string | null
  topic_id: string | null
  flag_type:
    | 'spam'
    | 'inappropriate'
    | 'off_topic'
    | 'harassment'
    | 'misinformation'
    | 'copyright'
    | 'other'
  reason: string
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed'
  reviewed_by_id: string | null
  reviewed_at: string | null
  resolution_notes: string | null
  created_at: string
}

export interface ForumAttachment {
  id: string
  filename: string
  url: string
  file_type: string
  file_size: number
  uploaded_at: string
}

// Extended types with relations
export interface ForumTopicWithDetails extends ForumTopic {
  category: ForumCategory
  author: {
    id: string
    full_name: string | null
    avatar_url: string | null
  }
  last_post?: ForumPost
  user_reaction?: ForumReaction
  is_bookmarked?: boolean
  // EV-related content
  related_ev_model?: {
    id: string
    make: string
    model: string
    year: number
  }
  related_manufacturer?: {
    id: string
    name: string
    logo_url: string | null
  }
  related_charging_station?: {
    id: string
    name: string
    network: string
  }
}

export interface ForumPostWithDetails extends ForumPost {
  author: {
    id: string
    full_name: string | null
    avatar_url: string | null
  }
  user_stats?: ForumUserStats
  user_reaction?: ForumReaction
  replies?: ForumPostWithDetails[]
  parent_post?: ForumPost
}

export interface ForumCategoryWithStats extends ForumCategory {
  subcategories?: ForumCategory[]
  recent_topics?: ForumTopicWithDetails[]
  last_post?: ForumPostWithDetails
}

// API Request/Response types
export interface CreateTopicRequest {
  category_id: string
  title: string
  content: string
  content_type?: 'markdown' | 'html' | 'text'
  tags?: string[]
  related_ev_model_id?: string
  related_manufacturer_id?: string
  related_charging_station_id?: string
  meta_description?: string
}

export interface CreatePostRequest {
  topic_id: string
  content: string
  content_type?: 'markdown' | 'html' | 'text'
  parent_post_id?: string
}

export interface UpdateTopicRequest {
  title?: string
  content?: string
  tags?: string[]
  is_pinned?: boolean
  is_locked?: boolean
  meta_description?: string
}

export interface UpdatePostRequest {
  content?: string
  edit_reason?: string
  is_solution?: boolean
}

export interface ForumSearchParams {
  q?: string
  category_id?: string
  tags?: string[]
  author_id?: string
  sort_by?: 'created_at' | 'last_activity_at' | 'view_count' | 'like_count'
  sort_order?: 'asc' | 'desc'
  page?: number
  limit?: number
  is_featured?: boolean
  related_ev_model_id?: string
}

export interface ForumSearchResult {
  topics: ForumTopicWithDetails[]
  total_count: number
  page: number
  limit: number
  has_next_page: boolean
  has_prev_page: boolean
}

// Forum notification types
export interface ForumNotification {
  id: string
  user_id: string
  type: 'new_reply' | 'new_reaction' | 'topic_locked' | 'post_flagged' | 'moderator_action'
  title: string
  message: string
  data: Record<string, any>
  is_read: boolean
  created_at: string
}

// Form validation types
export interface TopicFormData {
  category_id: string
  title: string
  content: string
  tags: string[]
  related_ev_model_id?: string
}

export interface PostFormData {
  content: string
  parent_post_id?: string
}

// UI state types
export interface ForumUIState {
  selectedCategory: ForumCategory | null
  currentTopic: ForumTopicWithDetails | null
  isCreatingTopic: boolean
  isCreatingPost: boolean
  searchQuery: string
  filters: ForumSearchParams
}

// Permission types
export type ForumPermission =
  | 'can_create_topic'
  | 'can_create_post'
  | 'can_edit_own_posts'
  | 'can_delete_own_posts'
  | 'can_react'
  | 'can_bookmark'
  | 'can_flag_content'
  | 'can_moderate'
  | 'can_pin_topics'
  | 'can_lock_topics'
  | 'can_delete_any_post'
  | 'can_ban_users'

export interface ForumUserPermissions {
  user_id: string
  permissions: ForumPermission[]
  is_moderator: boolean
  is_admin: boolean
  moderated_categories: string[]
}

// Analytics types
export interface ForumAnalytics {
  total_topics: number
  total_posts: number
  total_users: number
  active_users_today: number
  popular_categories: Array<{
    category: ForumCategory
    topic_count: number
    activity_score: number
  }>
  trending_topics: ForumTopicWithDetails[]
  user_engagement: {
    average_posts_per_user: number
    average_reactions_per_post: number
    most_active_users: Array<{
      user_id: string
      full_name: string | null
      post_count: number
      reputation_score: number
    }>
  }
}
