-- Migration: Create Forum Tables for GreenMilesEV
-- This creates a comprehensive forum system integrated with the EV marketplace

-- Create forum_categories table
CREATE TABLE forum_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    slug VARCHAR(100) NOT NULL UNIQUE,
    icon VARCHAR(50), -- Icon class or emoji for UI
    color VARCHAR(7), -- Hex color code for theming
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    requires_approval BOOLEAN DEFAULT false, -- Posts need approval
    parent_category_id UUID REFERENCES forum_categories(id) ON DELETE SET NULL,
    
    -- Statistics
    topic_count INTEGER DEFAULT 0,
    post_count INTEGER DEFAULT 0,
    last_post_id UUID, -- Will be set after posts table creation
    last_activity_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create forum_topics table
CREATE TABLE forum_topics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    category_id UUID NOT NULL REFERENCES forum_categories(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    content_type VARCHAR(20) DEFAULT 'markdown' CHECK (content_type IN ('markdown', 'html', 'text')),
    
    -- Topic metadata
    is_pinned BOOLEAN DEFAULT false,
    is_locked BOOLEAN DEFAULT false,
    is_approved BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    
    -- EV Marketplace Integration - Link topics to EV content
    related_ev_model_id UUID REFERENCES ev_models(id) ON DELETE SET NULL,
    related_manufacturer_id UUID REFERENCES ev_manufacturers(id) ON DELETE SET NULL,
    related_charging_station_id UUID REFERENCES charging_stations(id) ON DELETE SET NULL,
    
    -- Engagement metrics
    view_count INTEGER DEFAULT 0,
    post_count INTEGER DEFAULT 1, -- Includes the initial post
    like_count INTEGER DEFAULT 0,
    bookmark_count INTEGER DEFAULT 0,
    
    -- Activity tracking
    last_post_id UUID, -- Will be set after posts table creation
    last_post_at TIMESTAMPTZ DEFAULT now(),
    last_activity_at TIMESTAMPTZ DEFAULT now(),
    
    -- SEO and categorization
    tags TEXT[], -- Array of tags for better organization
    meta_description TEXT,
    
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create forum_posts table
CREATE TABLE forum_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    topic_id UUID NOT NULL REFERENCES forum_topics(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    parent_post_id UUID REFERENCES forum_posts(id) ON DELETE CASCADE, -- For threaded replies
    
    -- Content
    content TEXT NOT NULL,
    content_type VARCHAR(20) DEFAULT 'markdown' CHECK (content_type IN ('markdown', 'html', 'text')),
    raw_content TEXT, -- Original unprocessed content for editing
    
    -- Post metadata
    is_approved BOOLEAN DEFAULT true,
    is_solution BOOLEAN DEFAULT false, -- Mark as solution to the topic
    is_edited BOOLEAN DEFAULT false,
    edit_reason TEXT,
    edited_at TIMESTAMPTZ,
    edited_by_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    
    -- Engagement
    like_count INTEGER DEFAULT 0,
    dislike_count INTEGER DEFAULT 0,
    reaction_count JSONB DEFAULT '{}', -- Store reaction counts by type
    
    -- Hierarchy and ordering
    post_number INTEGER NOT NULL, -- Sequential number within topic (1, 2, 3...)
    reply_count INTEGER DEFAULT 0, -- Direct replies to this post
    thread_level INTEGER DEFAULT 0, -- Nesting level (0 = top level, 1 = reply, etc.)
    
    -- Moderation
    is_flagged BOOLEAN DEFAULT false,
    flag_count INTEGER DEFAULT 0,
    moderation_notes TEXT,
    
    -- Attachments and media
    attachments JSONB DEFAULT '[]', -- Array of attachment metadata
    
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create forum_reactions table for likes/dislikes/custom reactions
CREATE TABLE forum_reactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    post_id UUID REFERENCES forum_posts(id) ON DELETE CASCADE,
    topic_id UUID REFERENCES forum_topics(id) ON DELETE CASCADE,
    reaction_type VARCHAR(20) NOT NULL CHECK (reaction_type IN ('like', 'dislike', 'helpful', 'solved', 'funny', 'insightful')),
    created_at TIMESTAMPTZ DEFAULT now(),
    
    -- Ensure user can only have one reaction per post/topic
    UNIQUE(user_id, post_id, reaction_type),
    UNIQUE(user_id, topic_id, reaction_type),
    
    -- Must react to either a post or topic, not both
    CHECK (
        (post_id IS NOT NULL AND topic_id IS NULL) OR 
        (post_id IS NULL AND topic_id IS NOT NULL)
    )
);

-- Create forum_bookmarks table
CREATE TABLE forum_bookmarks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    topic_id UUID REFERENCES forum_topics(id) ON DELETE CASCADE,
    post_id UUID REFERENCES forum_posts(id) ON DELETE CASCADE,
    notes TEXT, -- Personal notes about the bookmark
    created_at TIMESTAMPTZ DEFAULT now(),
    
    -- User can bookmark either a topic or specific post
    CHECK (
        (topic_id IS NOT NULL AND post_id IS NULL) OR 
        (topic_id IS NULL AND post_id IS NOT NULL)
    ),
    
    -- Unique bookmark per user per item
    UNIQUE(user_id, topic_id),
    UNIQUE(user_id, post_id)
);

-- Create forum_moderators table
CREATE TABLE forum_moderators (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    category_id UUID REFERENCES forum_categories(id) ON DELETE CASCADE, -- NULL = global moderator
    role VARCHAR(20) NOT NULL DEFAULT 'moderator' CHECK (role IN ('moderator', 'admin', 'super_admin')),
    permissions JSONB DEFAULT '{}', -- Specific permissions like: {"can_delete": true, "can_ban": false}
    assigned_by_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMPTZ DEFAULT now(),
    is_active BOOLEAN DEFAULT true,
    
    UNIQUE(user_id, category_id)
);

-- Create forum_user_stats table for user forum activity
CREATE TABLE forum_user_stats (
    user_id UUID PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
    topic_count INTEGER DEFAULT 0,
    post_count INTEGER DEFAULT 0,
    like_count_received INTEGER DEFAULT 0,
    solution_count INTEGER DEFAULT 0, -- Solutions marked by others
    reputation_score INTEGER DEFAULT 0,
    forum_rank VARCHAR(50) DEFAULT 'Newcomer',
    last_post_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create forum_flags table for reporting inappropriate content
CREATE TABLE forum_flags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    flagger_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    post_id UUID REFERENCES forum_posts(id) ON DELETE CASCADE,
    topic_id UUID REFERENCES forum_topics(id) ON DELETE CASCADE,
    flag_type VARCHAR(30) NOT NULL CHECK (flag_type IN ('spam', 'inappropriate', 'off_topic', 'harassment', 'misinformation', 'copyright', 'other')),
    reason TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')),
    reviewed_by_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    reviewed_at TIMESTAMPTZ,
    resolution_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    
    -- Can flag either a post or topic
    CHECK (
        (post_id IS NOT NULL AND topic_id IS NULL) OR 
        (post_id IS NULL AND topic_id IS NOT NULL)
    ),
    
    -- User can only flag the same item once
    UNIQUE(flagger_id, post_id),
    UNIQUE(flagger_id, topic_id)
);

-- Add foreign key constraints for last_post references (after all tables are created)
ALTER TABLE forum_categories ADD CONSTRAINT fk_forum_categories_last_post 
    FOREIGN KEY (last_post_id) REFERENCES forum_posts(id) ON DELETE SET NULL;

ALTER TABLE forum_topics ADD CONSTRAINT fk_forum_topics_last_post 
    FOREIGN KEY (last_post_id) REFERENCES forum_posts(id) ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX idx_forum_topics_category_id ON forum_topics(category_id);
CREATE INDEX idx_forum_topics_author_id ON forum_topics(author_id);
CREATE INDEX idx_forum_topics_last_activity ON forum_topics(last_activity_at DESC);
CREATE INDEX idx_forum_topics_is_pinned ON forum_topics(is_pinned) WHERE is_pinned = true;
CREATE INDEX idx_forum_topics_tags ON forum_topics USING GIN(tags);
CREATE INDEX idx_forum_topics_related_ev_model ON forum_topics(related_ev_model_id) WHERE related_ev_model_id IS NOT NULL;

CREATE INDEX idx_forum_posts_topic_id ON forum_posts(topic_id);
CREATE INDEX idx_forum_posts_author_id ON forum_posts(author_id);
CREATE INDEX idx_forum_posts_parent_post_id ON forum_posts(parent_post_id);
CREATE INDEX idx_forum_posts_created_at ON forum_posts(created_at);
CREATE INDEX idx_forum_posts_post_number ON forum_posts(topic_id, post_number);

CREATE INDEX idx_forum_reactions_user_id ON forum_reactions(user_id);
CREATE INDEX idx_forum_reactions_post_id ON forum_reactions(post_id);
CREATE INDEX idx_forum_reactions_topic_id ON forum_reactions(topic_id);

CREATE INDEX idx_forum_bookmarks_user_id ON forum_bookmarks(user_id);
CREATE INDEX idx_forum_categories_parent_id ON forum_categories(parent_category_id);
CREATE INDEX idx_forum_categories_sort_order ON forum_categories(sort_order);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_forum_categories_updated_at 
    BEFORE UPDATE ON forum_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_forum_topics_updated_at 
    BEFORE UPDATE ON forum_topics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_forum_posts_updated_at 
    BEFORE UPDATE ON forum_posts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_forum_user_stats_updated_at 
    BEFORE UPDATE ON forum_user_stats 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default forum categories for EV marketplace
INSERT INTO forum_categories (name, description, slug, icon, color, sort_order) VALUES
('General Discussion', 'General discussions about electric vehicles', 'general', '💬', '#3B82F6', 1),
('EV Models & Reviews', 'Discuss specific EV models, share reviews and experiences', 'ev-models', '🚗', '#10B981', 2),
('Charging & Infrastructure', 'Charging stations, home charging, and infrastructure discussions', 'charging', '⚡', '#F59E0B', 3),
('Buying Advice', 'Get help choosing your next EV', 'buying-advice', '🛒', '#8B5CF6', 4),
('Technical Support', 'Technical questions and troubleshooting', 'tech-support', '🔧', '#EF4444', 5),
('Community Meetups', 'Local events and meetups', 'meetups', '🤝', '#06B6D4', 6),
('News & Updates', 'Latest EV news and industry updates', 'news', '��', '#64748B', 7); 