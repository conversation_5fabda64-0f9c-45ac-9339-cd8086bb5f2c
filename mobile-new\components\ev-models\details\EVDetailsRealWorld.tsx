import React from "react";
import { View, Text, ScrollView, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { EVModel } from "../../../types";

interface EVDetailsRealWorldProps {
  model: EVModel;
}

export function EVDetailsRealWorld({ model }: EVDetailsRealWorldProps) {
  const epaRange = model.range_epa_miles || 0;
  const realWorldRange = model.range_real_world_miles || epaRange * 0.85;
  const epaEfficiency = model.efficiency_mpge || 0;
  const realWorldEfficiency = epaEfficiency * 0.85;
  const annualMiles = 12000;
  const annualEnergyUse =
    epaEfficiency > 0 ? (annualMiles / epaEfficiency) * 33.7 : 0;
  const co2Reduction = (annualMiles / 28) * 19.6;

  const PerformanceCard = ({
    title,
    epaValue,
    realWorldValue,
    unit,
    icon,
  }: {
    title: string;
    epaValue: number;
    realWorldValue: number;
    unit: string;
    icon: keyof typeof Ionicons.glyphMap;
  }) => {
    const difference = epaValue - realWorldValue;
    const percentage = epaValue > 0 ? (realWorldValue / epaValue) * 100 : 0;

    return (
      <View style={styles.card}>
        <View style={styles.performanceCardHeader}>
          <Ionicons name={icon} size={20} color="#6b7280" />
          <Text style={styles.performanceCardTitle}>{title}</Text>
        </View>

        <View style={styles.performanceDetails}>
          <View style={styles.performanceRow}>
            <Text style={styles.performanceLabel}>EPA Rating</Text>
            <Text style={styles.performanceValue}>
              {Math.round(epaValue)} {unit}
            </Text>
          </View>

          <View style={styles.performanceRow}>
            <Text style={styles.performanceLabel}>Real-World</Text>
            <Text style={styles.performanceValue}>
              {Math.round(realWorldValue)} {unit}
            </Text>
          </View>

          <View style={styles.performanceDifferenceRow}>
            <View style={styles.performanceRow}>
              <Text style={styles.performanceLabel}>Difference</Text>
              <View style={{ flexDirection: "row", alignItems: "center" }}>
                <Text
                  style={[
                    styles.differenceText,
                    difference > 0 ? styles.orangeText : styles.greenText,
                  ]}
                >
                  {difference > 0 ? "-" : "+"}
                  {Math.abs(Math.round(difference))} {unit}
                </Text>
                <Text style={styles.percentageText}>
                  ({Math.round(percentage)}%)
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  };

  const DrivingTip = ({
    icon,
    title,
    description,
  }: {
    icon: keyof typeof Ionicons.glyphMap;
    title: string;
    description: string;
  }) => (
    <View style={styles.tipContainer}>
      <Ionicons
        name={icon}
        size={20}
        color="#22c55e"
        style={{ marginTop: 2 }}
      />
      <View style={styles.tipTextContainer}>
        <Text style={styles.tipTitle}>{title}</Text>
        <Text style={styles.tipDescription}>{description}</Text>
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Real-World Performance</Text>
        <Text style={styles.cardSubtitle}>
          How the {model.make} {model.model} performs in everyday driving
          conditions
        </Text>

        <View style={styles.infoBox}>
          <View style={styles.infoBoxHeader}>
            <Ionicons name="information-circle" size={20} color="#3b82f6" />
            <Text style={styles.infoBoxTitle}>Real-World vs EPA</Text>
          </View>
          <Text style={styles.infoBoxText}>
            Real-world performance typically differs from EPA ratings due to
            driving conditions, weather, and individual driving habits.
          </Text>
        </View>
      </View>

      <View style={{ marginHorizontal: 16, marginBottom: 16, gap: 12 }}>
        <PerformanceCard
          title="Driving Range"
          epaValue={epaRange}
          realWorldValue={realWorldRange}
          unit="miles"
          icon="speedometer"
        />
        <PerformanceCard
          title="Energy Efficiency"
          epaValue={epaEfficiency}
          realWorldValue={realWorldEfficiency}
          unit="MPGe"
          icon="leaf"
        />
      </View>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>
          Factors Affecting Real-World Performance
        </Text>
        <View style={{ gap: 16 }}>
          <View>
            <Text style={styles.factorTitle}>Weather Conditions</Text>
            <View style={styles.factorDetails}>
              <View style={styles.factorRow}>
                <Text style={styles.factorLabel}>
                  Cold Weather ({"<"} 32°F)
                </Text>
                <Text style={[styles.factorValue, styles.orangeText]}>
                  -20% to -40%
                </Text>
              </View>
              <View style={styles.factorRow}>
                <Text style={styles.factorLabel}>Hot Weather ({">"} 90°F)</Text>
                <Text style={[styles.factorValue, styles.orangeText]}>
                  -10% to -20%
                </Text>
              </View>
              <View style={styles.factorRow}>
                <Text style={styles.factorLabel}>Mild Weather (65-75°F)</Text>
                <Text style={[styles.factorValue, styles.greenText]}>
                  Optimal
                </Text>
              </View>
            </View>
          </View>
          <View>
            <Text style={styles.factorTitle}>Driving Conditions</Text>
            <View style={styles.factorDetails}>
              <View style={styles.factorRow}>
                <Text style={styles.factorLabel}>Highway (70+ mph)</Text>
                <Text style={[styles.factorValue, styles.orangeText]}>
                  -15% to -25%
                </Text>
              </View>
              <View style={styles.factorRow}>
                <Text style={styles.factorLabel}>City Driving</Text>
                <Text style={[styles.factorValue, styles.greenText]}>
                  +5% to +15%
                </Text>
              </View>
              <View style={styles.factorRow}>
                <Text style={styles.factorLabel}>Mixed Driving</Text>
                <Text style={[styles.factorValue, { color: "#4b5563" }]}>
                  EPA Baseline
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    backgroundColor: "white",
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
    marginBottom: 8,
  },
  cardSubtitle: {
    color: "#4b5563",
    marginBottom: 16,
  },
  infoBox: {
    backgroundColor: "#eff6ff",
    borderRadius: 8,
    padding: 16,
  },
  infoBoxHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  infoBoxTitle: {
    fontWeight: "500",
    color: "#1e3a8a",
    marginLeft: 8,
  },
  infoBoxText: {
    color: "#1e40af",
    fontSize: 14,
  },
  performanceCardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  performanceCardTitle: {
    color: "#111827",
    fontWeight: "500",
    marginLeft: 8,
  },
  performanceDetails: {
    gap: 8,
  },
  performanceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  performanceLabel: {
    color: "#4b5563",
  },
  performanceValue: {
    fontWeight: "600",
    color: "#111827",
  },
  performanceDifferenceRow: {
    borderTopWidth: 1,
    borderTopColor: "#e5e7eb",
    paddingTop: 8,
  },
  differenceText: {
    fontWeight: "500",
  },
  orangeText: {
    color: "#f97316",
  },
  greenText: {
    color: "#16a34a",
  },
  percentageText: {
    color: "#6b7280",
    marginLeft: 8,
  },
  tipContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: 12,
    backgroundColor: "#f9fafb",
    borderRadius: 8,
  },
  tipTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  tipTitle: {
    fontWeight: "500",
    color: "#111827",
    marginBottom: 4,
  },
  tipDescription: {
    color: "#4b5563",
    fontSize: 14,
  },
  factorTitle: {
    fontWeight: "500",
    color: "#111827",
    marginBottom: 8,
  },
  factorDetails: {
    gap: 8,
  },
  factorRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  factorLabel: {
    color: "#4b5563",
  },
  factorValue: {
    fontWeight: "500",
  },
});
