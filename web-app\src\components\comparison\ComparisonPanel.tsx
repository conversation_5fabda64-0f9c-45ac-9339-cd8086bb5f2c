'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { X, ChevronUp, ChevronDown, GitCompare, Trash2, ExternalLink } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useComparison } from '@/contexts/ComparisonContext'
import { useComparisonActions } from '@/hooks/useComparisonActions'
import { formatPrice, formatRange } from '@/utils/ev-buyer-guide'
import { PLACEHOLDER_IMAGES } from '@/constants/ev-buyer-guide'
import type { EVModel } from '../../../types'

export function ComparisonPanel() {
  const { state, togglePanel, closePanel } = useComparison()
  const { removeFromComparison, clearComparison } = useComparisonActions()
  const [isMinimized, setIsMinimized] = useState(false)

  if (state.models.length === 0) {
    return null
  }

  const handleToggleMinimize = () => {
    setIsMinimized(!isMinimized)
  }

  const handleCompareAll = () => {
    // Navigate to comparison page with all models
    const modelIds = state.models.map((m) => m.id).join(',')
    window.open(`/compare?models=${modelIds}`, '_blank')
  }

  return (
    <div
      className={cn(
        'fixed bottom-4 right-4 z-50 w-96 max-w-[calc(100vw-2rem)] transition-all duration-300',
        state.isOpen ? 'translate-y-0' : 'translate-y-full'
      )}
    >
      <Card className="border-electric-200 shadow-2xl dark:border-electric-800">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <GitCompare className="h-5 w-5 text-electric-600" />
              Compare EVs
              <Badge variant="secondary" className="ml-2">
                {state.models.length}/4
              </Badge>
            </CardTitle>

            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleToggleMinimize}
                className="h-8 w-8 p-0"
              >
                {isMinimized ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
              <Button variant="ghost" size="sm" onClick={closePanel} className="h-8 w-8 p-0">
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="space-y-4">
            {/* Model List */}
            <div className="max-h-64 space-y-3 overflow-y-auto">
              {state.models.map((model) => (
                <div
                  key={model.id}
                  className="flex items-center gap-3 rounded-lg border p-3 hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  {/* Model Image */}
                  <div className="relative h-12 w-16 shrink-0 overflow-hidden rounded">
                    <Image
                      src={model.images?.[0] || PLACEHOLDER_IMAGES.ev_model}
                      alt={`${model.make} ${model.model}`}
                      fill
                      className="object-cover"
                    />
                  </div>

                  {/* Model Info */}
                  <div className="min-w-0 flex-1">
                    <div className="truncate text-sm font-medium">
                      {model.make} {model.model}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      {formatPrice(model.price_msrp)} • {formatRange(model.range_epa_miles)}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="sm" asChild className="h-8 w-8 p-0">
                      <Link href={`/ev-models/${model.id}`}>
                        <ExternalLink className="h-3 w-3" />
                      </Link>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFromComparison(model.id)}
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Actions */}
            <div className="flex gap-2 border-t pt-2">
              <Button
                onClick={handleCompareAll}
                className="flex-1"
                disabled={state.models.length < 2}
              >
                <GitCompare className="mr-2 h-4 w-4" />
                Compare All
              </Button>
              <Button
                variant="outline"
                onClick={clearComparison}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>

            {/* Help Text */}
            {state.models.length < 2 && (
              <p className="text-center text-xs text-gray-600 dark:text-gray-400">
                Add at least 2 models to start comparing
              </p>
            )}
          </CardContent>
        )}
      </Card>
    </div>
  )
}
