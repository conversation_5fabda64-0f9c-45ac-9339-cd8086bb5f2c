import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  FlatList,
  Dimensions,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import { EVModel } from "../../types";
import { evModelsApi } from "../../lib/supabase";
import { useComparison } from "../../contexts/ComparisonContext";
import { EVDetailsOverview } from "../../components/ev-models/details/EVDetailsOverview";
import { EVDetailsSpecs } from "../../components/ev-models/details/EVDetailsSpecs";
import { EVDetailsCostAnalysis } from "../../components/ev-models/details/EVDetailsCostAnalysis";
import { EVDetailsRealWorld } from "../../components/ev-models/details/EVDetailsRealWorld";
import { Colors } from "@/constants/Colors";

const { width: screenWidth } = Dimensions.get("window");

type TabType = "overview" | "specs" | "costs" | "real-world";

interface Tab {
  id: TabType;
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
}

const tabs: Tab[] = [
  { id: "overview", label: "Overview", icon: "information-circle" },
  { id: "specs", label: "Specs", icon: "list" },
  { id: "costs", label: "Costs", icon: "calculator" },
  { id: "real-world", label: "Real-World", icon: "speedometer" },
];

export default function EVModelDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [model, setModel] = useState<EVModel | null>(null);
  const [similarModels, setSimilarModels] = useState<EVModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>("overview");
  const [imageIndex, setImageIndex] = useState(0);

  const { addToComparison, removeFromComparison, isInComparison } =
    useComparison();

  useEffect(() => {
    if (id) {
      fetchModelDetails();
    }
  }, [id]);

  const fetchModelDetails = async () => {
    if (!id) return;
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await evModelsApi.getById(id);

      if (fetchError) {
        throw fetchError;
      }

      if (data) {
        setModel(data as EVModel);
        fetchSimilarModels(data as EVModel);
      }
    } catch (err) {
      console.error("Error fetching model details:", err);
      setError(
        err instanceof Error ? err.message : "Failed to fetch model details"
      );
    } finally {
      setLoading(false);
    }
  };

  const fetchSimilarModels = async (currentModel: EVModel) => {
    try {
      const { data } = await evModelsApi.getAll({
        bodyType: currentModel.body_type ?? undefined,
        priceMin: Math.max(
          0,
          (currentModel.price_msrp || 0) - 1000000
        ).toString(), // $10k range
        priceMax: ((currentModel.price_msrp || 0) + 1000000).toString(),
      });

      if (data) {
        const similar = data
          .filter((m: EVModel) => m.id !== currentModel.id)
          .slice(0, 4);
        setSimilarModels(similar as EVModel[]);
      }
    } catch (err) {
      console.error("Error fetching similar models:", err);
    }
  };

  const getImages = () => {
    if (model?.images && model.images.length > 0) {
      return model.images.map((url: string) => ({ uri: url }));
    }
    return [];
  };

  const getCurrentImage = () => {
    const images = getImages();
    if (images.length > 0) {
      return images[imageIndex] || images[0];
    }
    return null;
  };

  const handleCompareToggle = () => {
    if (!model) return;

    if (isInComparison(model.id)) {
      removeFromComparison(model.id);
    } else {
      const success = addToComparison(model);
      if (!success) {
        Alert.alert(
          "Comparison Limit",
          "You can compare up to 4 vehicles at once. Remove one to add another.",
          [{ text: "OK" }]
        );
      }
    }
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#4f46e5" />
        <Text style={styles.loadingText}>Loading vehicle details...</Text>
      </View>
    );
  }

  if (error || !model) {
    return (
      <View style={styles.centerContainer}>
        <Ionicons name="alert-circle" size={48} color="#ef4444" />
        <Text style={styles.errorTitle}>Something went wrong</Text>
        <Text style={styles.errorText}>{error || "Vehicle not found"}</Text>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case "overview":
        return (
          <EVDetailsOverview model={model} similarModels={similarModels} />
        );
      case "specs":
        return <EVDetailsSpecs model={model} />;
      case "costs":
        return <EVDetailsCostAnalysis model={model} />;
      case "real-world":
        return <EVDetailsRealWorld model={model} />;
      default:
        return (
          <EVDetailsOverview model={model} similarModels={similarModels} />
        );
    }
  };

  const images = getImages();

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <TouchableOpacity onPress={() => router.back()}>
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>{model.model}</Text>
            <View style={{ width: 24 }} />
          </View>
          <View style={styles.imageContainer}>
            {images.length > 0 ? (
              <FlatList
                data={images}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                onMomentumScrollEnd={(event) => {
                  const newIndex = Math.round(
                    event.nativeEvent.contentOffset.x / screenWidth
                  );
                  setImageIndex(newIndex);
                }}
                renderItem={({ item }: { item: { uri: string } }) => (
                  <Image
                    source={item}
                    style={{ width: screenWidth, height: 280 }}
                    resizeMode="cover"
                  />
                )}
                keyExtractor={(_, index: number) => index.toString()}
              />
            ) : (
              <View style={styles.imagePlaceholder}>
                <Ionicons name="car" size={80} color="#9ca3af" />
              </View>
            )}

            {images.length > 1 && (
              <View style={styles.imageIndicatorContainer}>
                {images.map((_, index: number) => (
                  <View
                    key={index}
                    style={[
                      styles.imageIndicator,
                      index === imageIndex
                        ? styles.imageIndicatorActive
                        : styles.imageIndicatorInactive,
                    ]}
                  />
                ))}
              </View>
            )}

            <View style={styles.badgeContainer}>
              {model.is_featured && (
                <View style={[styles.badge, styles.featuredBadge]}>
                  <Text style={styles.badgeText}>Featured</Text>
                </View>
              )}
              {model.editor_choice && (
                <View style={[styles.badge, styles.editorChoiceBadge]}>
                  <Text style={styles.badgeText}>Editor&apos;s Choice</Text>
                </View>
              )}
            </View>
          </View>

          <View style={styles.tabsContainer}>
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                onPress={() => setActiveTab(tab.id)}
                style={[styles.tab, activeTab === tab.id && styles.activeTab]}
              >
                <Ionicons
                  name={tab.icon}
                  size={20}
                  color={activeTab === tab.id ? "#4f46e5" : "#6b7280"}
                />
                <Text
                  style={[
                    styles.tabText,
                    activeTab === tab.id
                      ? styles.activeTabText
                      : styles.inactiveTabText,
                  ]}
                >
                  {tab.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          <View style={styles.tabContent}>{renderTabContent()}</View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: { flex: 1, backgroundColor: Colors.light.tint },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    paddingTop: 24,
    backgroundColor: Colors.light.tint,
  },
  headerTitle: { fontSize: 18, fontWeight: "600", color: "white" },
  container: {
    flex: 1,
    backgroundColor: "#f9fafb",
  },
  centerContainer: {
    flex: 1,
    backgroundColor: "white",
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#4b5563",
    marginTop: 8,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    color: "#4b5563",
    textAlign: "center",
    paddingHorizontal: 32,
    marginBottom: 24,
  },
  backButton: {
    backgroundColor: "#4f46e5",
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  backButtonText: {
    color: "white",
    fontWeight: "600",
  },
  imageContainer: {
    position: "relative",
  },
  imagePlaceholder: {
    width: "100%",
    height: 280,
    backgroundColor: "#e5e7eb",
    alignItems: "center",
    justifyContent: "center",
  },
  imageIndicatorContainer: {
    position: "absolute",
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "center",
    gap: 8,
  },
  imageIndicator: {
    height: 8,
    width: 8,
    borderRadius: 4,
  },
  imageIndicatorActive: {
    backgroundColor: "white",
  },
  imageIndicatorInactive: {
    backgroundColor: "rgba(255, 255, 255, 0.5)",
  },
  badgeContainer: {
    position: "absolute",
    top: 16,
    left: 16,
    flexDirection: "row",
    gap: 8,
  },
  badge: {
    borderRadius: 9999,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  featuredBadge: {
    backgroundColor: "#4f46e5",
  },
  editorChoiceBadge: {
    backgroundColor: "#2563eb",
  },
  badgeText: {
    color: "white",
    fontSize: 12,
    fontWeight: "500",
  },
  tabsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    backgroundColor: "white",
    paddingTop: 8,
  },
  tab: {
    alignItems: "center",
    padding: 12,
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeTab: {
    borderBottomColor: "#4f46e5",
  },
  tabText: {
    fontSize: 12,
    marginTop: 4,
  },
  activeTabText: {
    color: "#4f46e5",
    fontWeight: "600",
  },
  inactiveTabText: {
    color: "#6b7280",
  },
  tabContent: {
    backgroundColor: "#f9fafb",
  },
});
