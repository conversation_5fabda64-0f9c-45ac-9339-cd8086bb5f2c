'use client'

import { ProtectedRoute } from '@/components/ProtectedRoute'
import { Header } from '@/components/Header'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { MapPin, Zap, Clock, DollarSign, Navigation, Filter } from 'lucide-react'

function ChargingContent() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header variant="dashboard" />

      {/* Main Content */}
      <main className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Charging Stations</h1>
            <p className="text-gray-600 dark:text-gray-300">
              Find and manage charging stations near you
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
            <Button className="bg-electric-600 hover:bg-electric-700">
              <Navigation className="mr-2 h-4 w-4" />
              Find Nearby
            </Button>
          </div>
        </div>

        {/* Current Charging Session */}
        <Card className="mb-8 border-electric-200 bg-electric-50">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-electric-600" />
                <CardTitle className="text-lg">Current Charging Session</CardTitle>
              </div>
              <Badge className="bg-electric-600">Active</Badge>
            </div>
            <CardDescription>Tesla Supercharger - Downtown Mall</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-electric-600">62%</p>
                <p className="text-sm text-gray-600">Battery Level</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">2h 15m</p>
                <p className="text-sm text-gray-600">Time Remaining</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">45 kW</p>
                <p className="text-sm text-gray-600">Charging Speed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">$12.50</p>
                <p className="text-sm text-gray-600">Session Cost</p>
              </div>
            </div>
            <div className="mt-4 flex space-x-2">
              <Button variant="outline" className="flex-1">
                View Details
              </Button>
              <Button variant="destructive" className="flex-1">
                Stop Charging
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Nearby Stations */}
        <div className="mb-6">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">Nearby Stations</h2>
          <div className="grid gap-4">
            {/* Station 1 */}
            <Card className="transition-shadow hover:shadow-md">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="text-lg font-semibold text-gray-900">Tesla Supercharger</h3>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Available
                      </Badge>
                    </div>
                    <div className="mt-1 flex items-center text-sm text-gray-600">
                      <MapPin className="mr-1 h-4 w-4" />
                      <span>123 Main St, Downtown • 0.3 miles</span>
                    </div>
                    <div className="mt-2 grid grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center">
                        <Zap className="mr-1 h-4 w-4 text-electric-600" />
                        <span>150 kW</span>
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="mr-1 h-4 w-4 text-green-600" />
                        <span>$0.28/kWh</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="mr-1 h-4 w-4 text-blue-600" />
                        <span>24/7</span>
                      </div>
                    </div>
                  </div>
                  <div className="ml-4 text-right">
                    <p className="text-sm text-gray-600">8/12 available</p>
                    <Button size="sm" className="mt-2 bg-electric-600 hover:bg-electric-700">
                      Navigate
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Station 2 */}
            <Card className="transition-shadow hover:shadow-md">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="text-lg font-semibold text-gray-900">ChargePoint Network</h3>
                      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                        Busy
                      </Badge>
                    </div>
                    <div className="mt-1 flex items-center text-sm text-gray-600">
                      <MapPin className="mr-1 h-4 w-4" />
                      <span>456 Oak Ave, Midtown • 0.7 miles</span>
                    </div>
                    <div className="mt-2 grid grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center">
                        <Zap className="mr-1 h-4 w-4 text-electric-600" />
                        <span>50 kW</span>
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="mr-1 h-4 w-4 text-green-600" />
                        <span>$0.32/kWh</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="mr-1 h-4 w-4 text-blue-600" />
                        <span>6AM-10PM</span>
                      </div>
                    </div>
                  </div>
                  <div className="ml-4 text-right">
                    <p className="text-sm text-gray-600">2/6 available</p>
                    <Button size="sm" variant="outline" className="mt-2">
                      Navigate
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Station 3 */}
            <Card className="transition-shadow hover:shadow-md">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="text-lg font-semibold text-gray-900">Electrify America</h3>
                      <Badge variant="secondary" className="bg-red-100 text-red-800">
                        Full
                      </Badge>
                    </div>
                    <div className="mt-1 flex items-center text-sm text-gray-600">
                      <MapPin className="mr-1 h-4 w-4" />
                      <span>789 Pine St, Uptown • 1.2 miles</span>
                    </div>
                    <div className="mt-2 grid grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center">
                        <Zap className="mr-1 h-4 w-4 text-electric-600" />
                        <span>350 kW</span>
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="mr-1 h-4 w-4 text-green-600" />
                        <span>$0.35/kWh</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="mr-1 h-4 w-4 text-blue-600" />
                        <span>24/7</span>
                      </div>
                    </div>
                  </div>
                  <div className="ml-4 text-right">
                    <p className="text-sm text-gray-600">0/8 available</p>
                    <Button size="sm" variant="outline" disabled className="mt-2">
                      Full
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Charging History */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Charging Sessions</CardTitle>
            <CardDescription>Your charging activity from the past week</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between border-b pb-4">
                <div>
                  <p className="font-medium">Tesla Supercharger - Downtown</p>
                  <p className="text-sm text-gray-600">Dec 13, 2024 • 2:30 PM</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">$18.50</p>
                  <p className="text-sm text-gray-600">45 kWh</p>
                </div>
              </div>
              <div className="flex items-center justify-between border-b pb-4">
                <div>
                  <p className="font-medium">ChargePoint - Office</p>
                  <p className="text-sm text-gray-600">Dec 12, 2024 • 9:00 AM</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">$12.25</p>
                  <p className="text-sm text-gray-600">28 kWh</p>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Home Charging</p>
                  <p className="text-sm text-gray-600">Dec 11, 2024 • 11:00 PM</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">$8.75</p>
                  <p className="text-sm text-gray-600">35 kWh</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}

export default function ChargingPage() {
  return (
    <ProtectedRoute>
      <ChargingContent />
    </ProtectedRoute>
  )
}
