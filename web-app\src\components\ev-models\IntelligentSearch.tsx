'use client'

import { useState, useEffect, useRef } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import {
  Search,
  X,
  Clock,
  TrendingUp,
  Zap,
  DollarSign,
  MapPin,
  Sparkles,
  Filter,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useDebounce } from '@/hooks/useDebounce'
import { formatPrice, formatRange } from '@/utils/ev-buyer-guide'
import type { EVModel } from '../../../types'

interface SearchSuggestion {
  text: string
  type: 'make' | 'model' | 'feature' | 'recent' | 'trending' | 'semantic'
  description?: string
  icon?: React.ReactNode
}

interface QuickFilter {
  id: string
  label: string
  icon: React.ReactNode
  searchQuery: string
  description: string
}

interface IntelligentSearchProps {
  value: string
  onChange: (value: string) => void
  onQuickFilter?: (filter: any) => void
  placeholder?: string
  className?: string
}

export function IntelligentSearch({
  value,
  onChange,
  onQuickFilter,
  placeholder = 'Search EVs by make, model, features, or describe what you need...',
  className,
}: IntelligentSearchProps) {
  const [isFocused, setIsFocused] = useState(false)
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [trendingSearches, setTrendingSearches] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [showQuickFilters, setShowQuickFilters] = useState(false)

  const inputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const debouncedValue = useDebounce(value, 300)

  // Quick filter suggestions
  const quickFilters: QuickFilter[] = [
    {
      id: 'affordable',
      label: 'Under $40k',
      icon: <DollarSign className="h-4 w-4" />,
      searchQuery: 'affordable electric cars under 40000',
      description: 'Budget-friendly EVs',
    },
    {
      id: 'long-range',
      label: '300+ Miles',
      icon: <MapPin className="h-4 w-4" />,
      searchQuery: 'long range electric vehicles 300 miles',
      description: 'High-range EVs',
    },
    {
      id: 'fast-charging',
      label: 'Fast Charging',
      icon: <Zap className="h-4 w-4" />,
      searchQuery: 'fast charging electric cars',
      description: 'Quick charging capability',
    },
    {
      id: 'family',
      label: 'Family SUVs',
      icon: <MapPin className="h-4 w-4" />,
      searchQuery: 'family electric SUV spacious',
      description: 'Spacious family vehicles',
    },
    {
      id: 'luxury',
      label: 'Luxury EVs',
      icon: <Sparkles className="h-4 w-4" />,
      searchQuery: 'luxury premium electric vehicles',
      description: 'Premium electric cars',
    },
    {
      id: 'commuter',
      label: 'Commuter Cars',
      icon: <TrendingUp className="h-4 w-4" />,
      searchQuery: 'efficient commuter electric sedan',
      description: 'Daily commuting EVs',
    },
  ]

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem('ev-recent-searches')
    if (saved) {
      setRecentSearches(JSON.parse(saved))
    }

    // Load trending searches (could be from API in real implementation)
    setTrendingSearches([
      'Tesla Model 3',
      'Ford Mustang Mach-E',
      'Hyundai IONIQ 5',
      'BMW iX',
      'Rivian R1T',
    ])
  }, [])

  useEffect(() => {
    if (debouncedValue && debouncedValue.length >= 2) {
      fetchIntelligentSuggestions(debouncedValue)
    } else {
      setSuggestions([])
    }
  }, [debouncedValue])

  const fetchIntelligentSuggestions = async (query: string) => {
    setLoading(true)
    try {
      // Enhanced search with semantic understanding
      const response = await fetch(
        `/api/ev-models/intelligent-search?q=${encodeURIComponent(query)}&limit=8`
      )
      if (response.ok) {
        const data = await response.json()

        const intelligentSuggestions: SearchSuggestion[] = []

        // Add semantic suggestions (understanding intent)
        if (data.semanticSuggestions) {
          data.semanticSuggestions.forEach((suggestion: any) => {
            intelligentSuggestions.push({
              text: suggestion.text,
              type: 'semantic',
              description: suggestion.description,
              icon: <Sparkles className="h-4 w-4" />,
            })
          })
        }

        // Add model suggestions
        if (data.modelSuggestions) {
          data.modelSuggestions.forEach((model: any) => {
            intelligentSuggestions.push({
              text: `${model.make} ${model.model}`,
              type: 'model',
              description: `${formatPrice(model.price_msrp)} • ${formatRange(model.range_epa_miles)}`,
              icon: <TrendingUp className="h-4 w-4" />,
            })
          })
        }

        // Add feature-based suggestions
        if (data.featureSuggestions) {
          data.featureSuggestions.forEach((feature: string) => {
            intelligentSuggestions.push({
              text: feature,
              type: 'feature',
              description: 'Feature-based search',
              icon: <Filter className="h-4 w-4" />,
            })
          })
        }

        setSuggestions(intelligentSuggestions)
      }
    } catch (error) {
      console.error('Failed to fetch intelligent suggestions:', error)
      // Fallback to basic suggestions
      fetchBasicSuggestions(query)
    } finally {
      setLoading(false)
    }
  }

  const fetchBasicSuggestions = async (query: string) => {
    try {
      const response = await fetch(`/api/ev-models/search?q=${encodeURIComponent(query)}&limit=5`)
      if (response.ok) {
        const data = await response.json()
        const basicSuggestions: SearchSuggestion[] =
          data.suggestions?.map((text: string) => ({
            text,
            type: 'make' as const,
            icon: <Search className="h-4 w-4" />,
          })) || []
        setSuggestions(basicSuggestions)
      }
    } catch (error) {
      console.error('Failed to fetch basic suggestions:', error)
    }
  }

  const saveRecentSearch = (query: string) => {
    if (!query.trim()) return

    const updated = [query, ...recentSearches.filter((s) => s !== query)].slice(0, 5)
    setRecentSearches(updated)
    localStorage.setItem('ev-recent-searches', JSON.stringify(updated))
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value)
  }

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    onChange(suggestion.text)
    setIsFocused(false)
    saveRecentSearch(suggestion.text)
  }

  const handleQuickFilterClick = (filter: QuickFilter) => {
    onChange(filter.searchQuery)
    saveRecentSearch(filter.searchQuery)
    setShowQuickFilters(false)
    if (onQuickFilter) {
      // Could trigger specific filters based on the quick filter
      onQuickFilter({ type: filter.id, query: filter.searchQuery })
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (value.trim()) {
      saveRecentSearch(value.trim())
      setIsFocused(false)
    }
  }

  const handleClear = () => {
    onChange('')
    inputRef.current?.focus()
  }

  const showSuggestionDropdown =
    isFocused && (suggestions.length > 0 || recentSearches.length > 0 || !value)

  return (
    <div ref={containerRef} className={cn('relative', className)}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <Input
            ref={inputRef}
            type="text"
            value={value}
            onChange={handleInputChange}
            onFocus={() => setIsFocused(true)}
            placeholder={placeholder}
            className="pl-10 pr-20"
          />
          <div className="absolute right-1 top-1/2 flex -translate-y-1/2 gap-1">
            {value && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleClear}
                className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowQuickFilters(!showQuickFilters)}
              className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </form>

      {/* Quick Filters */}
      {showQuickFilters && (
        <Card className="absolute top-full z-50 mt-1 w-full">
          <CardContent className="p-4">
            <h4 className="mb-3 text-sm font-semibold">Quick Filters</h4>
            <div className="grid gap-2 sm:grid-cols-2">
              {quickFilters.map((filter) => (
                <Button
                  key={filter.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickFilterClick(filter)}
                  className="h-auto justify-start p-3"
                >
                  <div className="flex items-center gap-2">
                    {filter.icon}
                    <div className="text-left">
                      <div className="font-medium">{filter.label}</div>
                      <div className="text-xs text-gray-500">{filter.description}</div>
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Suggestions Dropdown */}
      {showSuggestionDropdown && (
        <Card className="absolute top-full z-50 mt-1 max-h-96 w-full overflow-y-auto">
          <CardContent className="p-0">
            {/* Intelligent Suggestions */}
            {suggestions.length > 0 && (
              <div className="p-2">
                <div className="mb-2 px-2 text-xs font-semibold uppercase tracking-wide text-gray-500">
                  Suggestions
                </div>
                {suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full rounded-md p-2 text-left transition-colors hover:bg-gray-100 dark:hover:bg-gray-800"
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-gray-400">{suggestion.icon}</div>
                      <div className="min-w-0 flex-1">
                        <div className="truncate font-medium">{suggestion.text}</div>
                        {suggestion.description && (
                          <div className="truncate text-xs text-gray-500">
                            {suggestion.description}
                          </div>
                        )}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {suggestion.type}
                      </Badge>
                    </div>
                  </button>
                ))}
              </div>
            )}

            {/* Recent Searches */}
            {recentSearches.length > 0 && !value && (
              <div className="border-t p-2">
                <div className="mb-2 px-2 text-xs font-semibold uppercase tracking-wide text-gray-500">
                  Recent Searches
                </div>
                {recentSearches.map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick({ text: search, type: 'recent' })}
                    className="w-full rounded-md p-2 text-left transition-colors hover:bg-gray-100 dark:hover:bg-gray-800"
                  >
                    <div className="flex items-center gap-3">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span className="flex-1 truncate">{search}</span>
                    </div>
                  </button>
                ))}
              </div>
            )}

            {/* Trending Searches */}
            {trendingSearches.length > 0 && !value && (
              <div className="border-t p-2">
                <div className="mb-2 px-2 text-xs font-semibold uppercase tracking-wide text-gray-500">
                  Trending
                </div>
                {trendingSearches.slice(0, 3).map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick({ text: search, type: 'trending' })}
                    className="w-full rounded-md p-2 text-left transition-colors hover:bg-gray-100 dark:hover:bg-gray-800"
                  >
                    <div className="flex items-center gap-3">
                      <TrendingUp className="h-4 w-4 text-gray-400" />
                      <span className="flex-1 truncate">{search}</span>
                    </div>
                  </button>
                ))}
              </div>
            )}

            {loading && <div className="p-4 text-center text-sm text-gray-500">Searching...</div>}
          </CardContent>
        </Card>
      )}

      {/* Click outside to close */}
      {showSuggestionDropdown && (
        <div className="fixed inset-0 z-40" onClick={() => setIsFocused(false)} />
      )}
    </div>
  )
}
