import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/shared/types'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(id)) {
      return NextResponse.json({ error: 'Invalid comparison ID format' }, { status: 400 })
    }

    // Get comparison session
    const { data: comparison, error: comparisonError } = await supabase
      .from('comparison_sessions')
      .select('*')
      .eq('id', id)
      .single()

    if (comparisonError) {
      if (comparisonError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Comparison not found' }, { status: 404 })
      }
      console.error('Error fetching comparison:', comparisonError)
      return NextResponse.json({ error: 'Failed to fetch comparison' }, { status: 500 })
    }

    // Check if user has access (owner or public comparison)
    const authHeader = request.headers.get('authorization')
    let hasAccess = comparison.is_public

    if (authHeader && !hasAccess) {
      const token = authHeader.replace('Bearer ', '')
      const {
        data: { user },
      } = await supabase.auth.getUser(token)
      hasAccess = user?.id === comparison.user_id
    }

    if (!hasAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Get EV models details
    if (!comparison.ev_model_ids || comparison.ev_model_ids.length === 0) {
      return NextResponse.json({
        data: {
          ...comparison,
          ev_models: [],
        },
      })
    }

    const { data: evModels, error: modelsError } = await supabase
      .from('ev_models')
      .select(
        `
        id,
        make,
        model,
        year,
        trim,
        body_type,
        price_msrp,
        price_base,
        battery_capacity_kwh,
        range_epa_miles,
        range_real_world_miles,
        efficiency_mpge,
        charging_speed_dc_kw,
        charging_speed_ac_kw,
        charging_time_10_80_minutes,
        acceleration_0_60_mph,
        top_speed_mph,
        motor_power_hp,
        drivetrain,
        seating_capacity,
        cargo_volume_cubic_ft,
        curb_weight_lbs,
        safety_ratings,
        warranty_info,
        total_cost_ownership,
        pros_cons,
        images,
        is_featured,
        best_value,
        ev_manufacturers!inner(
          name,
          logo_url,
          website_url
        )
      `
      )
      .in('id', comparison.ev_model_ids)

    if (modelsError) {
      console.error('Error fetching EV models:', modelsError)
      return NextResponse.json(
        { error: 'Failed to fetch EV models for comparison' },
        { status: 500 }
      )
    }

    // Sort EV models in the same order as ev_model_ids
    const sortedEvModels = comparison.ev_model_ids
      .map((id: string) => evModels?.find((model) => model.id === id))
      .filter(Boolean)

    // Calculate comparison metrics
    const comparisonMetrics = {
      priceRange: {
        min: Math.min(
          ...sortedEvModels.map((m: any) => m.price_msrp || 0).filter((p: number) => p > 0)
        ),
        max: Math.max(...sortedEvModels.map((m: any) => m.price_msrp || 0)),
      },
      rangeComparison: {
        min: Math.min(
          ...sortedEvModels.map((m: any) => m.range_epa_miles || 0).filter((r: number) => r > 0)
        ),
        max: Math.max(...sortedEvModels.map((m: any) => m.range_epa_miles || 0)),
      },
      chargingSpeed: {
        min: Math.min(
          ...sortedEvModels
            .map((m: any) => m.charging_speed_dc_kw || 0)
            .filter((s: number) => s > 0)
        ),
        max: Math.max(...sortedEvModels.map((m: any) => m.charging_speed_dc_kw || 0)),
      },
      acceleration: {
        fastest: Math.min(
          ...sortedEvModels
            .map((m: any) => m.acceleration_0_60_mph || 999)
            .filter((a: number) => a < 999)
        ),
        slowest: Math.max(...sortedEvModels.map((m: any) => m.acceleration_0_60_mph || 0)),
      },
    }

    return NextResponse.json({
      data: {
        ...comparison,
        ev_models: sortedEvModels,
        metrics: comparisonMetrics,
      },
    })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 })
    }

    const token = authHeader.replace('Bearer ', '')
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token)
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid authentication' }, { status: 401 })
    }

    const { id } = params
    const body = await request.json()
    const { name, user_priorities, comparison_notes, is_public } = body

    // Update comparison session
    const { data: comparison, error } = await supabase
      .from('comparison_sessions')
      .update({
        name,
        user_priorities,
        comparison_notes,
        is_public,
      })
      .eq('id', id)
      .eq('user_id', user.id) // Ensure user owns the comparison
      .select()
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Comparison not found or access denied' },
          { status: 404 }
        )
      }
      console.error('Error updating comparison:', error)
      return NextResponse.json({ error: 'Failed to update comparison' }, { status: 500 })
    }

    return NextResponse.json({
      data: comparison,
      message: 'Comparison updated successfully',
    })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 })
    }

    const token = authHeader.replace('Bearer ', '')
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token)
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid authentication' }, { status: 401 })
    }

    const { id } = params

    // Delete comparison session
    const { error } = await supabase
      .from('comparison_sessions')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id) // Ensure user owns the comparison

    if (error) {
      console.error('Error deleting comparison:', error)
      return NextResponse.json({ error: 'Failed to delete comparison' }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Comparison deleted successfully',
    })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
