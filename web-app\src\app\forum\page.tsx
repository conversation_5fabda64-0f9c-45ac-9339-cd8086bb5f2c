'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { MessageSquare, Users, Pin, Clock, Plus } from 'lucide-react'

interface ForumHomeData {
  categories: any[]
  recentTopics: any[]
  stats: {
    totalTopics: number
    totalPosts: number
    totalUsers: number
  }
}

export default function ForumPage() {
  const { user } = useAuth()
  const [data, setData] = useState<ForumHomeData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchForumData()
  }, [])

  const fetchForumData = async () => {
    try {
      setLoading(true)

      // Fetch categories
      const categoriesResponse = await fetch('/api/forum/categories')
      const categoriesData = await categoriesResponse.json()

      if (!categoriesResponse.ok) {
        throw new Error(categoriesData.error || 'Failed to fetch categories')
      }

      // Fetch recent topics
      const topicsResponse = await fetch('/api/forum/topics?limit=10&sort=newest')
      const topicsData = await topicsResponse.json()

      if (!topicsResponse.ok) {
        throw new Error(topicsData.error || 'Failed to fetch topics')
      }

      setData({
        categories: categoriesData.data || [],
        recentTopics: topicsData.data || [],
        stats: {
          totalTopics: topicsData.pagination?.total || 0,
          totalPosts: 0,
          totalUsers: 0,
        },
      })
    } catch (err) {
      console.error('Error fetching forum data:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            <div className="animate-pulse space-y-6">
              <div className="h-8 w-48 rounded bg-gray-200"></div>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-32 rounded bg-gray-200"></div>
                ))}
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-6">
                <p className="text-red-600">Error: {error}</p>
                <Button onClick={fetchForumData} variant="outline" className="mt-4">
                  Try Again
                </Button>
              </CardContent>
            </Card>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8 flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">GreenMilesEV Community Forum</h1>
              <p className="mt-2 text-gray-600">
                Connect with fellow EV enthusiasts, share experiences, and get answers to your
                questions
              </p>
            </div>
            {user && (
              <Button asChild>
                <Link href="/forum/new-topic">
                  <Plus className="mr-2 h-4 w-4" />
                  New Topic
                </Link>
              </Button>
            )}
          </div>

          {/* Forum Stats */}
          <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-3">
            <Card>
              <CardContent className="flex items-center space-x-3 p-4">
                <MessageSquare className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{data?.stats.totalTopics.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Topics</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="flex items-center space-x-3 p-4">
                <MessageSquare className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">{data?.stats.totalPosts.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Posts</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="flex items-center space-x-3 p-4">
                <Users className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-2xl font-bold">{data?.stats.totalUsers.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Members</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* Categories */}
            <div className="lg:col-span-2">
              <h2 className="mb-6 text-2xl font-semibold">Forum Categories</h2>
              <div className="space-y-4">
                {data?.categories.map((category) => (
                  <Card key={category.id} className="transition-shadow hover:shadow-md">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex flex-1 items-start space-x-4">
                          {category.icon && (
                            <div
                              className="rounded-lg p-2 text-2xl"
                              style={{
                                backgroundColor: category.color ? `${category.color}20` : '#f3f4f6',
                                color: category.color || '#6b7280',
                              }}
                            >
                              {category.icon}
                            </div>
                          )}
                          <div className="flex-1">
                            <Link
                              href={`/forum/category/${category.slug}`}
                              className="text-lg font-semibold transition-colors hover:text-blue-600"
                            >
                              {category.name}
                            </Link>
                            {category.description && (
                              <p className="mt-1 text-sm text-gray-600">{category.description}</p>
                            )}

                            {/* Subcategories */}
                            {category.subcategories && category.subcategories.length > 0 && (
                              <div className="mt-3 flex flex-wrap gap-2">
                                {category.subcategories.map((sub: any) => (
                                  <Link
                                    key={sub.id}
                                    href={`/forum/category/${sub.slug}`}
                                    className="rounded-full bg-gray-100 px-2 py-1 text-xs transition-colors hover:bg-gray-200"
                                  >
                                    {sub.name}
                                  </Link>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="ml-4 text-right text-sm text-gray-600">
                          <div className="flex items-center space-x-4">
                            <div className="text-center">
                              <p className="font-semibold">{category.topic_count || 0}</p>
                              <p className="text-xs">Topics</p>
                            </div>
                            <div className="text-center">
                              <p className="font-semibold">{category.post_count || 0}</p>
                              <p className="text-xs">Posts</p>
                            </div>
                          </div>
                          {category.last_activity_at && (
                            <p className="mt-2 text-xs">
                              Last activity:{' '}
                              {new Date(category.last_activity_at).toLocaleDateString()}
                            </p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Recent Topics Sidebar */}
            <div>
              <h2 className="mb-6 text-2xl font-semibold">Recent Topics</h2>
              <div className="space-y-4">
                {data?.recentTopics.map((topic) => (
                  <Card key={topic.id} className="transition-shadow hover:shadow-md">
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-3">
                        {topic.author?.avatar_url ? (
                          <img
                            src={topic.author.avatar_url}
                            alt={topic.author.full_name || 'User'}
                            className="h-8 w-8 rounded-full"
                          />
                        ) : (
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-300">
                            {topic.author?.full_name?.[0]?.toUpperCase() || 'U'}
                          </div>
                        )}

                        <div className="min-w-0 flex-1">
                          <div className="mb-1 flex items-center space-x-2">
                            {topic.is_pinned && <Pin className="h-3 w-3 text-orange-500" />}
                            <Link
                              href={`/forum/topic/${topic.slug}`}
                              className="truncate text-sm font-medium transition-colors hover:text-blue-600"
                              title={topic.title}
                            >
                              {topic.title}
                            </Link>
                          </div>

                          <div className="flex items-center space-x-2 text-xs text-gray-600">
                            <span>by {topic.author?.full_name || 'Anonymous'}</span>
                            <span>•</span>
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>{new Date(topic.created_at).toLocaleDateString()}</span>
                            </div>
                          </div>

                          <div className="mt-2 flex items-center justify-between">
                            <Badge
                              variant="secondary"
                              className="text-xs"
                              style={{
                                backgroundColor: topic.category?.color
                                  ? `${topic.category.color}20`
                                  : '#f3f4f6',
                                color: topic.category?.color || '#6b7280',
                              }}
                            >
                              {topic.category?.name || 'General'}
                            </Badge>

                            <div className="flex items-center space-x-2 text-xs text-gray-600">
                              <span>{topic.post_count || 0} replies</span>
                              <span>•</span>
                              <span>{topic.view_count || 0} views</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {(!data?.recentTopics || data.recentTopics.length === 0) && (
                <Card>
                  <CardContent className="p-6 text-center">
                    <MessageSquare className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                    <p className="text-gray-600">No recent topics found.</p>
                    {user && (
                      <Button asChild className="mt-4">
                        <Link href="/forum/new-topic">Start the first discussion!</Link>
                      </Button>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
