import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  StyleSheet,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useComparison } from "../contexts/ComparisonContext";
import { ComparisonOverview } from "../components/comparison/ComparisonOverview";
import { ComparisonSpecs } from "../components/comparison/ComparisonSpecs";
import ComparisonCosts from "../components/comparison/ComparisonCosts";
import { DecisionMatrix } from "../components/comparison/DecisionMatrix";
import { ComparisonRecommendations } from "../components/comparison/ComparisonRecommendations";
import { Colors } from "@/constants/Colors";

type TabType = "overview" | "specs" | "costs" | "matrix" | "recommendations";

interface Tab {
  id: TabType;
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
}

const tabs: Tab[] = [
  { id: "overview", label: "Overview", icon: "eye-outline" },
  { id: "specs", label: "Specs", icon: "list-outline" },
  { id: "costs", label: "Costs", icon: "calculator-outline" },
  { id: "matrix", label: "Decision", icon: "analytics-outline" },
  { id: "recommendations", label: "Insights", icon: "bulb-outline" },
];

export default function ComparisonScreen() {
  const { comparisonList, clearComparison } = useComparison();
  const [activeTab, setActiveTab] = useState<TabType>("overview");

  const handleShare = () => {
    Alert.alert("Share Comparison", "Sharing functionality coming soon!");
  };

  const handleExport = () => {
    Alert.alert("Export Comparison", "Export functionality coming soon!");
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "overview":
        return <ComparisonOverview models={comparisonList} />;
      case "specs":
        return <ComparisonSpecs models={comparisonList} />;
      case "costs":
        return <ComparisonCosts models={comparisonList} />;
      case "matrix":
        return <DecisionMatrix models={comparisonList} />;
      case "recommendations":
        return <ComparisonRecommendations models={comparisonList} />;
      default:
        return <ComparisonOverview models={comparisonList} />;
    }
  };

  if (comparisonList.length === 0) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.emptyContainer}>
          <View style={styles.header}>
            <TouchableOpacity onPress={() => router.back()}>
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Compare Vehicles</Text>
            <View style={{ width: 24 }} />
          </View>

          <View style={styles.emptyContent}>
            <View style={styles.emptyIconContainer}>
              <Ionicons name="analytics-outline" size={48} color="#6b7280" />
            </View>
            <Text style={styles.emptyTitle}>Start Your Comparison</Text>
            <Text style={styles.emptySubtitle}>
              Add vehicles from the browse screen to compare them side-by-side.
            </Text>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                onPress={() => router.push("/(tabs)/browse")}
                style={styles.browseButton}
              >
                <Text style={styles.browseButtonText}>Browse Vehicles</Text>
              </TouchableOpacity>

              <View style={styles.proTipContainer}>
                <View style={styles.proTipHeader}>
                  <Ionicons
                    name="information-circle"
                    size={20}
                    color="#3b82f6"
                  />
                  <Text style={styles.proTipTitle}>Pro Tip</Text>
                </View>
                <Text style={styles.proTipText}>
                  You can compare up to 4 vehicles at once.
                </Text>
              </View>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            Compare {comparisonList.length} Vehicle
            {comparisonList.length > 1 ? "s" : ""}
          </Text>
          <View style={styles.headerActions}>
            <TouchableOpacity onPress={handleShare}>
              <Ionicons name="share-outline" size={24} color="white" />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleExport}>
              <Ionicons name="download-outline" size={24} color="white" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.tabBar}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tabScrollContainer}
          >
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                onPress={() => setActiveTab(tab.id)}
                style={[
                  styles.tabItem,
                  activeTab === tab.id && styles.activeTabItem,
                ]}
              >
                <Ionicons
                  name={tab.icon}
                  size={16}
                  color={activeTab === tab.id ? "white" : "#6b7280"}
                />
                <Text
                  style={[
                    styles.tabLabel,
                    activeTab === tab.id && styles.activeTabLabel,
                  ]}
                >
                  {tab.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <ScrollView style={styles.contentContainer}>
          {renderTabContent()}
        </ScrollView>

        <View style={styles.fabContainer}>
          <View style={styles.fabContent}>
            <TouchableOpacity
              onPress={() => router.push("/(tabs)/browse")}
              style={styles.addButton}
            >
              <Text style={styles.addButtonText}>Add More</Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={clearComparison}
              style={styles.clearButton}
            >
              <Ionicons name="trash-outline" size={20} color="#374151" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: { flex: 1, backgroundColor: Colors.light.tint },
  container: { flex: 1, backgroundColor: "#F3F4F6" },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    paddingTop: 24,
    backgroundColor: Colors.light.tint,
  },
  headerTitle: { fontSize: 18, fontWeight: "600", color: "white" },
  headerActions: { flexDirection: "row", alignItems: "center", gap: 12 },
  emptyContainer: { flex: 1, backgroundColor: "white" },
  emptyContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 32,
  },
  emptyIconContainer: {
    backgroundColor: "#F3F4F6",
    borderRadius: 999,
    padding: 24,
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#111827",
    marginBottom: 12,
    textAlign: "center",
  },
  emptySubtitle: {
    color: "#4B5563",
    textAlign: "center",
    marginBottom: 32,
    lineHeight: 22,
  },
  buttonContainer: { width: "100%", gap: 12 },
  browseButton: {
    backgroundColor: "#4F46E5",
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
  },
  browseButtonText: { color: "white", fontWeight: "600", fontSize: 18 },
  proTipContainer: {
    backgroundColor: "#EFF6FF",
    borderRadius: 12,
    padding: 16,
  },
  proTipHeader: { flexDirection: "row", alignItems: "center", marginBottom: 8 },
  proTipTitle: { fontWeight: "500", color: "#1E3A8A", marginLeft: 8 },
  proTipText: { color: "#1E40AF", fontSize: 14 },
  tabBar: {
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderColor: "#E5E7EB",
    paddingBottom: 16,
    paddingTop: 16,
  },
  tabScrollContainer: { paddingHorizontal: 16 },
  tabItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: "#F3F4F6",
  },
  activeTabItem: { backgroundColor: Colors.light.tint },
  tabLabel: { marginLeft: 8, fontWeight: "500", color: "#374151" },
  activeTabLabel: { color: "white" },
  contentContainer: { flex: 1, marginBottom: 64 },
  fabContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "white",
    borderTopWidth: 1,
    borderColor: "#E5E7EB",
    padding: 16,
  },
  fabContent: { flexDirection: "row", gap: 12 },
  addButton: {
    flex: 1,
    backgroundColor: Colors.light.tint,
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: "center",
  },
  addButtonText: { color: "white", fontWeight: "600" },
  clearButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: "#F3F4F6",
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
});
