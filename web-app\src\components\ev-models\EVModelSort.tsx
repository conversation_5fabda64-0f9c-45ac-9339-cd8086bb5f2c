'use client'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react'
import { EV_SORT_OPTIONS } from '../../../types'
import type { SortOptions, EVSortOption } from '../../../types'

interface EVModelSortProps {
  value: SortOptions
  onChange: (sort: SortOptions) => void
  className?: string
}

const SORT_LABELS: Record<EVSortOption, string> = {
  popularity_score: 'Popularity',
  price_msrp: 'Price',
  range_epa_miles: 'Range',
  efficiency_mpge: 'Efficiency',
  acceleration_0_60_mph: 'Acceleration',
  year: 'Year',
  make: 'Make',
  model: 'Model',
}

export function EVModelSort({ value, onChange, className }: EVModelSortProps) {
  const handleFieldChange = (field: string) => {
    onChange({
      field: field as EVSortOption,
      order: value.order,
    })
  }

  const handleOrderToggle = () => {
    onChange({
      field: value.field,
      order: value.order === 'asc' ? 'desc' : 'asc',
    })
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Select value={value.field} onValueChange={handleFieldChange}>
        <SelectTrigger className="w-40">
          <SelectValue placeholder="Sort by" />
        </SelectTrigger>
        <SelectContent>
          {EV_SORT_OPTIONS.map((option) => (
            <SelectItem key={option} value={option}>
              {SORT_LABELS[option]}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Button
        variant="outline"
        size="icon"
        onClick={handleOrderToggle}
        className="shrink-0"
        title={`Sort ${value.order === 'asc' ? 'ascending' : 'descending'}`}
      >
        {value.order === 'asc' ? (
          <ArrowUp className="h-4 w-4" />
        ) : (
          <ArrowDown className="h-4 w-4" />
        )}
      </Button>
    </div>
  )
}
