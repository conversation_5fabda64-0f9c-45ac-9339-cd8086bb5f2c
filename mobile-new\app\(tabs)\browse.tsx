import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  RefreshControl,
  Alert,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useEVModels } from "@/hooks/useEVModels";
import { useComparison } from "@/contexts/ComparisonContext";
import { EVModelCard } from "@/components/EVModelCard";
import { SearchFilters } from "@/components/SearchFilters";
import { QuickFilters } from "@/components/QuickFilters";
import { EVModel } from "@/types/index";
import { Colors } from "@/constants/Colors";

export default function BrowseScreen() {
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [refreshing, setRefreshing] = useState(false);

  const {
    evModels,
    loading,
    error,
    filters,
    updateFilters,
    clearFilters,
    hasActiveFilters,
    refetch,
  } = useEVModels();

  const {
    comparisonCount,
    addToComparison,
    removeFromComparison,
    isInComparison,
  } = useComparison();

  useEffect(() => {
    refetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    updateFilters({ search: query });
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleModelPress = (model: EVModel) => {
    router.push({
      pathname: "/ev-models/[id]",
      params: { id: model.id },
    });
  };

  const handleCompareToggle = (model: EVModel) => {
    if (isInComparison(model.id)) {
      removeFromComparison(model.id);
    } else {
      const success = addToComparison(model);
      if (!success) {
        Alert.alert(
          "Comparison Limit",
          "You can compare up to 4 vehicles at once. Remove one to add another.",
          [{ text: "OK" }]
        );
      }
    }
  };

  const handleViewComparison = () => {
    router.push("/comparison");
  };

  const renderEVModel = ({ item }: { item: EVModel }) => (
    <EVModelCard
      model={item}
      viewMode={viewMode}
      onPress={() => handleModelPress(item)}
      onCompareToggle={() => handleCompareToggle(item)}
      isInComparison={isInComparison(item.id)}
    />
  );

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.heroSection}>
        <Text style={styles.heroTitle}>Find Your Perfect EV</Text>
        <Text style={styles.heroSubtitle}>
          Discover and compare electric vehicles
        </Text>
      </View>

      <View style={styles.searchBarContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color="#6b7280" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search by make, model, or features..."
            value={searchQuery}
            onChangeText={handleSearch}
            placeholderTextColor="#6b7280"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => handleSearch("")}>
              <Ionicons name="close-circle" size={20} color="#6b7280" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <QuickFilters
        onFilterSelect={(filter) => updateFilters(filter)}
        activeFilters={filters}
      />

      <View style={styles.filterControlsContainer}>
        <View style={styles.filterControlsLeft}>
          <TouchableOpacity
            onPress={() => setShowFilters(!showFilters)}
            style={styles.filterButton}
          >
            <Ionicons name="options" size={18} color="#374151" />
            <Text style={styles.filterButtonText}>Filters</Text>
            {hasActiveFilters && <View style={styles.activeFilterDot} />}
          </TouchableOpacity>

          {hasActiveFilters && (
            <TouchableOpacity onPress={clearFilters}>
              <Text style={styles.clearFiltersText}>Clear All</Text>
            </TouchableOpacity>
          )}
        </View>

        <View>
          <TouchableOpacity
            onPress={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
            style={styles.viewModeButton}
          >
            <Ionicons
              name={viewMode === "grid" ? "list" : "grid"}
              size={20}
              color="#374151"
            />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.resultsCountContainer}>
        <Text style={styles.resultsCountText}>
          {evModels?.length || 0} vehicles found
        </Text>
      </View>
    </View>
  );

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={48} color="#ef4444" />
        <Text style={styles.errorTitle}>Something went wrong</Text>
        <Text style={styles.errorMessage}>
          We couldn&apos;t load the EV models. Please try again.
        </Text>
        <TouchableOpacity onPress={() => refetch()} style={styles.errorButton}>
          <Text style={styles.errorButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {showFilters && (
        <SearchFilters
          filters={filters}
          onFiltersChange={updateFilters}
          onClose={() => setShowFilters(false)}
        />
      )}

      <FlatList
        data={evModels || []}
        renderItem={renderEVModel}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        numColumns={viewMode === "grid" ? 2 : 1}
        key={viewMode}
        contentContainerStyle={{
          paddingBottom: comparisonCount > 0 ? 100 : 20,
        }}
        columnWrapperStyle={
          viewMode === "grid" ? { paddingHorizontal: 16, gap: 12 } : undefined
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.light.tint]}
            tintColor={Colors.light.tint}
          />
        }
        showsVerticalScrollIndicator={false}
      />

      {comparisonCount > 0 && (
        <View style={styles.comparisonBarContainer}>
          <TouchableOpacity
            onPress={handleViewComparison}
            style={styles.comparisonBar}
          >
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <Ionicons name="analytics" size={24} color="white" />
              <Text style={styles.comparisonBarText}>
                Compare {comparisonCount} Vehicle
                {comparisonCount > 1 ? "s" : ""}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="white" />
          </TouchableOpacity>
        </View>
      )}

      {loading && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.light.tint} />
            <Text style={styles.loadingText}>Loading vehicles...</Text>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f9fafb",
  },
  headerContainer: {
    backgroundColor: "white",
  },
  heroSection: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 16,
    paddingTop: 70,
    paddingBottom: 24,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "white",
    marginBottom: 8,
  },
  heroSubtitle: {
    color: "#dcfce7",
    fontSize: 16,
  },
  searchBarContainer: {
    padding: 16,
    backgroundColor: "white",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f3f4f6",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
  },
  filterControlsContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  filterControlsLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f3f4f6",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 12,
  },
  filterButtonText: {
    marginLeft: 8,
    color: "#374151",
    fontWeight: "500",
  },
  activeFilterDot: {
    marginLeft: 8,
    backgroundColor: Colors.light.tint,
    borderRadius: 4,
    width: 8,
    height: 8,
  },
  clearFiltersText: {
    color: Colors.light.tint,
    fontSize: 14,
    fontWeight: "500",
  },
  viewModeButton: {
    padding: 8,
  },
  resultsCountContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "#f9fafb",
  },
  resultsCountText: {
    fontSize: 14,
    color: "#4b5563",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "white",
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1f2937",
    marginTop: 16,
    marginBottom: 8,
  },
  errorMessage: {
    color: "#4b5563",
    textAlign: "center",
    paddingHorizontal: 32,
    marginBottom: 24,
  },
  errorButton: {
    backgroundColor: Colors.light.tint,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  errorButtonText: {
    color: "white",
    fontWeight: "600",
  },
  comparisonBarContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "white",
    borderTopWidth: 1,
    borderTopColor: "#e5e7eb",
    padding: 16,
  },
  comparisonBar: {
    backgroundColor: Colors.light.tint,
    borderRadius: 12,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  comparisonBarText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 12,
  },
  loadingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  loadingContainer: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 24,
    alignItems: "center",
  },
  loadingText: {
    color: "#4b5563",
    marginTop: 8,
  },
});
