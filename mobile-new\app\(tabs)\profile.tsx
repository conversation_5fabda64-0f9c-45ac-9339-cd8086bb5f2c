import React from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useAuth } from "@/contexts/AuthContext";
import { Colors } from "@/constants/Colors";

const StatCard = ({ value, label }: { value: string; label: string }) => (
  <View style={styles.statItem}>
    <Text style={styles.statValue}>{value}</Text>
    <Text style={styles.statLabel}>{label}</Text>
  </View>
);

const SettingsRow = ({
  icon,
  title,
  subtitle,
  onPress,
}: {
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  subtitle: string;
  onPress?: () => void;
}) => (
  <TouchableOpacity style={styles.settingsRow} onPress={onPress}>
    <View style={styles.settingsIconContainer}>
      <Ionicons name={icon} size={24} color="#4F46E5" />
    </View>
    <View style={styles.settingsTextContainer}>
      <Text style={styles.settingsTitle}>{title}</Text>
      <Text style={styles.settingsSubtitle}>{subtitle}</Text>
    </View>
    <Ionicons name="chevron-forward" size={20} color="#6B7280" />
  </TouchableOpacity>
);

export default function ProfileScreen() {
  const { user, signOut } = useAuth();

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <View style={styles.profileHeader}>
            <View style={styles.avatar}>
              <Ionicons name="person-outline" size={32} color="#4F46E5" />
            </View>
            <Text style={styles.name}>{user?.email}</Text>
            <Text style={styles.email}>{user?.id}</Text>
          </View>

          <TouchableOpacity style={styles.editButton}>
            <Text style={styles.editButtonText}>Edit Profile</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Your Impact</Text>
          <View style={styles.statsContainer}>
            <StatCard value="2,847" label="Miles Driven" />
            <StatCard value="342" label="lbs CO₂ Saved" />
            <StatCard value="$267" label="Money Saved" />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Settings</Text>
          <View style={styles.settingsContainer}>
            <SettingsRow
              icon="notifications-outline"
              title="Notifications"
              subtitle="Manage alerts"
            />
            <SettingsRow
              icon="shield-checkmark-outline"
              title="Privacy & Security"
              subtitle="Control your data"
            />
            <SettingsRow
              icon="card-outline"
              title="Payment Methods"
              subtitle="Manage charging payments"
            />
            <SettingsRow
              icon="options-outline"
              title="App Preferences"
              subtitle="Units, theme, and more"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          <View style={styles.settingsContainer}>
            <SettingsRow
              icon="help-circle-outline"
              title="Help Center"
              subtitle="FAQs and guides"
            />
            <SettingsRow
              icon="chatbubble-ellipses-outline"
              title="Contact Support"
              subtitle="Get help from our team"
            />
            <SettingsRow
              icon="information-circle-outline"
              title="About"
              subtitle="App version and info"
            />
          </View>
        </View>

        <View style={styles.signOutContainer}>
          <TouchableOpacity style={styles.signOutButton} onPress={signOut}>
            <Ionicons name="log-out-outline" size={20} color="white" />
            <Text style={styles.signOutText}>Sign Out</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: { flex: 1, backgroundColor: "#F3F4F6" },
  container: { flex: 1 },
  header: {
    backgroundColor: "white",
    margin: 16,
    borderRadius: 12,
    padding: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  profileHeader: { alignItems: "center", marginBottom: 16 },
  avatar: {
    width: 80,
    height: 80,
    backgroundColor: "#E0E7FF",
    borderRadius: 40,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 12,
  },
  name: { fontSize: 22, fontWeight: "bold", color: "#111827" },
  email: { fontSize: 16, color: "#6B7280" },
  editButton: {
    backgroundColor: Colors.light.tint,
    borderRadius: 8,
    padding: 12,
    alignItems: "center",
  },
  editButtonText: { color: "white", fontWeight: "600" },
  section: { marginHorizontal: 16, marginTop: 24 },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
    marginBottom: 12,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  statItem: { alignItems: "center" },
  statValue: { fontSize: 24, fontWeight: "bold", color: "#4F46E5" },
  statLabel: { fontSize: 14, color: "#6B7280" },
  settingsContainer: {
    backgroundColor: "white",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  settingsRow: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F3F4F6",
  },
  settingsIconContainer: { marginRight: 12 },
  settingsTextContainer: { flex: 1 },
  settingsTitle: { fontWeight: "500", fontSize: 16, color: "#111827" },
  settingsSubtitle: { fontSize: 14, color: "#6B7280" },
  signOutContainer: { margin: 16, marginTop: 24 },
  signOutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#DC2626",
    borderRadius: 12,
    padding: 16,
  },
  signOutText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 8,
    fontSize: 16,
  },
});
