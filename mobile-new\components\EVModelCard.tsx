import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { EVModel } from "../types";

interface EVModelCardProps {
  model: EVModel;
  viewMode: "grid" | "list";
  onPress: () => void;
  onCompareToggle: () => void;
  isInComparison: boolean;
}

const { width: screenWidth } = Dimensions.get("window");
const cardWidth = (screenWidth - 48) / 2; // Account for padding and gap

export function EVModelCard({
  model,
  viewMode,
  onPress,
  onCompareToggle,
  isInComparison,
}: EVModelCardProps) {
  const formatPrice = (priceInCents: number | null) => {
    if (!priceInCents) return "Price TBA";
    return `$${(priceInCents / 100).toLocaleString()}`;
  };

  const formatRange = (range: number | null) => {
    if (!range) return "N/A";
    return `${range} mi`;
  };

  const getImageSource = () => {
    if (model.images && model.images.length > 0) {
      return { uri: model.images[0] };
    }
    return null;
  };

  const imageSource = getImageSource();

  const getBadges = () => {
    const badges = [];
    if (model.is_featured)
      badges.push({ text: "Featured", color: styles.featuredBadge });
    if (model.editor_choice)
      badges.push({ text: "Editor's Choice", color: styles.editorChoiceBadge });
    if (model.best_value)
      badges.push({ text: "Best Value", color: styles.bestValueBadge });
    return badges;
  };

  const getEfficiencyRating = () => {
    if (!model.efficiency_mpge) return null;
    if (model.efficiency_mpge >= 130)
      return { rating: "Excellent", color: styles.excellentText };
    if (model.efficiency_mpge >= 110)
      return { rating: "Very Good", color: styles.veryGoodText };
    if (model.efficiency_mpge >= 90)
      return { rating: "Good", color: styles.goodText };
    return { rating: "Average", color: styles.averageText };
  };

  const badges = getBadges();
  const efficiencyRating = getEfficiencyRating();

  if (viewMode === "list") {
    return (
      <TouchableOpacity onPress={onPress} style={styles.listContainer}>
        <View style={styles.listContent}>
          {/* Image */}
          <View style={styles.listImageContainer}>
            {imageSource ? (
              <Image
                source={imageSource}
                style={styles.listImage}
                resizeMode="cover"
              />
            ) : (
              <View style={styles.listImagePlaceholder}>
                <Ionicons name="car" size={24} color="#9ca3af" />
              </View>
            )}
            {badges.length > 0 && (
              <View style={styles.listBadgeContainer}>
                <View style={[styles.badge, badges[0].color]}>
                  <Text style={styles.badgeText}>{badges[0].text}</Text>
                </View>
              </View>
            )}
          </View>

          {/* Content */}
          <View style={styles.listTextContainer}>
            <View style={styles.listHeader}>
              <View style={{ flex: 1 }}>
                <Text style={styles.modelName} numberOfLines={1}>
                  {model.make} {model.model}
                </Text>
                {model.trim && (
                  <Text style={styles.modelTrim} numberOfLines={1}>
                    {model.trim}
                  </Text>
                )}
                <Text style={styles.modelPrice}>
                  {formatPrice(model.price_msrp)}
                </Text>
              </View>

              {/* Compare Button */}
              <TouchableOpacity
                onPress={onCompareToggle}
                style={[
                  styles.compareButton,
                  isInComparison
                    ? styles.compareButtonActive
                    : styles.compareButtonInactive,
                ]}
              >
                <Ionicons
                  name="analytics"
                  size={18}
                  color={isInComparison ? "white" : "#6b7280"}
                />
              </TouchableOpacity>
            </View>

            {/* Specs */}
            <View style={styles.specsContainer}>
              <View style={styles.specItem}>
                <Ionicons name="speedometer" size={14} color="#6b7280" />
                <Text style={styles.specText}>
                  {formatRange(model.range_epa_miles)}
                </Text>
              </View>
              {model.efficiency_mpge && (
                <View style={styles.specItem}>
                  <Ionicons name="leaf" size={14} color="#6b7280" />
                  <Text style={styles.specText}>
                    {model.efficiency_mpge} MPGe
                  </Text>
                </View>
              )}
              {model.acceleration_0_60_mph && (
                <View style={styles.specItem}>
                  <Ionicons name="flash" size={14} color="#6b7280" />
                  <Text style={styles.specText}>
                    {model.acceleration_0_60_mph}s
                  </Text>
                </View>
              )}
            </View>

            {/* Efficiency Rating */}
            {efficiencyRating && (
              <View style={styles.efficiencyContainer}>
                <Text style={[styles.efficiencyText, efficiencyRating.color]}>
                  {efficiencyRating.rating} Efficiency
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  // Grid view
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.gridContainer, { width: cardWidth }]}
    >
      {/* Image */}
      <View style={styles.gridImageContainer}>
        {imageSource ? (
          <Image
            source={imageSource}
            style={styles.gridImage}
            resizeMode="cover"
          />
        ) : (
          <View style={styles.gridImagePlaceholder}>
            <Ionicons name="car" size={32} color="#9ca3af" />
          </View>
        )}

        {/* Badges */}
        {badges.length > 0 && (
          <View style={styles.gridBadgeContainer}>
            <View style={[styles.badge, badges[0].color]}>
              <Text style={styles.badgeText}>{badges[0].text}</Text>
            </View>
          </View>
        )}

        {/* Compare Button */}
        <TouchableOpacity
          onPress={onCompareToggle}
          style={[
            styles.gridCompareButton,
            isInComparison
              ? styles.compareButtonActive
              : styles.gridCompareButtonInactive,
          ]}
        >
          <Ionicons
            name="analytics"
            size={16}
            color={isInComparison ? "white" : "#6b7280"}
          />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.gridContentContainer}>
        <Text style={styles.modelName} numberOfLines={1}>
          {model.make} {model.model}
        </Text>
        {model.trim && (
          <Text style={styles.modelTrim} numberOfLines={1}>
            {model.trim}
          </Text>
        )}

        <Text style={styles.modelPrice}>{formatPrice(model.price_msrp)}</Text>

        {/* Key Specs */}
        <View style={styles.gridSpecsContainer}>
          <View style={styles.gridSpecItem}>
            <View style={styles.specItem}>
              <Ionicons name="speedometer" size={12} color="#6b7280" />
              <Text style={styles.gridSpecText}>Range</Text>
            </View>
            <Text style={styles.gridSpecValue}>
              {formatRange(model.range_epa_miles)}
            </Text>
          </View>

          {model.efficiency_mpge && (
            <View style={styles.gridSpecItem}>
              <View style={styles.specItem}>
                <Ionicons name="leaf" size={12} color="#6b7280" />
                <Text style={styles.gridSpecText}>Efficiency</Text>
              </View>
              <Text style={styles.gridSpecValue}>
                {model.efficiency_mpge} MPGe
              </Text>
            </View>
          )}

          {model.acceleration_0_60_mph && (
            <View style={styles.gridSpecItem}>
              <View style={styles.specItem}>
                <Ionicons name="flash" size={12} color="#6b7280" />
                <Text style={styles.gridSpecText}>0-60 mph</Text>
              </View>
              <Text style={styles.gridSpecValue}>
                {model.acceleration_0_60_mph}s
              </Text>
            </View>
          )}
        </View>

        {/* Efficiency Rating */}
        {efficiencyRating && (
          <View style={styles.gridEfficiencyContainer}>
            <Text style={[styles.efficiencyText, efficiencyRating.color]}>
              {efficiencyRating.rating} Efficiency
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  featuredBadge: { backgroundColor: "#4f46e5" },
  editorChoiceBadge: { backgroundColor: "#2563eb" },
  bestValueBadge: { backgroundColor: "#16a34a" },
  excellentText: { color: "#16a34a" },
  veryGoodText: { color: "#2563eb" },
  goodText: { color: "#ca8a04" },
  averageText: { color: "#4b5563" },
  listContainer: {
    backgroundColor: "white",
    marginHorizontal: 16,
    marginBottom: 12,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    borderWidth: 1,
    borderColor: "#f3f4f6",
  },
  listContent: {
    flexDirection: "row",
    padding: 16,
  },
  listImageContainer: {
    position: "relative",
  },
  listImage: {
    width: 96,
    height: 64,
    borderRadius: 8,
  },
  listImagePlaceholder: {
    width: 96,
    height: 64,
    borderRadius: 8,
    backgroundColor: "#e5e7eb",
    alignItems: "center",
    justifyContent: "center",
  },
  listBadgeContainer: {
    position: "absolute",
    top: -4,
    right: -4,
  },
  badge: {
    borderRadius: 9999,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  badgeText: {
    color: "white",
    fontSize: 10,
    fontWeight: "500",
  },
  listTextContainer: {
    flex: 1,
    marginLeft: 16,
  },
  listHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  modelName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
  },
  modelTrim: {
    fontSize: 14,
    color: "#4b5563",
  },
  modelPrice: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#4f46e5",
    marginTop: 4,
  },
  compareButton: {
    padding: 8,
    borderRadius: 8,
  },
  compareButtonActive: {
    backgroundColor: "#4f46e5",
  },
  compareButtonInactive: {
    backgroundColor: "#f3f4f6",
  },
  specsContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12,
    flexWrap: "wrap",
  },
  specItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  specText: {
    fontSize: 14,
    color: "#4b5563",
    marginLeft: 4,
  },
  efficiencyContainer: {
    marginTop: 8,
  },
  efficiencyText: {
    fontSize: 12,
    fontWeight: "500",
  },
  gridContainer: {
    backgroundColor: "white",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    borderWidth: 1,
    borderColor: "#f3f4f6",
    marginBottom: 12,
  },
  gridImageContainer: {
    position: "relative",
  },
  gridImage: {
    width: "100%",
    height: 128,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  gridImagePlaceholder: {
    width: "100%",
    height: 128,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: "#e5e7eb",
    alignItems: "center",
    justifyContent: "center",
  },
  gridBadgeContainer: {
    position: "absolute",
    top: 8,
    left: 8,
  },
  gridCompareButton: {
    position: "absolute",
    top: 8,
    right: 8,
    padding: 8,
    borderRadius: 8,
  },
  gridCompareButtonInactive: {
    backgroundColor: "rgba(255, 255, 255, 0.9)",
  },
  gridContentContainer: {
    padding: 12,
  },
  gridSpecsContainer: {
    marginTop: 12,
  },
  gridSpecItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  gridSpecText: {
    fontSize: 12,
    color: "#4b5563",
    marginLeft: 4,
  },
  gridSpecValue: {
    fontSize: 12,
    fontWeight: "500",
    color: "#111827",
  },
  gridEfficiencyContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "#f3f4f6",
  },
});
