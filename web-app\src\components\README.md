# Header Component

A reusable header component for the GreenMilesEV application that provides consistent navigation across all pages.

## Features

- **Responsive Design**: Works on desktop and mobile devices
- **Authentication Aware**: Shows different content based on user authentication status
- **Two Variants**: Default for public pages, dashboard for authenticated pages
- **Mobile Menu**: Collapsible navigation for mobile devices
- **Active Link Highlighting**: Shows current page in navigation
- **Consistent Branding**: GreenMilesEV logo and styling

## Usage

### Basic Usage (Public Pages)

```tsx
import { Header } from '@/components/Header'

export default function PublicPage() {
  return (
    <div>
      <Header />
      {/* Your page content */}
    </div>
  )
}
```

### Dashboard Variant (Authenticated Pages)

```tsx
import { Header } from '@/components/Header'

export default function DashboardPage() {
  return (
    <div>
      <Header variant="dashboard" />
      {/* Your page content */}
    </div>
  )
}
```

## Props

| Prop      | Type                       | Default     | Description           |
| --------- | -------------------------- | ----------- | --------------------- |
| `variant` | `'default' \| 'dashboard'` | `'default'` | Header variant to use |

## Variants

### Default Variant

- Used for public pages (home, landing pages)
- Shows public navigation links (Home, Features, About, Contact)
- Shows Sign In / Get Started buttons for unauthenticated users
- Shows Dashboard / Sign Out buttons for authenticated users

### Dashboard Variant

- Used for authenticated pages (dashboard, vehicles, charging, etc.)
- Shows authenticated navigation links (Dashboard, My Vehicles, Charging, Analytics)
- Shows user welcome message and profile actions
- Includes settings and sign out buttons

## Navigation Links

### Public Navigation (Default Variant)

- Home (/)
- Features (/#features)
- About (/#about)
- Contact (/#contact)

### Authenticated Navigation (Dashboard Variant)

- Dashboard (/dashboard)
- My Vehicles (/vehicles)
- Charging (/charging)
- Analytics (/analytics)

## Mobile Responsiveness

The header automatically adapts to mobile devices:

- Navigation links are hidden on mobile and replaced with a hamburger menu
- Mobile menu slides down when activated
- Clicking a link automatically closes the mobile menu
- Touch-friendly button sizes and spacing

## Authentication Integration

The header integrates with the `useAuth` hook to:

- Show/hide appropriate navigation items
- Display user information
- Handle sign out functionality
- Redirect to appropriate pages after authentication

## Styling

The header uses Tailwind CSS classes and follows the application's design system:

- Electric blue color scheme (`electric-600`, `electric-700`)
- Consistent spacing and typography
- Backdrop blur effect for modern appearance
- Smooth transitions and hover effects

## Examples

### Adding a New Page with Header

1. Create your page component:

```tsx
'use client'

import { Header } from '@/components/Header'
import { ProtectedRoute } from '@/components/ProtectedRoute'

function MyPageContent() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header variant="dashboard" />
      <main className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">{/* Your page content */}</main>
    </div>
  )
}

export default function MyPage() {
  return (
    <ProtectedRoute>
      <MyPageContent />
    </ProtectedRoute>
  )
}
```

2. Add the route to the navigation links in `Header.tsx` if needed.

### Customizing Navigation Links

To add or modify navigation links, edit the `authenticatedNavLinks` or `publicNavLinks` arrays in the Header component:

```tsx
const authenticatedNavLinks = [
  { href: '/dashboard', label: 'Dashboard' },
  { href: '/vehicles', label: 'My Vehicles' },
  { href: '/charging', label: 'Charging' },
  { href: '/analytics', label: 'Analytics' },
  { href: '/new-page', label: 'New Page' }, // Add new link here
]
```

## Footer Component

A companion `Footer` component is also available for consistent page footers:

```tsx
import { Footer } from '@/components/Footer'

export default function MyPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">{/* Your content */}</main>
      <Footer />
    </div>
  )
}
```

## Dark Mode Support

The application includes comprehensive dark mode support using `next-themes`. All components automatically adapt to the user's theme preference.

### Theme Toggle

The `ThemeToggle` component is included in the Header and allows users to switch between light and dark modes:

- **Light Mode**: Shows a moon icon
- **Dark Mode**: Shows a sun icon
- **System Mode**: Automatically follows the user's system preference

### Theme Provider

The app is wrapped with `ThemeProvider` in the root layout:

```tsx
<ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
  {/* App content */}
</ThemeProvider>
```

### Dark Mode Classes

All components use Tailwind's dark mode classes:

```tsx
// Example dark mode styling
<div className="bg-white text-gray-900 dark:bg-gray-900 dark:text-white">
  <h1 className="text-gray-900 dark:text-white">Title</h1>
  <p className="text-gray-600 dark:text-gray-300">Description</p>
</div>
```

### Supported Components

All major components support dark mode:

- **Header**: Dark backgrounds, text colors, and borders
- **Footer**: Consistent dark styling
- **Cards**: Automatic background and text color adaptation
- **Buttons**: Proper contrast in both themes
- **Navigation**: Active states work in both themes

### Adding Dark Mode to New Components

When creating new components, follow these patterns:

1. **Background Colors**:

   ```tsx
   className = 'bg-white dark:bg-gray-900'
   ```

2. **Text Colors**:

   ```tsx
   className = 'text-gray-900 dark:text-white' // Primary text
   className = 'text-gray-600 dark:text-gray-300' // Secondary text
   ```

3. **Borders**:

   ```tsx
   className = 'border-gray-200 dark:border-gray-800'
   ```

4. **Cards and Containers**:
   ```tsx
   className = 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
   ```

## Toast Notifications

The application includes a comprehensive toast notification system using `react-hot-toast` with custom styling.

### Toast Provider

The `ToastProvider` is included in the root layout and provides consistent styling across the app.

### Custom Toast Utility

Use the custom `showToast` utility for consistent notifications:

```tsx
import { showToast } from '@/lib/toast'

// Basic notifications
showToast.success('Operation completed successfully!')
showToast.error('Something went wrong')
showToast.loading('Processing...')
showToast.info("Here's some information")
showToast.warning('Please be careful')

// Electric theme variants (recommended for our app)
showToast.electric.success('Profile updated!')
showToast.electric.error('Failed to save changes')
showToast.electric.loading('Uploading file...')
```

### Loading Toast Pattern

For async operations, use the loading toast pattern:

```tsx
const handleSave = async () => {
  const loadingToast = showToast.electric.loading('Saving...')

  try {
    await saveData()
    showToast.electric.success('Saved successfully!', {
      id: loadingToast, // Replaces the loading toast
    })
  } catch (error) {
    showToast.electric.error('Failed to save', {
      id: loadingToast, // Replaces the loading toast
    })
  }
}
```

### Toast Features

- **Consistent Styling**: Matches app theme and design system
- **Dark Mode Support**: Automatically adapts to theme
- **Electric Theme**: Special variants with app branding colors
- **Loading States**: Seamless loading → success/error transitions
- **Auto Dismiss**: Configurable duration for different toast types
- **Position**: Top-right positioning for non-intrusive notifications
