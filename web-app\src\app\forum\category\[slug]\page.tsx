'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  MessageSquare,
  Plus,
  Pin,
  Lock,
  Clock,
  Eye,
  TrendingUp,
  Filter,
  Search,
} from 'lucide-react'
import Link from 'next/link'

interface CategoryTopic {
  id: string
  title: string
  slug: string
  content: string
  author: {
    id: string
    full_name: string
    avatar_url?: string
  }
  created_at: string
  updated_at: string
  view_count: number
  post_count: number
  like_count: number
  is_pinned: boolean
  is_locked: boolean
  is_approved: boolean
  tags?: string[]
  last_activity_at?: string
  last_post_author?: {
    id: string
    full_name: string
  }
}

interface ForumCategory {
  id: string
  name: string
  description: string
  slug: string
  icon?: string
  color?: string
  topic_count: number
  post_count: number
  subcategories?: ForumCategory[]
}

interface CategoryPageData {
  category: ForumCategory
  topics: CategoryTopic[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}

export default function CategoryPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const [data, setData] = useState<CategoryPageData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'most_active' | 'most_popular'>(
    'newest'
  )
  const [showPinnedOnly, setShowPinnedOnly] = useState(false)

  const slug = params.slug as string

  useEffect(() => {
    fetchCategoryData()
  }, [slug, sortBy, showPinnedOnly, searchQuery])

  const fetchCategoryData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Build query parameters
      const queryParams = new URLSearchParams({
        category_slug: slug,
        sort: sortBy,
        limit: '20',
        page: '1',
      })

      if (searchQuery) {
        queryParams.append('search', searchQuery)
      }

      if (showPinnedOnly) {
        queryParams.append('pinned_only', 'true')
      }

      // Fetch category details
      const categoryResponse = await fetch(`/api/forum/categories?slug=${slug}`)
      const categoryData = await categoryResponse.json()

      if (!categoryResponse.ok) {
        throw new Error(categoryData.error || 'Failed to fetch category')
      }

      if (!categoryData.data || categoryData.data.length === 0) {
        throw new Error('Category not found')
      }

      const category = categoryData.data[0]

      // Fetch topics for this category
      const topicsResponse = await fetch(`/api/forum/topics?${queryParams.toString()}`)
      const topicsData = await topicsResponse.json()

      if (!topicsResponse.ok) {
        throw new Error(topicsData.error || 'Failed to fetch topics')
      }

      setData({
        category,
        topics: topicsData.data || [],
        pagination: topicsData.pagination || {
          total: 0,
          page: 1,
          limit: 20,
          totalPages: 0,
        },
      })
    } catch (err) {
      console.error('Error fetching category data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load category')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    return date.toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            <div className="animate-pulse space-y-6">
              <div className="h-8 w-3/4 rounded bg-gray-200"></div>
              <div className="h-4 w-1/2 rounded bg-gray-200"></div>
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-20 rounded bg-gray-200"></div>
                ))}
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            <Alert className="border-red-200 bg-red-50">
              <AlertDescription className="text-red-600">
                {error || 'Category not found'}
              </AlertDescription>
            </Alert>
            <div className="mt-4">
              <Button onClick={() => router.back()} variant="outline">
                Go Back
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  const { category, topics, pagination } = data

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        <div className="container mx-auto px-4 py-8">
          {/* Breadcrumb */}
          <nav className="mb-6 text-sm text-gray-600">
            <Link href="/forum" className="hover:text-blue-600">
              Forum
            </Link>
            <span className="mx-2">›</span>
            <span className="text-gray-900">{category.name}</span>
          </nav>

          {/* Category Header */}
          <div className="mb-8">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                {category.icon && (
                  <div
                    className="rounded-lg p-3 text-3xl"
                    style={{
                      backgroundColor: category.color ? `${category.color}20` : '#f3f4f6',
                      color: category.color || '#6b7280',
                    }}
                  >
                    {category.icon}
                  </div>
                )}
                <div>
                  <h1 className="text-3xl font-bold">{category.name}</h1>
                  {category.description && (
                    <p className="mt-2 text-gray-600">{category.description}</p>
                  )}
                  <div className="mt-3 flex items-center space-x-4 text-sm text-gray-600">
                    <span>{category.topic_count} topics</span>
                    <span>•</span>
                    <span>{category.post_count} posts</span>
                  </div>
                </div>
              </div>

              {user && (
                <Button asChild>
                  <Link href={`/forum/new-topic?category=${category.slug}`}>
                    <Plus className="mr-2 h-4 w-4" />
                    New Topic
                  </Link>
                </Button>
              )}
            </div>

            {/* Subcategories */}
            {category.subcategories && category.subcategories.length > 0 && (
              <div className="mt-6">
                <h3 className="mb-3 text-sm font-medium text-gray-700">Subcategories</h3>
                <div className="flex flex-wrap gap-2">
                  {category.subcategories.map((sub) => (
                    <Link
                      key={sub.id}
                      href={`/forum/category/${sub.slug}`}
                      className="flex items-center space-x-2 rounded-lg border border-gray-200 px-3 py-2 text-sm transition-colors hover:border-gray-300 hover:bg-gray-50"
                    >
                      {sub.icon && <span>{sub.icon}</span>}
                      <span>{sub.name}</span>
                      <Badge variant="secondary" className="text-xs">
                        {sub.topic_count}
                      </Badge>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Controls */}
          <div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            {/* Search */}
            <div className="flex flex-1 items-center space-x-2 md:max-w-md">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search topics..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Sort and Filter Controls */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-400" />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="rounded border border-gray-300 bg-white px-3 py-1 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="most_active">Most Active</option>
                  <option value="most_popular">Most Popular</option>
                </select>
              </div>

              <Button
                variant={showPinnedOnly ? 'default' : 'outline'}
                size="sm"
                onClick={() => setShowPinnedOnly(!showPinnedOnly)}
              >
                <Pin className="mr-1 h-3 w-3" />
                Pinned Only
              </Button>
            </div>
          </div>

          {/* Topics List */}
          <div className="space-y-4">
            {topics.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <MessageSquare className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                  <h3 className="mb-2 text-lg font-medium">No topics found</h3>
                  <p className="mb-4 text-gray-600">
                    {searchQuery
                      ? 'No topics match your search criteria.'
                      : 'Be the first to start a discussion in this category!'}
                  </p>
                  {user && (
                    <Button asChild>
                      <Link href={`/forum/new-topic?category=${category.slug}`}>
                        <Plus className="mr-2 h-4 w-4" />
                        Create First Topic
                      </Link>
                    </Button>
                  )}
                </CardContent>
              </Card>
            ) : (
              topics.map((topic) => (
                <Card key={topic.id} className="transition-shadow hover:shadow-md">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      {/* Author Avatar */}
                      {topic.author.avatar_url ? (
                        <img
                          src={topic.author.avatar_url}
                          alt={topic.author.full_name}
                          className="h-10 w-10 rounded-full"
                        />
                      ) : (
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-300">
                          {topic.author.full_name[0]?.toUpperCase()}
                        </div>
                      )}

                      <div className="flex-1">
                        {/* Topic Title and Status */}
                        <div className="mb-2 flex items-start justify-between">
                          <div className="flex-1">
                            <div className="mb-1 flex items-center space-x-2">
                              {topic.is_pinned && <Pin className="h-4 w-4 text-orange-500" />}
                              {topic.is_locked && <Lock className="h-4 w-4 text-red-500" />}
                              <Link
                                href={`/forum/topic/${topic.slug}`}
                                className="text-lg font-semibold text-gray-900 transition-colors hover:text-blue-600"
                              >
                                {topic.title}
                              </Link>
                            </div>
                            <p className="line-clamp-2 text-sm text-gray-600">{topic.content}</p>
                          </div>
                        </div>

                        {/* Topic Metadata */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <span>by {topic.author.full_name}</span>
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>{formatDate(topic.created_at)}</span>
                            </div>
                          </div>

                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <div className="flex items-center space-x-1">
                              <MessageSquare className="h-4 w-4" />
                              <span>{topic.post_count}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Eye className="h-4 w-4" />
                              <span>{topic.view_count}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <TrendingUp className="h-4 w-4" />
                              <span>{topic.like_count}</span>
                            </div>
                          </div>
                        </div>

                        {/* Tags */}
                        {topic.tags && topic.tags.length > 0 && (
                          <div className="mt-3 flex flex-wrap gap-1">
                            {topic.tags.slice(0, 3).map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {topic.tags.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{topic.tags.length - 3} more
                              </Badge>
                            )}
                          </div>
                        )}

                        {/* Last Activity */}
                        {topic.last_activity_at && (
                          <div className="mt-2 text-xs text-gray-500">
                            Last activity: {formatDate(topic.last_activity_at)}
                            {topic.last_post_author && (
                              <span> by {topic.last_post_author.full_name}</span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Results Summary */}
          <div className="mt-8 text-center text-sm text-gray-600">
            Showing {topics.length} of {pagination.total} topics
            {searchQuery && <span> for "{searchQuery}"</span>}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
