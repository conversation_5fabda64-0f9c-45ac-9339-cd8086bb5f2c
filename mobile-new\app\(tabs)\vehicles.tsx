import React from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";

export default function VehiclesScreen() {
  return (
    <ScrollView style={styles.container}>
      {/* Current Vehicle */}
      <View style={styles.card}>
        <View style={styles.cardHeader}>
          <Text style={styles.vehicleName}>My Tesla Model 3</Text>
          <TouchableOpacity>
            <Ionicons name="settings-outline" size={24} color="#6b7280" />
          </TouchableOpacity>
        </View>

        <View style={styles.batteryInfoContainer}>
          <View style={styles.batteryDetails}>
            <View>
              <Text style={styles.batteryPercentage}>85%</Text>
              <Text style={styles.batteryLabel}>Battery Level</Text>
            </View>
            <View style={styles.rangeDetails}>
              <Text style={styles.rangeText}>247 miles</Text>
              <Text style={styles.rangeLabel}>Estimated Range</Text>
            </View>
          </View>
        </View>

        <View style={styles.vehicleStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>2021</Text>
            <Text style={styles.statLabel}>Year</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>75 kWh</Text>
            <Text style={styles.statLabel}>Battery</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>358 mi</Text>
            <Text style={styles.statLabel}>Max Range</Text>
          </View>
        </View>
      </View>

      {/* Vehicle Actions */}
      <View style={styles.actionsContainer}>
        <Text style={styles.sectionTitle}>Vehicle Controls</Text>
        <View style={styles.actionsCard}>
          <TouchableOpacity style={styles.actionItem}>
            <View style={styles.actionContent}>
              <Ionicons name="thermometer-outline" size={24} color="#22c55e" />
              <View style={styles.actionTextContainer}>
                <Text style={styles.actionTitle}>Climate Control</Text>
                <Text style={styles.actionSubtitle}>
                  Pre-condition your vehicle
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionItem}>
            <View style={styles.actionContent}>
              <Ionicons name="lock-closed-outline" size={24} color="#22c55e" />
              <View style={styles.actionTextContainer}>
                <Text style={styles.actionTitle}>Lock/Unlock</Text>
                <Text style={styles.actionSubtitle}>Remote vehicle access</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomWidth: 0 }]}
          >
            <View style={styles.actionContent}>
              <Ionicons name="flash-outline" size={24} color="#22c55e" />
              <View style={styles.actionTextContainer}>
                <Text style={styles.actionTitle}>Start Charging</Text>
                <Text style={styles.actionSubtitle}>
                  Begin charging session
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* Maintenance */}
      <View style={styles.maintenanceContainer}>
        <Text style={styles.sectionTitle}>Maintenance</Text>
        <View style={styles.maintenanceCard}>
          <View style={styles.maintenanceHeader}>
            <Text style={styles.maintenanceTitle}>Next Service</Text>
            <Text style={styles.maintenanceValue}>In 2,500 miles</Text>
          </View>
          <View style={styles.progressBarBackground}>
            <View style={[styles.progressBarFill, { width: "75%" }]} />
          </View>
          <Text style={styles.maintenanceSubtitle}>
            Tire rotation and inspection
          </Text>
        </View>
      </View>

      {/* Add Vehicle Button */}
      <View style={styles.addButtonContainer}>
        <TouchableOpacity style={styles.addButton}>
          <View style={styles.addButtonContent}>
            <Ionicons name="add" size={24} color="white" />
            <Text style={styles.addButtonText}>Add Another Vehicle</Text>
          </View>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f9fafb",
  },
  card: {
    backgroundColor: "white",
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    padding: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  vehicleName: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#111827",
  },
  batteryInfoContainer: {
    backgroundColor: "#eef2ff",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  batteryDetails: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  batteryPercentage: {
    fontSize: 30,
    fontWeight: "bold",
    color: "#4f46e5",
  },
  batteryLabel: {
    fontSize: 14,
    color: "#4b5563",
  },
  rangeDetails: {
    alignItems: "flex-end",
  },
  rangeText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
  },
  rangeLabel: {
    fontSize: 14,
    color: "#4b5563",
  },
  vehicleStats: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  statItem: {
    alignItems: "center",
  },
  statValue: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
  },
  statLabel: {
    fontSize: 14,
    color: "#4b5563",
  },
  actionsContainer: {
    marginHorizontal: 16,
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
    marginBottom: 12,
  },
  actionsCard: {
    backgroundColor: "white",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  actionItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  actionContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  actionTitle: {
    fontWeight: "500",
    color: "#111827",
  },
  actionSubtitle: {
    fontSize: 14,
    color: "#4b5563",
  },
  maintenanceContainer: {
    marginHorizontal: 16,
    marginTop: 24,
  },
  maintenanceCard: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  maintenanceHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  maintenanceTitle: {
    fontWeight: "500",
    color: "#111827",
  },
  maintenanceValue: {
    fontSize: 14,
    color: "#4f46e5",
  },
  progressBarBackground: {
    backgroundColor: "#e5e7eb",
    borderRadius: 9999,
    height: 8,
    marginBottom: 8,
  },
  progressBarFill: {
    backgroundColor: "#4f46e5",
    height: 8,
    borderRadius: 9999,
  },
  maintenanceSubtitle: {
    fontSize: 14,
    color: "#4b5563",
  },
  addButtonContainer: {
    marginHorizontal: 16,
    marginTop: 24,
    marginBottom: 24,
  },
  addButton: {
    backgroundColor: Colors.light.tint,
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
  },
  addButtonContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  addButtonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 8,
  },
});
