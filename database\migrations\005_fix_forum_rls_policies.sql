-- Migration: Fix Infinite Recursion in Forum RLS Policies
-- This fixes the circular reference issue in forum_moderators policies

-- Drop ALL existing policies on forum_moderators to start fresh
DROP POLICY IF EXISTS "Forum moderators can view their assignments" ON forum_moderators;
DROP POLICY IF EXISTS "Ad<PERSON> can view all moderator assignments" ON forum_moderators;
DROP POLICY IF EXISTS "Super admins can manage all moderators" ON forum_moderators;
DROP POLICY IF EXISTS "Admins can manage moderators in their categories" ON forum_moderators;
DROP POLICY IF EXISTS "Users can view their own moderator status" ON forum_moderators;
DROP POLICY IF EXISTS "Active moderators are publicly viewable" ON forum_moderators;
DROP POLICY IF EXISTS "Only super admins can manage moderators" ON forum_moderators;

-- First, update the profiles table to include a forum_role column if it doesn't exist
-- This will be used instead of checking forum_moderators table for role verification
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'forum_role') THEN
        ALTER TABLE profiles ADD COLUMN forum_role TEXT DEFAULT 'user' 
        CHECK (forum_role IN ('user', 'moderator', 'admin', 'super_admin'));
    END IF;
END $$;

-- Create simplified, non-recursive policies for forum_moderators
-- Allow users to view their own moderator status (without checking if they're a moderator)
CREATE POLICY "Users can view their own moderator status" ON forum_moderators
    FOR SELECT USING (user_id = auth.uid());

-- Allow viewing active moderators (for displaying moderator badges, etc.)
CREATE POLICY "Active moderators are publicly viewable" ON forum_moderators
    FOR SELECT USING (is_active = true);

-- Only allow inserts/updates/deletes by users with explicit super_admin role in profiles
-- This avoids the circular reference by checking a separate table
CREATE POLICY "Only super admins can manage moderators" ON forum_moderators
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND (forum_role = 'super_admin' OR forum_role = 'admin')
        )
    );

-- Drop existing functions first to avoid parameter conflicts
DROP FUNCTION IF EXISTS is_forum_admin(UUID);
DROP FUNCTION IF EXISTS is_forum_moderator(UUID);
DROP FUNCTION IF EXISTS is_forum_admin();
DROP FUNCTION IF EXISTS is_forum_moderator();

-- Create a function to check if a user is a forum admin/moderator
-- This avoids RLS recursion by using a security definer function
CREATE FUNCTION is_forum_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has admin/super_admin role in profiles
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = user_id 
        AND forum_role IN ('admin', 'super_admin')
    );
END;
$$;

-- Create a function to check if a user is a forum moderator
CREATE FUNCTION is_forum_moderator(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has any moderator role in profiles or is an active moderator
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = user_id 
        AND forum_role IN ('moderator', 'admin', 'super_admin')
    ) OR EXISTS (
        SELECT 1 FROM forum_moderators 
        WHERE forum_moderators.user_id = user_id 
        AND is_active = true
    );
END;
$$;

-- Grant execute permissions on the functions
-- Note: Functions with default parameters only create one signature
GRANT EXECUTE ON FUNCTION is_forum_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION is_forum_moderator(UUID) TO authenticated; 