import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ComparisonProvider } from '@/contexts/ComparisonContext'
import { DecisionMatrix } from '@/components/comparison/DecisionMatrix'
import type { EVModel } from '@/shared/types'

// Mock EV models for testing
const mockModels: EVModel[] = [
  {
    id: '1',
    make: 'Tesla',
    model: 'Model 3',
    year: 2024,
    trim: 'Long Range',
    body_type: 'sedan',
    price_msrp: 4799900,
    range_epa_miles: 358,
    efficiency_mpge: 132,
    acceleration_0_60_mph: 4.2,
    charging_speed_dc_kw: 250,
    battery_capacity_kwh: 75,
    seating_capacity: 5,
    production_status: 'current',
    images: ['tesla-model3.jpg'],
    is_featured: true,
    popularity_score: 95,
    editor_choice: true,
    best_value: false,
    manufacturer: {
      name: 'Tesla',
      logo_url: 'tesla-logo.png'
    }
  },
  {
    id: '2',
    make: 'BMW',
    model: 'iX',
    year: 2024,
    trim: 'xDrive50',
    body_type: 'suv',
    price_msrp: 8399900,
    range_epa_miles: 324,
    efficiency_mpge: 86,
    acceleration_0_60_mph: 4.6,
    charging_speed_dc_kw: 200,
    battery_capacity_kwh: 111.5,
    seating_capacity: 5,
    production_status: 'current',
    images: ['bmw-ix.jpg'],
    is_featured: true,
    popularity_score: 88,
    editor_choice: false,
    best_value: false,
    manufacturer: {
      name: 'BMW',
      logo_url: 'bmw-logo.png'
    }
  },
  {
    id: '3',
    make: 'Hyundai',
    model: 'IONIQ 5',
    year: 2024,
    trim: 'SEL',
    body_type: 'suv',
    price_msrp: 4199900,
    range_epa_miles: 303,
    efficiency_mpge: 114,
    acceleration_0_60_mph: 5.1,
    charging_speed_dc_kw: 235,
    battery_capacity_kwh: 77.4,
    seating_capacity: 5,
    production_status: 'current',
    images: ['hyundai-ioniq5.jpg'],
    is_featured: true,
    popularity_score: 92,
    editor_choice: false,
    best_value: true,
    manufacturer: {
      name: 'Hyundai',
      logo_url: 'hyundai-logo.png'
    }
  }
]

// Mock the comparison context with test data
jest.mock('@/contexts/ComparisonContext', () => ({
  ...jest.requireActual('@/contexts/ComparisonContext'),
  useComparison: () => ({
    comparisonList: mockModels,
    comparisonCount: mockModels.length,
    addToComparison: jest.fn(),
    removeFromComparison: jest.fn(),
    clearComparison: jest.fn(),
    isInComparison: jest.fn(),
    canAddMore: false
  })
}))

const renderDecisionMatrix = () => {
  return render(
    <ComparisonProvider>
      <DecisionMatrix />
    </ComparisonProvider>
  )
}

describe('DecisionMatrix', () => {
  beforeEach(() => {
    localStorage.clear()
  })

  describe('Priority Weight Management', () => {
    it('should render priority weight sliders', () => {
      renderDecisionMatrix()
      
      expect(screen.getByText('Price')).toBeInTheDocument()
      expect(screen.getByText('Range')).toBeInTheDocument()
      expect(screen.getByText('Efficiency')).toBeInTheDocument()
      expect(screen.getByText('Performance')).toBeInTheDocument()
      expect(screen.getByText('Charging Speed')).toBeInTheDocument()
    })

    it('should allow adjusting priority weights', async () => {
      renderDecisionMatrix()
      
      const priceSlider = screen.getByRole('slider', { name: /price/i })
      
      fireEvent.change(priceSlider, { target: { value: '80' } })
      
      await waitFor(() => {
        expect(priceSlider).toHaveValue('80')
      })
    })

    it('should normalize weights to 100%', async () => {
      renderDecisionMatrix()
      
      // Adjust multiple sliders
      const priceSlider = screen.getByRole('slider', { name: /price/i })
      const rangeSlider = screen.getByRole('slider', { name: /range/i })
      
      fireEvent.change(priceSlider, { target: { value: '60' } })
      fireEvent.change(rangeSlider, { target: { value: '40' } })
      
      // Check that weights are normalized (implementation detail)
      await waitFor(() => {
        expect(priceSlider).toHaveValue('60')
        expect(rangeSlider).toHaveValue('40')
      })
    })
  })

  describe('Model Scoring', () => {
    it('should display scores for each model', () => {
      renderDecisionMatrix()
      
      expect(screen.getByText('Tesla Model 3')).toBeInTheDocument()
      expect(screen.getByText('BMW iX')).toBeInTheDocument()
      expect(screen.getByText('Hyundai IONIQ 5')).toBeInTheDocument()
      
      // Should show scores (exact values depend on scoring algorithm)
      expect(screen.getAllByText(/\d+(\.\d+)?/)).toHaveLength(expect.any(Number))
    })

    it('should rank models by total score', () => {
      renderDecisionMatrix()
      
      const modelElements = screen.getAllByTestId(/model-score-/)
      expect(modelElements.length).toBe(3)
      
      // Models should be ordered by score (highest first)
      // This is a simplified test - actual implementation would verify order
    })

    it('should update scores when priorities change', async () => {
      renderDecisionMatrix()
      
      // Get initial scores
      const initialScores = screen.getAllByTestId(/total-score-/)
      const initialScore1 = initialScores[0].textContent
      
      // Change priority weights
      const priceSlider = screen.getByRole('slider', { name: /price/i })
      fireEvent.change(priceSlider, { target: { value: '90' } })
      
      // Scores should update
      await waitFor(() => {
        const updatedScores = screen.getAllByTestId(/total-score-/)
        expect(updatedScores[0].textContent).not.toBe(initialScore1)
      })
    })
  })

  describe('Category Scoring', () => {
    it('should show individual category scores', () => {
      renderDecisionMatrix()
      
      // Should display scores for each category
      expect(screen.getAllByTestId(/price-score-/)).toHaveLength(3)
      expect(screen.getAllByTestId(/range-score-/)).toHaveLength(3)
      expect(screen.getAllByTestId(/efficiency-score-/)).toHaveLength(3)
    })

    it('should highlight best performer in each category', () => {
      renderDecisionMatrix()
      
      // Best price performer should be highlighted (Hyundai IONIQ 5)
      const priceScores = screen.getAllByTestId(/price-score-/)
      const bestPriceScore = Math.max(...priceScores.map(el => parseFloat(el.textContent || '0')))
      
      expect(bestPriceScore).toBeGreaterThan(0)
    })
  })

  describe('Recommendation Engine', () => {
    it('should show top recommendation', () => {
      renderDecisionMatrix()
      
      expect(screen.getByText(/recommended/i)).toBeInTheDocument()
      expect(screen.getByText(/best match/i)).toBeInTheDocument()
    })

    it('should provide reasoning for recommendation', () => {
      renderDecisionMatrix()
      
      // Should show why this model is recommended
      expect(screen.getByText(/because/i) || screen.getByText(/due to/i) || screen.getByText(/offers/i)).toBeInTheDocument()
    })

    it('should update recommendation when priorities change', async () => {
      renderDecisionMatrix()
      
      const initialRecommendation = screen.getByTestId('top-recommendation').textContent
      
      // Dramatically change priorities to favor different model
      const performanceSlider = screen.getByRole('slider', { name: /performance/i })
      fireEvent.change(performanceSlider, { target: { value: '100' } })
      
      await waitFor(() => {
        const updatedRecommendation = screen.getByTestId('top-recommendation').textContent
        // Recommendation might change based on new priorities
        expect(updatedRecommendation).toBeDefined()
      })
    })
  })

  describe('Preset Profiles', () => {
    it('should provide preset buyer profiles', () => {
      renderDecisionMatrix()
      
      expect(screen.getByText(/commuter/i) || screen.getByText(/family/i) || screen.getByText(/performance/i)).toBeInTheDocument()
    })

    it('should apply preset weights when profile is selected', async () => {
      renderDecisionMatrix()
      
      // Find and click a preset profile
      const commuterProfile = screen.getByText(/commuter/i)
      fireEvent.click(commuterProfile)
      
      await waitFor(() => {
        // Efficiency should be weighted higher for commuter profile
        const efficiencySlider = screen.getByRole('slider', { name: /efficiency/i })
        expect(parseInt(efficiencySlider.value)).toBeGreaterThan(50)
      })
    })
  })

  describe('Data Persistence', () => {
    it('should save priority weights to localStorage', async () => {
      renderDecisionMatrix()
      
      const priceSlider = screen.getByRole('slider', { name: /price/i })
      fireEvent.change(priceSlider, { target: { value: '75' } })
      
      await waitFor(() => {
        const saved = localStorage.getItem('decision-matrix-weights')
        expect(saved).toBeTruthy()
        
        const weights = JSON.parse(saved!)
        expect(weights.price).toBe(75)
      })
    })

    it('should load saved weights on initialization', () => {
      // Pre-populate localStorage
      localStorage.setItem('decision-matrix-weights', JSON.stringify({
        price: 60,
        range: 40,
        efficiency: 30,
        performance: 20,
        charging: 10
      }))
      
      renderDecisionMatrix()
      
      const priceSlider = screen.getByRole('slider', { name: /price/i })
      expect(priceSlider).toHaveValue('60')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      renderDecisionMatrix()
      
      expect(screen.getByRole('slider', { name: /price/i })).toHaveAttribute('aria-label')
      expect(screen.getByRole('slider', { name: /range/i })).toHaveAttribute('aria-label')
    })

    it('should be keyboard navigable', () => {
      renderDecisionMatrix()
      
      const priceSlider = screen.getByRole('slider', { name: /price/i })
      priceSlider.focus()
      
      expect(document.activeElement).toBe(priceSlider)
    })
  })
})
