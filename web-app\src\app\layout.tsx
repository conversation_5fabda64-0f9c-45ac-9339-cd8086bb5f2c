import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '@/contexts/AuthContext'
import { AuthErrorBoundary } from '@/components/AuthErrorBoundary'
import { ThemeProvider } from '@/components/ThemeProvider'
import { ToastProvider } from '@/components/ToastProvider'
import { ComparisonProvider } from '@/contexts/ComparisonContext'
import { ComparisonPanel } from '@/components/comparison/ComparisonPanel'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'GreenMilesEV - Electric Vehicle Management',
  description:
    'Comprehensive platform for electric vehicle management, charging, and trip planning',
  keywords: ['electric vehicle', 'EV', 'charging', 'green energy', 'sustainability'],
  authors: [{ name: 'GreenMilesEV Team' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthErrorBoundary>
            <AuthProvider>
              <ComparisonProvider>
                <div className="min-h-screen bg-background">{children}</div>
                <ComparisonPanel />
                <ToastProvider />
              </ComparisonProvider>
            </AuthProvider>
          </AuthErrorBoundary>
        </ThemeProvider>
      </body>
    </html>
  )
}
