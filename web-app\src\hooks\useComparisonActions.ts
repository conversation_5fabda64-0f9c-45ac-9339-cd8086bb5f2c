'use client'

import { useCallback } from 'react'
import { useComparison } from '@/contexts/ComparisonContext'
import { toast } from 'sonner'
import type { EVModel } from '@/shared/types'

/**
 * Custom hook that provides comparison actions with user feedback
 */
export function useComparisonActions() {
  const {
    addModel,
    removeModel,
    clearAll,
    isModelInComparison,
    canAddMore,
    comparisonCount,
    state
  } = useComparison()

  const handleAddToComparison = useCallback((model: EVModel) => {
    if (isModelInComparison(model.id)) {
      toast.info(`${model.make} ${model.model} is already in comparison`)
      return false
    }

    if (!canAddMore) {
      toast.warning('Maximum 4 models can be compared at once')
      return false
    }

    addModel(model)
    toast.success(`Added ${model.make} ${model.model} to comparison`)
    return true
  }, [addModel, isModelInComparison, canAddMore])

  const handleRemoveFromComparison = useCallback((modelId: string) => {
    const model = state.models.find(m => m.id === modelId)
    removeModel(modelId)
    
    if (model) {
      toast.success(`Removed ${model.make} ${model.model} from comparison`)
    }
  }, [removeModel, state.models])

  const handleClearComparison = useCallback(() => {
    if (comparisonCount === 0) {
      return
    }

    clearAll()
    toast.success('Cleared all models from comparison')
  }, [clearAll, comparisonCount])

  const handleToggleComparison = useCallback((model: EVModel) => {
    if (isModelInComparison(model.id)) {
      handleRemoveFromComparison(model.id)
    } else {
      handleAddToComparison(model)
    }
  }, [isModelInComparison, handleAddToComparison, handleRemoveFromComparison])

  return {
    addToComparison: handleAddToComparison,
    removeFromComparison: handleRemoveFromComparison,
    clearComparison: handleClearComparison,
    toggleComparison: handleToggleComparison,
    isInComparison: isModelInComparison,
    canAddMore,
    comparisonCount,
    comparisonModels: state.models
  }
}
