import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { EVModel } from "../../types";

interface ComparisonSpecsProps {
  models: EVModel[];
}

interface SpecCategory {
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  specs: Array<{
    label: string;
    key: keyof EVModel;
    unit?: string;
    formatter?: (value: any) => string;
  }>;
}

export function ComparisonSpecs({ models }: ComparisonSpecsProps) {
  const [expandedCategories, setExpandedCategories] = useState<string[]>([
    "Performance",
  ]);

  const formatValue = (
    value: any,
    unit?: string,
    formatter?: (value: any) => string
  ) => {
    if (value === null || value === undefined) return "N/A";
    if (formatter) return formatter(value);
    if (typeof value === "number") {
      return unit
        ? `${value.toLocaleString()} ${unit}`
        : value.toLocaleString();
    }
    return unit ? `${value} ${unit}` : value;
  };

  const toggleCategory = (categoryTitle: string) => {
    setExpandedCategories((prev) =>
      prev.includes(categoryTitle)
        ? prev.filter((c) => c !== categoryTitle)
        : [...prev, categoryTitle]
    );
  };

  const getBestValue = (spec: any, models: EVModel[]) => {
    const values = models
      .map((m) => (m as any)[spec.key])
      .filter((v) => v !== null && v !== undefined);
    if (values.length === 0) return null;

    if (spec.key.includes("price") || spec.key.includes("acceleration")) {
      return Math.min(...values);
    }
    return Math.max(...values);
  };

  const specCategories: SpecCategory[] = [
    {
      title: "Performance",
      icon: "flash",
      specs: [
        { label: "0-60 mph", key: "acceleration_0_60_mph", unit: "seconds" },
        { label: "Top Speed", key: "top_speed_mph", unit: "mph" },
        { label: "Motor Power", key: "motor_power_hp", unit: "hp" },
        { label: "Motor Torque", key: "motor_torque_lb_ft", unit: "lb-ft" },
        {
          label: "Drivetrain",
          key: "drivetrain",
          formatter: (v) => v?.toUpperCase() || "N/A",
        },
      ],
    },
    {
      title: "Range & Efficiency",
      icon: "speedometer",
      specs: [
        { label: "EPA Range", key: "range_epa_miles", unit: "miles" },
        { label: "WLTP Range", key: "range_wltp_miles", unit: "miles" },
        {
          label: "Real-World Range",
          key: "range_real_world_miles",
          unit: "miles",
        },
        { label: "Efficiency (EPA)", key: "efficiency_mpge", unit: "MPGe" },
        { label: "Battery Capacity", key: "battery_capacity_kwh", unit: "kWh" },
      ],
    },
    {
      title: "Charging",
      icon: "flash-outline",
      specs: [
        { label: "DC Fast Charging", key: "charging_speed_dc_kw", unit: "kW" },
        { label: "AC Charging", key: "charging_speed_ac_kw", unit: "kW" },
        {
          label: "10-80% Time",
          key: "charging_time_10_80_minutes",
          unit: "minutes",
        },
      ],
    },
    {
      title: "Dimensions",
      icon: "resize",
      specs: [
        { label: "Length", key: "length_inches", unit: "inches" },
        { label: "Width", key: "width_inches", unit: "inches" },
        { label: "Height", key: "height_inches", unit: "inches" },
        { label: "Wheelbase", key: "wheelbase_inches", unit: "inches" },
        { label: "Curb Weight", key: "curb_weight_lbs", unit: "lbs" },
      ],
    },
    {
      title: "Interior",
      icon: "car",
      specs: [
        { label: "Seating Capacity", key: "seating_capacity", unit: "seats" },
        {
          label: "Cargo Volume",
          key: "cargo_volume_cubic_ft",
          unit: "cubic ft",
        },
      ],
    },
    {
      title: "Pricing",
      icon: "cash",
      specs: [
        {
          label: "MSRP",
          key: "price_msrp",
          formatter: (v) => (v ? `$${(v / 100).toLocaleString()}` : "N/A"),
        },
        {
          label: "Base Price",
          key: "price_base",
          formatter: (v) => (v ? `$${(v / 100).toLocaleString()}` : "N/A"),
        },
      ],
    },
  ];

  const SpecRow = ({ spec, models }: { spec: any; models: EVModel[] }) => {
    const bestValue = getBestValue(spec, models);
    const hasValidData = models.some(
      (m) => (m as any)[spec.key] !== null && (m as any)[spec.key] !== undefined
    );

    if (!hasValidData) return null;

    return (
      <View style={styles.specRowContainer}>
        <Text style={styles.specLabel}>{spec.label}</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={{ flexDirection: "row", gap: 12 }}>
            {models.map((model) => {
              const value = (model as any)[spec.key];
              const isBest =
                value === bestValue && value !== null && value !== undefined;

              return (
                <View
                  key={model.id}
                  style={[styles.specValueCard, isBest && styles.bestValueCard]}
                >
                  <Text style={styles.specModelName} numberOfLines={1}>
                    {model.make} {model.model}
                  </Text>
                  <Text
                    style={[styles.specValue, isBest && styles.bestSpecValue]}
                  >
                    {formatValue(value, spec.unit, spec.formatter)}
                  </Text>
                  {isBest && (
                    <View style={styles.bestValueBadge}>
                      <Ionicons name="trophy" size={12} color="#16a34a" />
                      <Text style={styles.bestValueText}>Best</Text>
                    </View>
                  )}
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  const CategorySection = ({ category }: { category: SpecCategory }) => {
    const isExpanded = expandedCategories.includes(category.title);
    const hasValidSpecs = category.specs.some((spec) =>
      models.some(
        (m) =>
          (m as any)[spec.key] !== null && (m as any)[spec.key] !== undefined
      )
    );

    if (!hasValidSpecs) return null;

    return (
      <View style={styles.categoryContainer}>
        <TouchableOpacity
          onPress={() => toggleCategory(category.title)}
          style={styles.categoryHeader}
        >
          <View style={styles.categoryTitleContainer}>
            <Ionicons name={category.icon} size={20} color="#6b7280" />
            <Text style={styles.categoryTitle}>{category.title}</Text>
          </View>
          <Ionicons
            name={isExpanded ? "chevron-up" : "chevron-down"}
            size={20}
            color="#6b7280"
          />
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.specsContainer}>
            {category.specs.map((spec, index) => (
              <SpecRow key={index} spec={spec} models={models} />
            ))}
          </View>
        )}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {specCategories.map((category, index) => (
        <CategorySection key={index} category={category} />
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 16,
  },
  categoryContainer: {
    backgroundColor: "white",
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    overflow: "hidden",
  },
  categoryHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    backgroundColor: "#f9fafb",
  },
  categoryTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#111827",
    marginLeft: 12,
  },
  specsContainer: {
    padding: 16,
  },
  specRowContainer: {
    marginBottom: 16,
  },
  specLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#374151",
    marginBottom: 8,
  },
  specValueCard: {
    width: 128,
    borderRadius: 8,
    padding: 12,
    backgroundColor: "#f9fafb",
  },
  bestValueCard: {
    backgroundColor: "#f0fdf4",
    borderColor: "#bbf7d0",
    borderWidth: 1,
  },
  specModelName: {
    fontSize: 12,
    color: "#6b7280",
    marginBottom: 4,
  },
  specValue: {
    fontWeight: "600",
    color: "#111827",
  },
  bestSpecValue: {
    color: "#15803d",
  },
  bestValueBadge: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
  },
  bestValueText: {
    fontSize: 12,
    color: "#16a34a",
    marginLeft: 4,
  },
});
